{"version": 3, "sources": ["../../d3-scale/src/init.js", "../../d3-scale/src/ordinal.js", "../../d3-scale/src/band.js", "../../d3-scale/src/constant.js", "../../d3-scale/src/number.js", "../../d3-scale/src/continuous.js", "../../d3-format/src/formatDecimal.js", "../../d3-format/src/exponent.js", "../../d3-format/src/formatGroup.js", "../../d3-format/src/formatNumerals.js", "../../d3-format/src/formatSpecifier.js", "../../d3-format/src/formatTrim.js", "../../d3-format/src/formatPrefixAuto.js", "../../d3-format/src/formatRounded.js", "../../d3-format/src/formatTypes.js", "../../d3-format/src/identity.js", "../../d3-format/src/locale.js", "../../d3-format/src/defaultLocale.js", "../../d3-format/src/precisionFixed.js", "../../d3-format/src/precisionPrefix.js", "../../d3-format/src/precisionRound.js", "../../d3-scale/src/tickFormat.js", "../../d3-scale/src/linear.js", "../../d3-scale/src/identity.js", "../../d3-scale/src/nice.js", "../../d3-scale/src/log.js", "../../d3-scale/src/symlog.js", "../../d3-scale/src/pow.js", "../../d3-scale/src/radial.js", "../../d3-scale/src/quantile.js", "../../d3-scale/src/quantize.js", "../../d3-scale/src/threshold.js", "../../d3-time/src/interval.js", "../../d3-time/src/millisecond.js", "../../d3-time/src/duration.js", "../../d3-time/src/second.js", "../../d3-time/src/minute.js", "../../d3-time/src/hour.js", "../../d3-time/src/day.js", "../../d3-time/src/week.js", "../../d3-time/src/month.js", "../../d3-time/src/year.js", "../../d3-time/src/ticks.js", "../../d3-time-format/src/locale.js", "../../d3-time-format/src/defaultLocale.js", "../../d3-time-format/src/isoFormat.js", "../../d3-time-format/src/isoParse.js", "../../d3-scale/src/time.js", "../../d3-scale/src/utcTime.js", "../../d3-scale/src/sequential.js", "../../d3-scale/src/sequentialQuantile.js", "../../d3-scale/src/diverging.js"], "sourcesContent": ["export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n", "import {InternMap} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport const implicit = Symbol(\"implicit\");\n\nexport default function ordinal() {\n  var index = new InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n", "import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n", "export default function constants(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function number(x) {\n  return +x;\n}\n", "import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous() {\n  return transformer()(identity, identity);\n}\n", "export default function(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21\n      ? x.toLocaleString(\"en\").replace(/,/g, \"\")\n      : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i, coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [\n    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n    +x.slice(i + 1)\n  ];\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}\n", "export default function(grouping, thousands) {\n  return function(value, width) {\n    var i = value.length,\n        t = [],\n        j = 0,\n        g = grouping[0],\n        length = 0;\n\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n\n    return t.reverse().join(thousands);\n  };\n}\n", "export default function(numerals) {\n  return function(value) {\n    return value.replace(/[0-9]/g, function(i) {\n      return numerals[+i];\n    });\n  };\n}\n", "// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function() {\n  return this.fill\n      + this.align\n      + this.sign\n      + this.symbol\n      + (this.zero ? \"0\" : \"\")\n      + (this.width === undefined ? \"\" : Math.max(1, this.width | 0))\n      + (this.comma ? \",\" : \"\")\n      + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0))\n      + (this.trim ? \"~\" : \"\")\n      + this.type;\n};\n", "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\": i0 = i1 = i; break;\n      case \"0\": if (i0 === 0) i0 = i; i1 = i; break;\n      default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport var prefixExponent;\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient\n      : i > n ? coefficient + new Array(i - n + 1).join(\"0\")\n      : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i)\n      : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient\n      : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1)\n      : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n", "import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\n\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": (x) => Math.round(x).toString(2),\n  \"c\": (x) => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": (x) => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": (x) => Math.round(x).toString(16).toUpperCase(),\n  \"x\": (x) => Math.round(x).toString(16)\n};\n", "export default function(x) {\n  return x;\n}\n", "import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport {prefixExponent} from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\n\nvar map = Array.prototype.map,\n    prefixes = [\"y\",\"z\",\"a\",\"f\",\"p\",\"n\",\"µ\",\"m\",\"\",\"k\",\"M\",\"G\",\"T\",\"P\",\"E\",\"Z\",\"Y\"];\n\nexport default function(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n      currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n      currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n      decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n      numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n      percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n      minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n      nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n\n    var fill = specifier.fill,\n        align = specifier.align,\n        sign = specifier.sign,\n        symbol = specifier.symbol,\n        zero = specifier.zero,\n        width = specifier.width,\n        comma = specifier.comma,\n        precision = specifier.precision,\n        trim = specifier.trim,\n        type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || (fill === \"0\" && align === \"=\")) zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n        suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n        maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6\n        : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))\n        : Math.max(0, Math.min(20, precision));\n\n    function format(value) {\n      var valuePrefix = prefix,\n          valueSuffix = suffix,\n          i, n, c;\n\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? (sign === \"(\" ? sign : minus) : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n          padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\": value = valuePrefix + value + valueSuffix + padding; break;\n        case \"=\": value = valuePrefix + padding + value + valueSuffix; break;\n        case \"^\": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;\n        default: value = padding + valuePrefix + value + valueSuffix; break;\n      }\n\n      return numerals(value);\n    }\n\n    format.toString = function() {\n      return specifier + \"\";\n    };\n\n    return format;\n  }\n\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n        e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n        k = Math.pow(10, -e),\n        prefix = prefixes[8 + e / 3];\n    return function(value) {\n      return f(k * value) + prefix;\n    };\n  }\n\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var format;\nexport var formatPrefix;\n\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step) {\n  return Math.max(0, -exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n", "import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n", "import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous();\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import {linearish} from \"./linear.js\";\nimport number from \"./number.js\";\n\nexport default function identity(domain) {\n  var unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n\n  scale.invert = scale;\n\n  scale.domain = scale.range = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), scale) : domain.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return identity(domain).unknown(unknown);\n  };\n\n  domain = arguments.length ? Array.from(domain, number) : [0, 1];\n\n  return linearish(scale);\n}\n", "export default function nice(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n", "import {ticks} from \"d3-array\";\nimport {format, formatSpecifier} from \"d3-format\";\nimport nice from \"./nice.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformLog(x) {\n  return Math.log(x);\n}\n\nfunction transformExp(x) {\n  return Math.exp(x);\n}\n\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\n\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\n\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\n\nfunction powp(base) {\n  return base === 10 ? pow10\n      : base === Math.E ? Math.exp\n      : x => Math.pow(base, x);\n}\n\nfunction logp(base) {\n  return base === Math.E ? Math.log\n      : base === 10 && Math.log10\n      || base === 2 && Math.log2\n      || (base = Math.log(base), x => Math.log(x) / base);\n}\n\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\n\nexport function loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n\n  scale.base = function(_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n\n    if (r) ([u, v] = [v, u]);\n\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = ticks(u, v, n);\n    } else {\n      z = ticks(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = formatSpecifier(specifier)).precision == null) specifier.trim = true;\n      specifier = format(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n\n  scale.nice = () => {\n    return domain(nice(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n\n  return scale;\n}\n\nexport default function log() {\n  const scale = loggish(transformer()).domain([1, 10]);\n  scale.copy = () => copy(scale, log()).base(scale.base());\n  initRange.apply(scale, arguments);\n  return scale;\n}\n", "import {linearish} from \"./linear.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformSymlog(c) {\n  return function(x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\n\nfunction transformSymexp(c) {\n  return function(x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\n\nexport function symlogish(transform) {\n  var c = 1, scale = transform(transformSymlog(c), transformSymexp(c));\n\n  scale.constant = function(_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n\n  return linearish(scale);\n}\n\nexport default function symlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, symlog()).constant(scale.constant());\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "import {linearish} from \"./linear.js\";\nimport {copy, identity, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformPow(exponent) {\n  return function(x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\n\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\n\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\n\nexport function powish(transform) {\n  var scale = transform(identity, identity),\n      exponent = 1;\n\n  function rescale() {\n    return exponent === 1 ? transform(identity, identity)\n        : exponent === 0.5 ? transform(transformSqrt, transformSquare)\n        : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n\n  scale.exponent = function(_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n\n  return linearish(scale);\n}\n\nexport default function pow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, pow()).exponent(scale.exponent());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n\nexport function sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}\n", "import continuous from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport number from \"./number.js\";\n\nfunction square(x) {\n  return Math.sign(x) * x * x;\n}\n\nfunction unsquare(x) {\n  return Math.sign(x) * Math.sqrt(Math.abs(x));\n}\n\nexport default function radial() {\n  var squared = continuous(),\n      range = [0, 1],\n      round = false,\n      unknown;\n\n  function scale(x) {\n    var y = unsquare(squared(x));\n    return isNaN(y) ? unknown : round ? Math.round(y) : y;\n  }\n\n  scale.invert = function(y) {\n    return squared.invert(square(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (squared.domain(_), scale) : squared.domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (squared.range((range = Array.from(_, number)).map(square)), scale) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return scale.range(_).round(true);\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, scale) : round;\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (squared.clamp(_), scale) : squared.clamp();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return radial(squared.domain(), range)\n        .round(round)\n        .clamp(squared.clamp())\n        .unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import {ascending, bisect, quantileSorted as threshold} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantile() {\n  var domain = [],\n      range = [],\n      thresholds = [],\n      unknown;\n\n  function rescale() {\n    var i = 0, n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = threshold(domain, i / n);\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[bisect(thresholds, x)];\n  }\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [\n      i > 0 ? thresholds[i - 1] : domain[0],\n      i < thresholds.length ? thresholds[i] : domain[domain.length - 1]\n    ];\n  };\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return rescale();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.quantiles = function() {\n    return thresholds.slice();\n  };\n\n  scale.copy = function() {\n    return quantile()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "import {bisect} from \"d3-array\";\nimport {linearish} from \"./linear.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantize() {\n  var x0 = 0,\n      x1 = 1,\n      n = 1,\n      domain = [0.5],\n      range = [0, 1],\n      unknown;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN]\n        : i < 1 ? [x0, domain[0]]\n        : i >= n ? [domain[n - 1], x1]\n        : [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n\n  scale.thresholds = function() {\n    return domain.slice();\n  };\n\n  scale.copy = function() {\n    return quantize()\n        .domain([x0, x1])\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(linearish(scale), arguments);\n}\n", "import {bisect} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function threshold() {\n  var domain = [0.5],\n      range = [0, 1],\n      unknown,\n      n = 1;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return threshold()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "const t0 = new Date, t1 = new Date;\n\nexport function timeInterval(floori, offseti, count, field) {\n\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n  }\n\n  interval.floor = (date) => {\n    return floori(date = new Date(+date)), date;\n  };\n\n  interval.ceil = (date) => {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n\n  interval.round = (date) => {\n    const d0 = interval(date), d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n\n  interval.offset = (date, step) => {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n\n  interval.range = (start, stop, step) => {\n    const range = [];\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n    let previous;\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n    while (previous < start && start < stop);\n    return range;\n  };\n\n  interval.filter = (test) => {\n    return timeInterval((date) => {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, (date, step) => {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n\n  if (count) {\n    interval.count = (start, end) => {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n\n    interval.every = (step) => {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null\n          : !(step > 1) ? interval\n          : interval.filter(field\n              ? (d) => field(d) % step === 0\n              : (d) => interval.count(0, d) % step === 0);\n    };\n  }\n\n  return interval;\n}\n", "import {timeInterval} from \"./interval.js\";\n\nexport const millisecond = timeInterval(() => {\n  // noop\n}, (date, step) => {\n  date.setTime(+date + step);\n}, (start, end) => {\n  return end - start;\n});\n\n// An optimized implementation for this simple case.\nmillisecond.every = (k) => {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return timeInterval((date) => {\n    date.setTime(Math.floor(date / k) * k);\n  }, (date, step) => {\n    date.setTime(+date + step * k);\n  }, (start, end) => {\n    return (end - start) / k;\n  });\n};\n\nexport const milliseconds = millisecond.range;\n", "export const durationSecond = 1000;\nexport const durationMinute = durationSecond * 60;\nexport const durationHour = durationMinute * 60;\nexport const durationDay = durationHour * 24;\nexport const durationWeek = durationDay * 7;\nexport const durationMonth = durationDay * 30;\nexport const durationYear = durationDay * 365;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationSecond} from \"./duration.js\";\n\nexport const second = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, (date) => {\n  return date.getUTCSeconds();\n});\n\nexport const seconds = second.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeMinute = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getMinutes();\n});\n\nexport const timeMinutes = timeMinute.range;\n\nexport const utcMinute = timeInterval((date) => {\n  date.setUTCSeconds(0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getUTCMinutes();\n});\n\nexport const utcMinutes = utcMinute.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationHour, durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeHour = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getHours();\n});\n\nexport const timeHours = timeHour.range;\n\nexport const utcHour = timeInterval((date) => {\n  date.setUTCMinutes(0, 0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getUTCHours();\n});\n\nexport const utcHours = utcHour.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationDay, durationMinute} from \"./duration.js\";\n\nexport const timeDay = timeInterval(\n  date => date.setHours(0, 0, 0, 0),\n  (date, step) => date.setDate(date.getDate() + step),\n  (start, end) => (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay,\n  date => date.getDate() - 1\n);\n\nexport const timeDays = timeDay.range;\n\nexport const utcDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return date.getUTCDate() - 1;\n});\n\nexport const utcDays = utcDay.range;\n\nexport const unixDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return Math.floor(date / durationDay);\n});\n\nexport const unixDays = unixDay.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationWeek} from \"./duration.js\";\n\nfunction timeWeekday(i) {\n  return timeInterval((date) => {\n    date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setDate(date.getDate() + step * 7);\n  }, (start, end) => {\n    return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationWeek;\n  });\n}\n\nexport const timeSunday = timeWeekday(0);\nexport const timeMonday = timeWeekday(1);\nexport const timeTuesday = timeWeekday(2);\nexport const timeWednesday = timeWeekday(3);\nexport const timeThursday = timeWeekday(4);\nexport const timeFriday = timeWeekday(5);\nexport const timeSaturday = timeWeekday(6);\n\nexport const timeSundays = timeSunday.range;\nexport const timeMondays = timeMonday.range;\nexport const timeTuesdays = timeTuesday.range;\nexport const timeWednesdays = timeWednesday.range;\nexport const timeThursdays = timeThursday.range;\nexport const timeFridays = timeFriday.range;\nexport const timeSaturdays = timeSaturday.range;\n\nfunction utcWeekday(i) {\n  return timeInterval((date) => {\n    date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCDate(date.getUTCDate() + step * 7);\n  }, (start, end) => {\n    return (end - start) / durationWeek;\n  });\n}\n\nexport const utcSunday = utcWeekday(0);\nexport const utcMonday = utcWeekday(1);\nexport const utcTuesday = utcWeekday(2);\nexport const utcWednesday = utcWeekday(3);\nexport const utcThursday = utcWeekday(4);\nexport const utcFriday = utcWeekday(5);\nexport const utcSaturday = utcWeekday(6);\n\nexport const utcSundays = utcSunday.range;\nexport const utcMondays = utcMonday.range;\nexport const utcTuesdays = utcTuesday.range;\nexport const utcWednesdays = utcWednesday.range;\nexport const utcThursdays = utcThursday.range;\nexport const utcFridays = utcFriday.range;\nexport const utcSaturdays = utcSaturday.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeMonth = timeInterval((date) => {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setMonth(date.getMonth() + step);\n}, (start, end) => {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date) => {\n  return date.getMonth();\n});\n\nexport const timeMonths = timeMonth.range;\n\nexport const utcMonth = timeInterval((date) => {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end) => {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date) => {\n  return date.getUTCMonth();\n});\n\nexport const utcMonths = utcMonth.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeYear = timeInterval((date) => {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setFullYear(date.getFullYear() + step);\n}, (start, end) => {\n  return end.getFullYear() - start.getFullYear();\n}, (date) => {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\ntimeYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\n\nexport const timeYears = timeYear.range;\n\nexport const utcYear = timeInterval((date) => {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end) => {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date) => {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nexport const utcYears = utcYear.range;\n", "import {bisector, tickStep} from \"d3-array\";\nimport {durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear} from \"./duration.js\";\nimport {millisecond} from \"./millisecond.js\";\nimport {second} from \"./second.js\";\nimport {timeMinute, utcMinute} from \"./minute.js\";\nimport {timeHour, utcHour} from \"./hour.js\";\nimport {timeDay, unixDay} from \"./day.js\";\nimport {timeSunday, utcSunday} from \"./week.js\";\nimport {timeMonth, utcMonth} from \"./month.js\";\nimport {timeYear, utcYear} from \"./year.js\";\n\nfunction ticker(year, month, week, day, hour, minute) {\n\n  const tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);\n\nexport {utcTicks, utcTickInterval, timeTicks, timeTickInterval};\n", "import {\n  timeDay,\n  timeSunday,\n  timeMonday,\n  timeThursday,\n  timeYear,\n  utcDay,\n  utcSunday,\n  utcMonday,\n  utcThursday,\n  utcYear\n} from \"d3-time\";\n\nfunction localDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);\n    date.setFullYear(d.y);\n    return date;\n  }\n  return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);\n}\n\nfunction utcDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));\n    date.setUTCFullYear(d.y);\n    return date;\n  }\n  return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));\n}\n\nfunction newDate(y, m, d) {\n  return {y: y, m: m, d: d, H: 0, M: 0, S: 0, L: 0};\n}\n\nexport default function formatLocale(locale) {\n  var locale_dateTime = locale.dateTime,\n      locale_date = locale.date,\n      locale_time = locale.time,\n      locale_periods = locale.periods,\n      locale_weekdays = locale.days,\n      locale_shortWeekdays = locale.shortDays,\n      locale_months = locale.months,\n      locale_shortMonths = locale.shortMonths;\n\n  var periodRe = formatRe(locale_periods),\n      periodLookup = formatLookup(locale_periods),\n      weekdayRe = formatRe(locale_weekdays),\n      weekdayLookup = formatLookup(locale_weekdays),\n      shortWeekdayRe = formatRe(locale_shortWeekdays),\n      shortWeekdayLookup = formatLookup(locale_shortWeekdays),\n      monthRe = formatRe(locale_months),\n      monthLookup = formatLookup(locale_months),\n      shortMonthRe = formatRe(locale_shortMonths),\n      shortMonthLookup = formatLookup(locale_shortMonths);\n\n  var formats = {\n    \"a\": formatShortWeekday,\n    \"A\": formatWeekday,\n    \"b\": formatShortMonth,\n    \"B\": formatMonth,\n    \"c\": null,\n    \"d\": formatDayOfMonth,\n    \"e\": formatDayOfMonth,\n    \"f\": formatMicroseconds,\n    \"g\": formatYearISO,\n    \"G\": formatFullYearISO,\n    \"H\": formatHour24,\n    \"I\": formatHour12,\n    \"j\": formatDayOfYear,\n    \"L\": formatMilliseconds,\n    \"m\": formatMonthNumber,\n    \"M\": formatMinutes,\n    \"p\": formatPeriod,\n    \"q\": formatQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatSeconds,\n    \"u\": formatWeekdayNumberMonday,\n    \"U\": formatWeekNumberSunday,\n    \"V\": formatWeekNumberISO,\n    \"w\": formatWeekdayNumberSunday,\n    \"W\": formatWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatYear,\n    \"Y\": formatFullYear,\n    \"Z\": formatZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var utcFormats = {\n    \"a\": formatUTCShortWeekday,\n    \"A\": formatUTCWeekday,\n    \"b\": formatUTCShortMonth,\n    \"B\": formatUTCMonth,\n    \"c\": null,\n    \"d\": formatUTCDayOfMonth,\n    \"e\": formatUTCDayOfMonth,\n    \"f\": formatUTCMicroseconds,\n    \"g\": formatUTCYearISO,\n    \"G\": formatUTCFullYearISO,\n    \"H\": formatUTCHour24,\n    \"I\": formatUTCHour12,\n    \"j\": formatUTCDayOfYear,\n    \"L\": formatUTCMilliseconds,\n    \"m\": formatUTCMonthNumber,\n    \"M\": formatUTCMinutes,\n    \"p\": formatUTCPeriod,\n    \"q\": formatUTCQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatUTCSeconds,\n    \"u\": formatUTCWeekdayNumberMonday,\n    \"U\": formatUTCWeekNumberSunday,\n    \"V\": formatUTCWeekNumberISO,\n    \"w\": formatUTCWeekdayNumberSunday,\n    \"W\": formatUTCWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatUTCYear,\n    \"Y\": formatUTCFullYear,\n    \"Z\": formatUTCZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var parses = {\n    \"a\": parseShortWeekday,\n    \"A\": parseWeekday,\n    \"b\": parseShortMonth,\n    \"B\": parseMonth,\n    \"c\": parseLocaleDateTime,\n    \"d\": parseDayOfMonth,\n    \"e\": parseDayOfMonth,\n    \"f\": parseMicroseconds,\n    \"g\": parseYear,\n    \"G\": parseFullYear,\n    \"H\": parseHour24,\n    \"I\": parseHour24,\n    \"j\": parseDayOfYear,\n    \"L\": parseMilliseconds,\n    \"m\": parseMonthNumber,\n    \"M\": parseMinutes,\n    \"p\": parsePeriod,\n    \"q\": parseQuarter,\n    \"Q\": parseUnixTimestamp,\n    \"s\": parseUnixTimestampSeconds,\n    \"S\": parseSeconds,\n    \"u\": parseWeekdayNumberMonday,\n    \"U\": parseWeekNumberSunday,\n    \"V\": parseWeekNumberISO,\n    \"w\": parseWeekdayNumberSunday,\n    \"W\": parseWeekNumberMonday,\n    \"x\": parseLocaleDate,\n    \"X\": parseLocaleTime,\n    \"y\": parseYear,\n    \"Y\": parseFullYear,\n    \"Z\": parseZone,\n    \"%\": parseLiteralPercent\n  };\n\n  // These recursive directive definitions must be deferred.\n  formats.x = newFormat(locale_date, formats);\n  formats.X = newFormat(locale_time, formats);\n  formats.c = newFormat(locale_dateTime, formats);\n  utcFormats.x = newFormat(locale_date, utcFormats);\n  utcFormats.X = newFormat(locale_time, utcFormats);\n  utcFormats.c = newFormat(locale_dateTime, utcFormats);\n\n  function newFormat(specifier, formats) {\n    return function(date) {\n      var string = [],\n          i = -1,\n          j = 0,\n          n = specifier.length,\n          c,\n          pad,\n          format;\n\n      if (!(date instanceof Date)) date = new Date(+date);\n\n      while (++i < n) {\n        if (specifier.charCodeAt(i) === 37) {\n          string.push(specifier.slice(j, i));\n          if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);\n          else pad = c === \"e\" ? \" \" : \"0\";\n          if (format = formats[c]) c = format(date, pad);\n          string.push(c);\n          j = i + 1;\n        }\n      }\n\n      string.push(specifier.slice(j, i));\n      return string.join(\"\");\n    };\n  }\n\n  function newParse(specifier, Z) {\n    return function(string) {\n      var d = newDate(1900, undefined, 1),\n          i = parseSpecifier(d, specifier, string += \"\", 0),\n          week, day;\n      if (i != string.length) return null;\n\n      // If a UNIX timestamp is specified, return it.\n      if (\"Q\" in d) return new Date(d.Q);\n      if (\"s\" in d) return new Date(d.s * 1000 + (\"L\" in d ? d.L : 0));\n\n      // If this is utcParse, never use the local timezone.\n      if (Z && !(\"Z\" in d)) d.Z = 0;\n\n      // The am-pm flag is 0 for AM, and 1 for PM.\n      if (\"p\" in d) d.H = d.H % 12 + d.p * 12;\n\n      // If the month was not specified, inherit from the quarter.\n      if (d.m === undefined) d.m = \"q\" in d ? d.q : 0;\n\n      // Convert day-of-week and week-of-year to day-of-year.\n      if (\"V\" in d) {\n        if (d.V < 1 || d.V > 53) return null;\n        if (!(\"w\" in d)) d.w = 1;\n        if (\"Z\" in d) {\n          week = utcDate(newDate(d.y, 0, 1)), day = week.getUTCDay();\n          week = day > 4 || day === 0 ? utcMonday.ceil(week) : utcMonday(week);\n          week = utcDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getUTCFullYear();\n          d.m = week.getUTCMonth();\n          d.d = week.getUTCDate() + (d.w + 6) % 7;\n        } else {\n          week = localDate(newDate(d.y, 0, 1)), day = week.getDay();\n          week = day > 4 || day === 0 ? timeMonday.ceil(week) : timeMonday(week);\n          week = timeDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getFullYear();\n          d.m = week.getMonth();\n          d.d = week.getDate() + (d.w + 6) % 7;\n        }\n      } else if (\"W\" in d || \"U\" in d) {\n        if (!(\"w\" in d)) d.w = \"u\" in d ? d.u % 7 : \"W\" in d ? 1 : 0;\n        day = \"Z\" in d ? utcDate(newDate(d.y, 0, 1)).getUTCDay() : localDate(newDate(d.y, 0, 1)).getDay();\n        d.m = 0;\n        d.d = \"W\" in d ? (d.w + 6) % 7 + d.W * 7 - (day + 5) % 7 : d.w + d.U * 7 - (day + 6) % 7;\n      }\n\n      // If a time zone is specified, all fields are interpreted as UTC and then\n      // offset according to the specified time zone.\n      if (\"Z\" in d) {\n        d.H += d.Z / 100 | 0;\n        d.M += d.Z % 100;\n        return utcDate(d);\n      }\n\n      // Otherwise, all fields are in local time.\n      return localDate(d);\n    };\n  }\n\n  function parseSpecifier(d, specifier, string, j) {\n    var i = 0,\n        n = specifier.length,\n        m = string.length,\n        c,\n        parse;\n\n    while (i < n) {\n      if (j >= m) return -1;\n      c = specifier.charCodeAt(i++);\n      if (c === 37) {\n        c = specifier.charAt(i++);\n        parse = parses[c in pads ? specifier.charAt(i++) : c];\n        if (!parse || ((j = parse(d, string, j)) < 0)) return -1;\n      } else if (c != string.charCodeAt(j++)) {\n        return -1;\n      }\n    }\n\n    return j;\n  }\n\n  function parsePeriod(d, string, i) {\n    var n = periodRe.exec(string.slice(i));\n    return n ? (d.p = periodLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortWeekday(d, string, i) {\n    var n = shortWeekdayRe.exec(string.slice(i));\n    return n ? (d.w = shortWeekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseWeekday(d, string, i) {\n    var n = weekdayRe.exec(string.slice(i));\n    return n ? (d.w = weekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortMonth(d, string, i) {\n    var n = shortMonthRe.exec(string.slice(i));\n    return n ? (d.m = shortMonthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseMonth(d, string, i) {\n    var n = monthRe.exec(string.slice(i));\n    return n ? (d.m = monthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseLocaleDateTime(d, string, i) {\n    return parseSpecifier(d, locale_dateTime, string, i);\n  }\n\n  function parseLocaleDate(d, string, i) {\n    return parseSpecifier(d, locale_date, string, i);\n  }\n\n  function parseLocaleTime(d, string, i) {\n    return parseSpecifier(d, locale_time, string, i);\n  }\n\n  function formatShortWeekday(d) {\n    return locale_shortWeekdays[d.getDay()];\n  }\n\n  function formatWeekday(d) {\n    return locale_weekdays[d.getDay()];\n  }\n\n  function formatShortMonth(d) {\n    return locale_shortMonths[d.getMonth()];\n  }\n\n  function formatMonth(d) {\n    return locale_months[d.getMonth()];\n  }\n\n  function formatPeriod(d) {\n    return locale_periods[+(d.getHours() >= 12)];\n  }\n\n  function formatQuarter(d) {\n    return 1 + ~~(d.getMonth() / 3);\n  }\n\n  function formatUTCShortWeekday(d) {\n    return locale_shortWeekdays[d.getUTCDay()];\n  }\n\n  function formatUTCWeekday(d) {\n    return locale_weekdays[d.getUTCDay()];\n  }\n\n  function formatUTCShortMonth(d) {\n    return locale_shortMonths[d.getUTCMonth()];\n  }\n\n  function formatUTCMonth(d) {\n    return locale_months[d.getUTCMonth()];\n  }\n\n  function formatUTCPeriod(d) {\n    return locale_periods[+(d.getUTCHours() >= 12)];\n  }\n\n  function formatUTCQuarter(d) {\n    return 1 + ~~(d.getUTCMonth() / 3);\n  }\n\n  return {\n    format: function(specifier) {\n      var f = newFormat(specifier += \"\", formats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    parse: function(specifier) {\n      var p = newParse(specifier += \"\", false);\n      p.toString = function() { return specifier; };\n      return p;\n    },\n    utcFormat: function(specifier) {\n      var f = newFormat(specifier += \"\", utcFormats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    utcParse: function(specifier) {\n      var p = newParse(specifier += \"\", true);\n      p.toString = function() { return specifier; };\n      return p;\n    }\n  };\n}\n\nvar pads = {\"-\": \"\", \"_\": \" \", \"0\": \"0\"},\n    numberRe = /^\\s*\\d+/, // note: ignores next directive\n    percentRe = /^%/,\n    requoteRe = /[\\\\^$*+?|[\\]().{}]/g;\n\nfunction pad(value, fill, width) {\n  var sign = value < 0 ? \"-\" : \"\",\n      string = (sign ? -value : value) + \"\",\n      length = string.length;\n  return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);\n}\n\nfunction requote(s) {\n  return s.replace(requoteRe, \"\\\\$&\");\n}\n\nfunction formatRe(names) {\n  return new RegExp(\"^(?:\" + names.map(requote).join(\"|\") + \")\", \"i\");\n}\n\nfunction formatLookup(names) {\n  return new Map(names.map((name, i) => [name.toLowerCase(), i]));\n}\n\nfunction parseWeekdayNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.w = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekdayNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.u = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.U = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberISO(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.V = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.W = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseFullYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 4));\n  return n ? (d.y = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;\n}\n\nfunction parseZone(d, string, i) {\n  var n = /^(Z)|([+-]\\d\\d)(?::?(\\d\\d))?/.exec(string.slice(i, i + 6));\n  return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || \"00\")), i + n[0].length) : -1;\n}\n\nfunction parseQuarter(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.q = n[0] * 3 - 3, i + n[0].length) : -1;\n}\n\nfunction parseMonthNumber(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.m = n[0] - 1, i + n[0].length) : -1;\n}\n\nfunction parseDayOfMonth(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseDayOfYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseHour24(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.H = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMinutes(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.M = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.S = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMilliseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.L = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMicroseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 6));\n  return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;\n}\n\nfunction parseLiteralPercent(d, string, i) {\n  var n = percentRe.exec(string.slice(i, i + 1));\n  return n ? i + n[0].length : -1;\n}\n\nfunction parseUnixTimestamp(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.Q = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseUnixTimestampSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.s = +n[0], i + n[0].length) : -1;\n}\n\nfunction formatDayOfMonth(d, p) {\n  return pad(d.getDate(), p, 2);\n}\n\nfunction formatHour24(d, p) {\n  return pad(d.getHours(), p, 2);\n}\n\nfunction formatHour12(d, p) {\n  return pad(d.getHours() % 12 || 12, p, 2);\n}\n\nfunction formatDayOfYear(d, p) {\n  return pad(1 + timeDay.count(timeYear(d), d), p, 3);\n}\n\nfunction formatMilliseconds(d, p) {\n  return pad(d.getMilliseconds(), p, 3);\n}\n\nfunction formatMicroseconds(d, p) {\n  return formatMilliseconds(d, p) + \"000\";\n}\n\nfunction formatMonthNumber(d, p) {\n  return pad(d.getMonth() + 1, p, 2);\n}\n\nfunction formatMinutes(d, p) {\n  return pad(d.getMinutes(), p, 2);\n}\n\nfunction formatSeconds(d, p) {\n  return pad(d.getSeconds(), p, 2);\n}\n\nfunction formatWeekdayNumberMonday(d) {\n  var day = d.getDay();\n  return day === 0 ? 7 : day;\n}\n\nfunction formatWeekNumberSunday(d, p) {\n  return pad(timeSunday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction dISO(d) {\n  var day = d.getDay();\n  return (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n}\n\nfunction formatWeekNumberISO(d, p) {\n  d = dISO(d);\n  return pad(timeThursday.count(timeYear(d), d) + (timeYear(d).getDay() === 4), p, 2);\n}\n\nfunction formatWeekdayNumberSunday(d) {\n  return d.getDay();\n}\n\nfunction formatWeekNumberMonday(d, p) {\n  return pad(timeMonday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction formatYear(d, p) {\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatYearISO(d, p) {\n  d = dISO(d);\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatFullYear(d, p) {\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatFullYearISO(d, p) {\n  var day = d.getDay();\n  d = (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatZone(d) {\n  var z = d.getTimezoneOffset();\n  return (z > 0 ? \"-\" : (z *= -1, \"+\"))\n      + pad(z / 60 | 0, \"0\", 2)\n      + pad(z % 60, \"0\", 2);\n}\n\nfunction formatUTCDayOfMonth(d, p) {\n  return pad(d.getUTCDate(), p, 2);\n}\n\nfunction formatUTCHour24(d, p) {\n  return pad(d.getUTCHours(), p, 2);\n}\n\nfunction formatUTCHour12(d, p) {\n  return pad(d.getUTCHours() % 12 || 12, p, 2);\n}\n\nfunction formatUTCDayOfYear(d, p) {\n  return pad(1 + utcDay.count(utcYear(d), d), p, 3);\n}\n\nfunction formatUTCMilliseconds(d, p) {\n  return pad(d.getUTCMilliseconds(), p, 3);\n}\n\nfunction formatUTCMicroseconds(d, p) {\n  return formatUTCMilliseconds(d, p) + \"000\";\n}\n\nfunction formatUTCMonthNumber(d, p) {\n  return pad(d.getUTCMonth() + 1, p, 2);\n}\n\nfunction formatUTCMinutes(d, p) {\n  return pad(d.getUTCMinutes(), p, 2);\n}\n\nfunction formatUTCSeconds(d, p) {\n  return pad(d.getUTCSeconds(), p, 2);\n}\n\nfunction formatUTCWeekdayNumberMonday(d) {\n  var dow = d.getUTCDay();\n  return dow === 0 ? 7 : dow;\n}\n\nfunction formatUTCWeekNumberSunday(d, p) {\n  return pad(utcSunday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction UTCdISO(d) {\n  var day = d.getUTCDay();\n  return (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n}\n\nfunction formatUTCWeekNumberISO(d, p) {\n  d = UTCdISO(d);\n  return pad(utcThursday.count(utcYear(d), d) + (utcYear(d).getUTCDay() === 4), p, 2);\n}\n\nfunction formatUTCWeekdayNumberSunday(d) {\n  return d.getUTCDay();\n}\n\nfunction formatUTCWeekNumberMonday(d, p) {\n  return pad(utcMonday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction formatUTCYear(d, p) {\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCYearISO(d, p) {\n  d = UTCdISO(d);\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCFullYear(d, p) {\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCFullYearISO(d, p) {\n  var day = d.getUTCDay();\n  d = (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCZone() {\n  return \"+0000\";\n}\n\nfunction formatLiteralPercent() {\n  return \"%\";\n}\n\nfunction formatUnixTimestamp(d) {\n  return +d;\n}\n\nfunction formatUnixTimestampSeconds(d) {\n  return Math.floor(+d / 1000);\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var timeFormat;\nexport var timeParse;\nexport var utcFormat;\nexport var utcParse;\n\ndefaultLocale({\n  dateTime: \"%x, %X\",\n  date: \"%-m/%-d/%Y\",\n  time: \"%-I:%M:%S %p\",\n  periods: [\"AM\", \"PM\"],\n  days: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n  shortDays: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  months: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"],\n  shortMonths: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  timeFormat = locale.format;\n  timeParse = locale.parse;\n  utcFormat = locale.utcFormat;\n  utcParse = locale.utcParse;\n  return locale;\n}\n", "import {utcFormat} from \"./defaultLocale.js\";\n\nexport var isoSpecifier = \"%Y-%m-%dT%H:%M:%S.%LZ\";\n\nfunction formatIsoNative(date) {\n  return date.toISOString();\n}\n\nvar formatIso = Date.prototype.toISOString\n    ? formatIsoNative\n    : utcFormat(isoSpecifier);\n\nexport default formatIso;\n", "import {isoSpecifier} from \"./isoFormat.js\";\nimport {utcParse} from \"./defaultLocale.js\";\n\nfunction parseIsoNative(string) {\n  var date = new Date(string);\n  return isNaN(date) ? null : date;\n}\n\nvar parseIso = +new Date(\"2000-01-01T00:00:00.000Z\")\n    ? parseIsoNative\n    : utcParse(isoSpecifier);\n\nexport default parseIso;\n", "import {timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeTicks, timeTickInterval} from \"d3-time\";\nimport {timeFormat} from \"d3-time-format\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport nice from \"./nice.js\";\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nexport function calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = continuous(),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain(nice(d, interval)) : scale;\n  };\n\n  scale.copy = function() {\n    return copy(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nexport default function time() {\n  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n", "import {utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcTicks, utcTickInterval} from \"d3-time\";\nimport {utcFormat} from \"d3-time-format\";\nimport {calendar} from \"./time.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function utcTime() {\n  return initRange.apply(calendar(utcTicks, utcTickInterval, utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}\n", "import {interpolate, interpolateRound} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .interpolator(source.interpolator())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport default function sequential() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, sequential());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialLog() {\n  var scale = loggish(transformer()).domain([1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}\n", "import {ascending, bisect, quantile} from \"d3-array\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\n\nexport default function sequentialQuantile() {\n  var domain = [],\n      interpolator = identity;\n\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator((bisect(domain, x, 1) - 1) / (domain.length - 1));\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return scale;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.range = function() {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n\n  scale.quantiles = function(n) {\n    return Array.from({length: n + 1}, (_, i) => quantile(domain, i / n));\n  };\n\n  scale.copy = function() {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n", "import {interpolate, interpolateRound, piecewise} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {copy} from \"./sequential.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 0.5,\n      x2 = 1,\n      s = 1,\n      t0,\n      t1,\n      t2,\n      k10,\n      k21,\n      interpolator = identity,\n      transform,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = piecewise(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\n\nexport default function diverging() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, diverging());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingLog() {\n  var scale = loggish(transformer()).domain([0.1, 1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, divergingLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAO,SAAS,UAAU,QAAQ,OAAO;AACvC,UAAQ,UAAU,QAAQ;AAAA,IACxB,KAAK;AAAG;AAAA,IACR,KAAK;AAAG,WAAK,MAAM,MAAM;AAAG;AAAA,IAC5B;AAAS,WAAK,MAAM,KAAK,EAAE,OAAO,MAAM;AAAG;AAAA,EAC7C;AACA,SAAO;AACT;AAEO,SAAS,iBAAiB,QAAQ,cAAc;AACrD,UAAQ,UAAU,QAAQ;AAAA,IACxB,KAAK;AAAG;AAAA,IACR,KAAK,GAAG;AACN,UAAI,OAAO,WAAW;AAAY,aAAK,aAAa,MAAM;AAAA;AACrD,aAAK,MAAM,MAAM;AACtB;AAAA,IACF;AAAA,IACA,SAAS;AACP,WAAK,OAAO,MAAM;AAClB,UAAI,OAAO,iBAAiB;AAAY,aAAK,aAAa,YAAY;AAAA;AACjE,aAAK,MAAM,YAAY;AAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACtBO,IAAM,WAAW,OAAO,UAAU;AAE1B,SAAR,UAA2B;AAChC,MAAI,QAAQ,IAAI,UAAU,GACtB,SAAS,CAAC,GACV,QAAQ,CAAC,GACT,UAAU;AAEd,WAAS,MAAM,GAAG;AAChB,QAAI,IAAI,MAAM,IAAI,CAAC;AACnB,QAAI,MAAM,QAAW;AACnB,UAAI,YAAY;AAAU,eAAO;AACjC,YAAM,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC;AAAA,IACrC;AACA,WAAO,MAAM,IAAI,MAAM,MAAM;AAAA,EAC/B;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU;AAAQ,aAAO,OAAO,MAAM;AAC3C,aAAS,CAAC,GAAG,QAAQ,IAAI,UAAU;AACnC,eAAW,SAAS,GAAG;AACrB,UAAI,MAAM,IAAI,KAAK;AAAG;AACtB,YAAM,IAAI,OAAO,OAAO,KAAK,KAAK,IAAI,CAAC;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,MAAM,KAAK,CAAC,GAAG,SAAS,MAAM,MAAM;AAAA,EACzE;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,QAAQ,QAAQ,KAAK,EAAE,QAAQ,OAAO;AAAA,EAC/C;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO;AACT;;;ACzCe,SAAR,OAAwB;AAC7B,MAAI,QAAQ,QAAQ,EAAE,QAAQ,MAAS,GACnC,SAAS,MAAM,QACf,eAAe,MAAM,OACrB,KAAK,GACL,KAAK,GACL,MACA,WACA,QAAQ,OACR,eAAe,GACf,eAAe,GACf,QAAQ;AAEZ,SAAO,MAAM;AAEb,WAAS,UAAU;AACjB,QAAI,IAAI,OAAO,EAAE,QACb,UAAU,KAAK,IACf,QAAQ,UAAU,KAAK,IACvB,OAAO,UAAU,KAAK;AAC1B,YAAQ,OAAO,SAAS,KAAK,IAAI,GAAG,IAAI,eAAe,eAAe,CAAC;AACvE,QAAI;AAAO,aAAO,KAAK,MAAM,IAAI;AACjC,cAAU,OAAO,QAAQ,QAAQ,IAAI,iBAAiB;AACtD,gBAAY,QAAQ,IAAI;AACxB,QAAI;AAAO,cAAQ,KAAK,MAAM,KAAK,GAAG,YAAY,KAAK,MAAM,SAAS;AACtE,QAAI,SAAS,cAAS,CAAC,EAAE,IAAI,SAAS,GAAG;AAAE,aAAO,QAAQ,OAAO;AAAA,IAAG,CAAC;AACrE,WAAO,aAAa,UAAU,OAAO,QAAQ,IAAI,MAAM;AAAA,EACzD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,QAAQ,KAAK,OAAO;AAAA,EAC5D;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,EAAE;AAAA,EACnF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,MAAM,QAAQ;AAAA,EACjE;AAEA,QAAM,YAAY,WAAW;AAC3B,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EACvD;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,eAAe,KAAK,IAAI,GAAG,eAAe,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EACzF;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,KAAK;AAAA,EACzE;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,CAAC,GAAG,QAAQ,KAAK;AAAA,EAC7D;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EAC/E;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,EACzB,MAAM,KAAK,EACX,aAAa,YAAY,EACzB,aAAa,YAAY,EACzB,MAAM,KAAK;AAAA,EAClB;AAEA,SAAO,UAAU,MAAM,QAAQ,GAAG,SAAS;AAC7C;AAEA,SAAS,SAAS,OAAO;AACvB,MAAIA,QAAO,MAAM;AAEjB,QAAM,UAAU,MAAM;AACtB,SAAO,MAAM;AACb,SAAO,MAAM;AAEb,QAAM,OAAO,WAAW;AACtB,WAAO,SAASA,MAAK,CAAC;AAAA,EACxB;AAEA,SAAO;AACT;AAEO,SAAS,QAAQ;AACtB,SAAO,SAAS,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,CAAC,CAAC;AAC7D;;;ACpGe,SAAR,UAA2B,GAAG;AACnC,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACJe,SAAR,OAAwB,GAAG;AAChC,SAAO,CAAC;AACV;;;ACGA,IAAI,OAAO,CAAC,GAAG,CAAC;AAET,SAAS,SAAS,GAAG;AAC1B,SAAO;AACT;AAEA,SAAS,UAAU,GAAG,GAAG;AACvB,UAAQ,KAAM,IAAI,CAAC,KACb,SAAS,GAAG;AAAE,YAAQ,IAAI,KAAK;AAAA,EAAG,IAClC,UAAS,MAAM,CAAC,IAAI,MAAM,GAAG;AACrC;AAEA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI;AACJ,MAAI,IAAI;AAAG,QAAI,GAAG,IAAI,GAAG,IAAI;AAC7B,SAAO,SAAS,GAAG;AAAE,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAAA,EAAG;AAC3D;AAIA,SAAS,MAAM,QAAQ,OAAO,aAAa;AACzC,MAAI,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC;AAC/D,MAAI,KAAK;AAAI,SAAK,UAAU,IAAI,EAAE,GAAG,KAAK,YAAY,IAAI,EAAE;AAAA;AACvD,SAAK,UAAU,IAAI,EAAE,GAAG,KAAK,YAAY,IAAI,EAAE;AACpD,SAAO,SAAS,GAAG;AAAE,WAAO,GAAG,GAAG,CAAC,CAAC;AAAA,EAAG;AACzC;AAEA,SAAS,QAAQ,QAAQ,OAAO,aAAa;AAC3C,MAAI,IAAI,KAAK,IAAI,OAAO,QAAQ,MAAM,MAAM,IAAI,GAC5C,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI;AAGR,MAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG;AACzB,aAAS,OAAO,MAAM,EAAE,QAAQ;AAChC,YAAQ,MAAM,MAAM,EAAE,QAAQ;AAAA,EAChC;AAEA,SAAO,EAAE,IAAI,GAAG;AACd,MAAE,CAAC,IAAI,UAAU,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACzC,MAAE,CAAC,IAAI,YAAY,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AAAA,EAC3C;AAEA,SAAO,SAAS,GAAG;AACjB,QAAIC,KAAI,eAAO,QAAQ,GAAG,GAAG,CAAC,IAAI;AAClC,WAAO,EAAEA,EAAC,EAAE,EAAEA,EAAC,EAAE,CAAC,CAAC;AAAA,EACrB;AACF;AAEO,SAAS,KAAK,QAAQ,QAAQ;AACnC,SAAO,OACF,OAAO,OAAO,OAAO,CAAC,EACtB,MAAM,OAAO,MAAM,CAAC,EACpB,YAAY,OAAO,YAAY,CAAC,EAChC,MAAM,OAAO,MAAM,CAAC,EACpB,QAAQ,OAAO,QAAQ,CAAC;AAC/B;AAEO,SAAS,cAAc;AAC5B,MAAI,SAAS,MACT,QAAQ,MACR,cAAc,eACd,WACA,aACA,SACA,QAAQ,UACRC,YACA,QACA;AAEJ,WAAS,UAAU;AACjB,QAAI,IAAI,KAAK,IAAI,OAAO,QAAQ,MAAM,MAAM;AAC5C,QAAI,UAAU;AAAU,cAAQ,QAAQ,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAChE,IAAAA,aAAY,IAAI,IAAI,UAAU;AAC9B,aAAS,QAAQ;AACjB,WAAO;AAAA,EACT;AAEA,WAAS,MAAM,GAAG;AAChB,WAAO,KAAK,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAI,WAAW,WAAW,SAASA,WAAU,OAAO,IAAI,SAAS,GAAG,OAAO,WAAW,IAAI,UAAU,MAAM,CAAC,CAAC,CAAC;AAAA,EAC/I;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,MAAM,aAAa,UAAU,QAAQA,WAAU,OAAO,OAAO,IAAI,SAAS,GAAG,cAAiB,IAAI,CAAC,CAAC,CAAC;AAAA,EAC9G;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,MAAM,KAAK,GAAG,MAAM,GAAG,QAAQ,KAAK,OAAO,MAAM;AAAA,EACvF;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM;AAAA,EAC7E;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,QAAQ,MAAM,KAAK,CAAC,GAAG,cAAc,eAAkB,QAAQ;AAAA,EACxE;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,IAAI,OAAO,UAAU,QAAQ,KAAK,UAAU;AAAA,EACjF;AAEA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,cAAc,GAAG,QAAQ,KAAK;AAAA,EAC3D;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,SAAO,SAAS,GAAG,GAAG;AACpB,gBAAY,GAAG,cAAc;AAC7B,WAAO,QAAQ;AAAA,EACjB;AACF;AAEe,SAAR,aAA8B;AACnC,SAAO,YAAY,EAAE,UAAU,QAAQ;AACzC;;;AC5He,SAAR,sBAAiB,GAAG;AACzB,SAAO,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,KAAK,OAChC,EAAE,eAAe,IAAI,EAAE,QAAQ,MAAM,EAAE,IACvC,EAAE,SAAS,EAAE;AACrB;AAKO,SAAS,mBAAmB,GAAG,GAAG;AACvC,OAAK,KAAK,IAAI,IAAI,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,cAAc,GAAG,QAAQ,GAAG,KAAK;AAAG,WAAO;AACxF,MAAI,GAAG,cAAc,EAAE,MAAM,GAAG,CAAC;AAIjC,SAAO;AAAA,IACL,YAAY,SAAS,IAAI,YAAY,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI;AAAA,IACjE,CAAC,EAAE,MAAM,IAAI,CAAC;AAAA,EAChB;AACF;;;ACjBe,SAAR,iBAAiB,GAAG;AACzB,SAAO,IAAI,mBAAmB,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI;AACzD;;;ACJe,SAAR,oBAAiB,UAAU,WAAW;AAC3C,SAAO,SAAS,OAAO,OAAO;AAC5B,QAAI,IAAI,MAAM,QACV,IAAI,CAAC,GACL,IAAI,GACJ,IAAI,SAAS,CAAC,GACd,SAAS;AAEb,WAAO,IAAI,KAAK,IAAI,GAAG;AACrB,UAAI,SAAS,IAAI,IAAI;AAAO,YAAI,KAAK,IAAI,GAAG,QAAQ,MAAM;AAC1D,QAAE,KAAK,MAAM,UAAU,KAAK,GAAG,IAAI,CAAC,CAAC;AACrC,WAAK,UAAU,IAAI,KAAK;AAAO;AAC/B,UAAI,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM;AAAA,IAC5C;AAEA,WAAO,EAAE,QAAQ,EAAE,KAAK,SAAS;AAAA,EACnC;AACF;;;ACjBe,SAAR,uBAAiB,UAAU;AAChC,SAAO,SAAS,OAAO;AACrB,WAAO,MAAM,QAAQ,UAAU,SAAS,GAAG;AACzC,aAAO,SAAS,CAAC,CAAC;AAAA,IACpB,CAAC;AAAA,EACH;AACF;;;ACLA,IAAI,KAAK;AAEM,SAAR,gBAAiC,WAAW;AACjD,MAAI,EAAE,QAAQ,GAAG,KAAK,SAAS;AAAI,UAAM,IAAI,MAAM,qBAAqB,SAAS;AACjF,MAAI;AACJ,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,MAAM,CAAC;AAAA,IACb,OAAO,MAAM,CAAC;AAAA,IACd,MAAM,MAAM,CAAC;AAAA,IACb,QAAQ,MAAM,CAAC;AAAA,IACf,MAAM,MAAM,CAAC;AAAA,IACb,OAAO,MAAM,CAAC;AAAA,IACd,OAAO,MAAM,CAAC;AAAA,IACd,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC;AAAA,IACvC,MAAM,MAAM,CAAC;AAAA,IACb,MAAM,MAAM,EAAE;AAAA,EAChB,CAAC;AACH;AAEA,gBAAgB,YAAY,gBAAgB;AAErC,SAAS,gBAAgB,WAAW;AACzC,OAAK,OAAO,UAAU,SAAS,SAAY,MAAM,UAAU,OAAO;AAClE,OAAK,QAAQ,UAAU,UAAU,SAAY,MAAM,UAAU,QAAQ;AACrE,OAAK,OAAO,UAAU,SAAS,SAAY,MAAM,UAAU,OAAO;AAClE,OAAK,SAAS,UAAU,WAAW,SAAY,KAAK,UAAU,SAAS;AACvE,OAAK,OAAO,CAAC,CAAC,UAAU;AACxB,OAAK,QAAQ,UAAU,UAAU,SAAY,SAAY,CAAC,UAAU;AACpE,OAAK,QAAQ,CAAC,CAAC,UAAU;AACzB,OAAK,YAAY,UAAU,cAAc,SAAY,SAAY,CAAC,UAAU;AAC5E,OAAK,OAAO,CAAC,CAAC,UAAU;AACxB,OAAK,OAAO,UAAU,SAAS,SAAY,KAAK,UAAU,OAAO;AACnE;AAEA,gBAAgB,UAAU,WAAW,WAAW;AAC9C,SAAO,KAAK,OACN,KAAK,QACL,KAAK,OACL,KAAK,UACJ,KAAK,OAAO,MAAM,OAClB,KAAK,UAAU,SAAY,KAAK,KAAK,IAAI,GAAG,KAAK,QAAQ,CAAC,MAC1D,KAAK,QAAQ,MAAM,OACnB,KAAK,cAAc,SAAY,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,YAAY,CAAC,MACxE,KAAK,OAAO,MAAM,MACnB,KAAK;AACb;;;AC7Ce,SAAR,mBAAiB,GAAG;AACzB;AAAK,aAAS,IAAI,EAAE,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG;AAC1D,cAAQ,EAAE,CAAC,GAAG;AAAA,QACZ,KAAK;AAAK,eAAK,KAAK;AAAG;AAAA,QACvB,KAAK;AAAK,cAAI,OAAO;AAAG,iBAAK;AAAG,eAAK;AAAG;AAAA,QACxC;AAAS,cAAI,CAAC,CAAC,EAAE,CAAC;AAAG,kBAAM;AAAK,cAAI,KAAK;AAAG,iBAAK;AAAG;AAAA,MACtD;AAAA,IACF;AACA,SAAO,KAAK,IAAI,EAAE,MAAM,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC,IAAI;AACrD;;;ACRO,IAAI;AAEI,SAAR,yBAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,mBAAmB,GAAG,CAAC;AAC/B,MAAI,CAAC;AAAG,WAAO,IAAI;AACnB,MAAI,cAAc,EAAE,CAAC,GACjB,WAAW,EAAE,CAAC,GACd,IAAI,YAAY,iBAAiB,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,WAAW,CAAC,CAAC,CAAC,IAAI,KAAK,GAC5F,IAAI,YAAY;AACpB,SAAO,MAAM,IAAI,cACX,IAAI,IAAI,cAAc,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,GAAG,IACnD,IAAI,IAAI,YAAY,MAAM,GAAG,CAAC,IAAI,MAAM,YAAY,MAAM,CAAC,IAC3D,OAAO,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,mBAAmB,GAAG,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;AAC3F;;;ACbe,SAAR,sBAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,mBAAmB,GAAG,CAAC;AAC/B,MAAI,CAAC;AAAG,WAAO,IAAI;AACnB,MAAI,cAAc,EAAE,CAAC,GACjB,WAAW,EAAE,CAAC;AAClB,SAAO,WAAW,IAAI,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI,cACxD,YAAY,SAAS,WAAW,IAAI,YAAY,MAAM,GAAG,WAAW,CAAC,IAAI,MAAM,YAAY,MAAM,WAAW,CAAC,IAC7G,cAAc,IAAI,MAAM,WAAW,YAAY,SAAS,CAAC,EAAE,KAAK,GAAG;AAC3E;;;ACNA,IAAO,sBAAQ;AAAA,EACb,KAAK,CAAC,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC;AAAA,EAClC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,SAAS,CAAC;AAAA,EACpC,KAAK,CAAC,MAAM,IAAI;AAAA,EAChB,KAAK;AAAA,EACL,KAAK,CAAC,GAAG,MAAM,EAAE,cAAc,CAAC;AAAA,EAChC,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC;AAAA,EAC1B,KAAK,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC;AAAA,EAC9B,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,SAAS,CAAC;AAAA,EACpC,KAAK,CAAC,GAAG,MAAM,sBAAc,IAAI,KAAK,CAAC;AAAA,EACvC,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,EACnD,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE;AACvC;;;AClBe,SAAR,iBAAiB,GAAG;AACzB,SAAO;AACT;;;ACOA,IAAI,MAAM,MAAM,UAAU;AAA1B,IACI,WAAW,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG;AAEnE,SAAR,eAAiBC,SAAQ;AAC9B,MAAI,QAAQA,QAAO,aAAa,UAAaA,QAAO,cAAc,SAAY,mBAAW,oBAAY,IAAI,KAAKA,QAAO,UAAU,MAAM,GAAGA,QAAO,YAAY,EAAE,GACzJ,iBAAiBA,QAAO,aAAa,SAAY,KAAKA,QAAO,SAAS,CAAC,IAAI,IAC3E,iBAAiBA,QAAO,aAAa,SAAY,KAAKA,QAAO,SAAS,CAAC,IAAI,IAC3E,UAAUA,QAAO,YAAY,SAAY,MAAMA,QAAO,UAAU,IAChE,WAAWA,QAAO,aAAa,SAAY,mBAAW,uBAAe,IAAI,KAAKA,QAAO,UAAU,MAAM,CAAC,GACtG,UAAUA,QAAO,YAAY,SAAY,MAAMA,QAAO,UAAU,IAChE,QAAQA,QAAO,UAAU,SAAY,MAAMA,QAAO,QAAQ,IAC1D,MAAMA,QAAO,QAAQ,SAAY,QAAQA,QAAO,MAAM;AAE1D,WAAS,UAAU,WAAW;AAC5B,gBAAY,gBAAgB,SAAS;AAErC,QAAI,OAAO,UAAU,MACjB,QAAQ,UAAU,OAClB,OAAO,UAAU,MACjB,SAAS,UAAU,QACnB,OAAO,UAAU,MACjB,QAAQ,UAAU,OAClB,QAAQ,UAAU,OAClB,YAAY,UAAU,WACtB,OAAO,UAAU,MACjB,OAAO,UAAU;AAGrB,QAAI,SAAS;AAAK,cAAQ,MAAM,OAAO;AAAA,aAG9B,CAAC,oBAAY,IAAI;AAAG,oBAAc,WAAc,YAAY,KAAK,OAAO,MAAM,OAAO;AAG9F,QAAI,QAAS,SAAS,OAAO,UAAU;AAAM,aAAO,MAAM,OAAO,KAAK,QAAQ;AAI9E,QAAI,SAAS,WAAW,MAAM,iBAAiB,WAAW,OAAO,SAAS,KAAK,IAAI,IAAI,MAAM,KAAK,YAAY,IAAI,IAC9G,SAAS,WAAW,MAAM,iBAAiB,OAAO,KAAK,IAAI,IAAI,UAAU;AAK7E,QAAI,aAAa,oBAAY,IAAI,GAC7B,cAAc,aAAa,KAAK,IAAI;AAMxC,gBAAY,cAAc,SAAY,IAChC,SAAS,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,CAAC,IACzD,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,CAAC;AAEzC,aAASC,QAAO,OAAO;AACrB,UAAI,cAAc,QACd,cAAc,QACd,GAAG,GAAG;AAEV,UAAI,SAAS,KAAK;AAChB,sBAAc,WAAW,KAAK,IAAI;AAClC,gBAAQ;AAAA,MACV,OAAO;AACL,gBAAQ,CAAC;AAGT,YAAI,gBAAgB,QAAQ,KAAK,IAAI,QAAQ;AAG7C,gBAAQ,MAAM,KAAK,IAAI,MAAM,WAAW,KAAK,IAAI,KAAK,GAAG,SAAS;AAGlE,YAAI;AAAM,kBAAQ,mBAAW,KAAK;AAGlC,YAAI,iBAAiB,CAAC,UAAU,KAAK,SAAS;AAAK,0BAAgB;AAGnE,uBAAe,gBAAiB,SAAS,MAAM,OAAO,QAAS,SAAS,OAAO,SAAS,MAAM,KAAK,QAAQ;AAC3G,uBAAe,SAAS,MAAM,SAAS,IAAI,iBAAiB,CAAC,IAAI,MAAM,eAAe,iBAAiB,SAAS,MAAM,MAAM;AAI5H,YAAI,aAAa;AACf,cAAI,IAAI,IAAI,MAAM;AAClB,iBAAO,EAAE,IAAI,GAAG;AACd,gBAAI,IAAI,MAAM,WAAW,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI;AAC7C,6BAAe,MAAM,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC,IAAI,MAAM,MAAM,CAAC,KAAK;AAC3E,sBAAQ,MAAM,MAAM,GAAG,CAAC;AACxB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,SAAS,CAAC;AAAM,gBAAQ,MAAM,OAAO,QAAQ;AAGjD,UAAI,SAAS,YAAY,SAAS,MAAM,SAAS,YAAY,QACzD,UAAU,SAAS,QAAQ,IAAI,MAAM,QAAQ,SAAS,CAAC,EAAE,KAAK,IAAI,IAAI;AAG1E,UAAI,SAAS;AAAM,gBAAQ,MAAM,UAAU,OAAO,QAAQ,SAAS,QAAQ,YAAY,SAAS,QAAQ,GAAG,UAAU;AAGrH,cAAQ,OAAO;AAAA,QACb,KAAK;AAAK,kBAAQ,cAAc,QAAQ,cAAc;AAAS;AAAA,QAC/D,KAAK;AAAK,kBAAQ,cAAc,UAAU,QAAQ;AAAa;AAAA,QAC/D,KAAK;AAAK,kBAAQ,QAAQ,MAAM,GAAG,SAAS,QAAQ,UAAU,CAAC,IAAI,cAAc,QAAQ,cAAc,QAAQ,MAAM,MAAM;AAAG;AAAA,QAC9H;AAAS,kBAAQ,UAAU,cAAc,QAAQ;AAAa;AAAA,MAChE;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,IAAAA,QAAO,WAAW,WAAW;AAC3B,aAAO,YAAY;AAAA,IACrB;AAEA,WAAOA;AAAA,EACT;AAEA,WAASC,cAAa,WAAW,OAAO;AACtC,QAAI,IAAI,WAAW,YAAY,gBAAgB,SAAS,GAAG,UAAU,OAAO,KAAK,UAAU,GACvF,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,iBAAS,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,GACjE,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GACnB,SAAS,SAAS,IAAI,IAAI,CAAC;AAC/B,WAAO,SAASC,QAAO;AACrB,aAAO,EAAE,IAAIA,MAAK,IAAI;AAAA,IACxB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,cAAcD;AAAA,EAChB;AACF;;;ACjJA,IAAI;AACG,IAAI;AACJ,IAAI;AAEX,cAAc;AAAA,EACZ,WAAW;AAAA,EACX,UAAU,CAAC,CAAC;AAAA,EACZ,UAAU,CAAC,KAAK,EAAE;AACpB,CAAC;AAEc,SAAR,cAA+B,YAAY;AAChD,WAAS,eAAa,UAAU;AAChC,WAAS,OAAO;AAChB,iBAAe,OAAO;AACtB,SAAO;AACT;;;ACfe,SAAR,uBAAiB,MAAM;AAC5B,SAAO,KAAK,IAAI,GAAG,CAAC,iBAAS,KAAK,IAAI,IAAI,CAAC,CAAC;AAC9C;;;ACFe,SAAR,wBAAiB,MAAM,OAAO;AACnC,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,iBAAS,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,iBAAS,KAAK,IAAI,IAAI,CAAC,CAAC;AAC9G;;;ACFe,SAAR,uBAAiB,MAAM,KAAK;AACjC,SAAO,KAAK,IAAI,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,IAAI;AAC7C,SAAO,KAAK,IAAI,GAAG,iBAAS,GAAG,IAAI,iBAAS,IAAI,CAAC,IAAI;AACvD;;;ACFe,SAAR,WAA4B,OAAO,MAAM,OAAO,WAAW;AAChE,MAAI,OAAO,SAAS,OAAO,MAAM,KAAK,GAClC;AACJ,cAAY,gBAAgB,aAAa,OAAO,OAAO,SAAS;AAChE,UAAQ,UAAU,MAAM;AAAA,IACtB,KAAK,KAAK;AACR,UAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC;AACpD,UAAI,UAAU,aAAa,QAAQ,CAAC,MAAM,YAAY,wBAAgB,MAAM,KAAK,CAAC;AAAG,kBAAU,YAAY;AAC3G,aAAO,aAAa,WAAW,KAAK;AAAA,IACtC;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,KAAK;AACR,UAAI,UAAU,aAAa,QAAQ,CAAC,MAAM,YAAY,uBAAe,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC;AAAG,kBAAU,YAAY,aAAa,UAAU,SAAS;AAC9K;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,KAAK;AACR,UAAI,UAAU,aAAa,QAAQ,CAAC,MAAM,YAAY,uBAAe,IAAI,CAAC;AAAG,kBAAU,YAAY,aAAa,UAAU,SAAS,OAAO;AAC1I;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,SAAS;AACzB;;;ACvBO,SAAS,UAAU,OAAO;AAC/B,MAAI,SAAS,MAAM;AAEnB,QAAM,QAAQ,SAAS,OAAO;AAC5B,QAAI,IAAI,OAAO;AACf,WAAO,cAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,SAAS,OAAO,KAAK,KAAK;AAAA,EAChE;AAEA,QAAM,aAAa,SAAS,OAAO,WAAW;AAC5C,QAAI,IAAI,OAAO;AACf,WAAO,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,SAAS,OAAO,KAAK,OAAO,SAAS;AAAA,EAChF;AAEA,QAAM,OAAO,SAAS,OAAO;AAC3B,QAAI,SAAS;AAAM,cAAQ;AAE3B,QAAI,IAAI,OAAO;AACf,QAAI,KAAK;AACT,QAAI,KAAK,EAAE,SAAS;AACpB,QAAI,QAAQ,EAAE,EAAE;AAChB,QAAI,OAAO,EAAE,EAAE;AACf,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU;AAEd,QAAI,OAAO,OAAO;AAChB,aAAO,OAAO,QAAQ,MAAM,OAAO;AACnC,aAAO,IAAI,KAAK,IAAI,KAAK;AAAA,IAC3B;AAEA,WAAO,YAAY,GAAG;AACpB,aAAO,cAAc,OAAO,MAAM,KAAK;AACvC,UAAI,SAAS,SAAS;AACpB,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,eAAO,OAAO,CAAC;AAAA,MACjB,WAAW,OAAO,GAAG;AACnB,gBAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI;AACnC,eAAO,KAAK,KAAK,OAAO,IAAI,IAAI;AAAA,MAClC,WAAW,OAAO,GAAG;AACnB,gBAAQ,KAAK,KAAK,QAAQ,IAAI,IAAI;AAClC,eAAO,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,MACnC,OAAO;AACL;AAAA,MACF;AACA,gBAAU;AAAA,IACZ;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEe,SAAR,SAA0B;AAC/B,MAAI,QAAQ,WAAW;AAEvB,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,OAAO,CAAC;AAAA,EAC7B;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO,UAAU,KAAK;AACxB;;;AClEe,SAARE,UAA0B,QAAQ;AACvC,MAAI;AAEJ,WAAS,MAAM,GAAG;AAChB,WAAO,KAAK,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAI,UAAU;AAAA,EAChD;AAEA,QAAM,SAAS;AAEf,QAAM,SAAS,MAAM,QAAQ,SAAS,GAAG;AACvC,WAAO,UAAU,UAAU,SAAS,MAAM,KAAK,GAAG,MAAM,GAAG,SAAS,OAAO,MAAM;AAAA,EACnF;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAOA,UAAS,MAAM,EAAE,QAAQ,OAAO;AAAA,EACzC;AAEA,WAAS,UAAU,SAAS,MAAM,KAAK,QAAQ,MAAM,IAAI,CAAC,GAAG,CAAC;AAE9D,SAAO,UAAU,KAAK;AACxB;;;AC3Be,SAAR,KAAsB,QAAQ,UAAU;AAC7C,WAAS,OAAO,MAAM;AAEtB,MAAI,KAAK,GACL,KAAK,OAAO,SAAS,GACrB,KAAK,OAAO,EAAE,GACd,KAAK,OAAO,EAAE,GACd;AAEJ,MAAI,KAAK,IAAI;AACX,QAAI,IAAI,KAAK,IAAI,KAAK;AACtB,QAAI,IAAI,KAAK,IAAI,KAAK;AAAA,EACxB;AAEA,SAAO,EAAE,IAAI,SAAS,MAAM,EAAE;AAC9B,SAAO,EAAE,IAAI,SAAS,KAAK,EAAE;AAC7B,SAAO;AACT;;;ACXA,SAAS,aAAa,GAAG;AACvB,SAAO,KAAK,IAAI,CAAC;AACnB;AAEA,SAAS,aAAa,GAAG;AACvB,SAAO,KAAK,IAAI,CAAC;AACnB;AAEA,SAAS,cAAc,GAAG;AACxB,SAAO,CAAC,KAAK,IAAI,CAAC,CAAC;AACrB;AAEA,SAAS,cAAc,GAAG;AACxB,SAAO,CAAC,KAAK,IAAI,CAAC,CAAC;AACrB;AAEA,SAAS,MAAM,GAAG;AAChB,SAAO,SAAS,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,IAAI;AACjD;AAEA,SAAS,KAAK,MAAM;AAClB,SAAO,SAAS,KAAK,QACf,SAAS,KAAK,IAAI,KAAK,MACvB,OAAK,KAAK,IAAI,MAAM,CAAC;AAC7B;AAEA,SAAS,KAAK,MAAM;AAClB,SAAO,SAAS,KAAK,IAAI,KAAK,MACxB,SAAS,MAAM,KAAK,SACnB,SAAS,KAAK,KAAK,SAClB,OAAO,KAAK,IAAI,IAAI,GAAG,OAAK,KAAK,IAAI,CAAC,IAAI;AACpD;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC;AAC3B;AAEO,SAAS,QAAQ,WAAW;AACjC,QAAM,QAAQ,UAAU,cAAc,YAAY;AAClD,QAAM,SAAS,MAAM;AACrB,MAAI,OAAO;AACX,MAAI;AACJ,MAAI;AAEJ,WAAS,UAAU;AACjB,WAAO,KAAK,IAAI,GAAG,OAAO,KAAK,IAAI;AACnC,QAAI,OAAO,EAAE,CAAC,IAAI,GAAG;AACnB,aAAO,QAAQ,IAAI,GAAG,OAAO,QAAQ,IAAI;AACzC,gBAAU,eAAe,aAAa;AAAA,IACxC,OAAO;AACL,gBAAU,cAAc,YAAY;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,SAAS,GAAG;AACvB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,QAAQ,KAAK;AAAA,EACrD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,QAAQ,KAAK,OAAO;AAAA,EAC5D;AAEA,QAAM,QAAQ,WAAS;AACrB,UAAM,IAAI,OAAO;AACjB,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,EAAE,SAAS,CAAC;AACtB,UAAM,IAAI,IAAI;AAEd,QAAI;AAAG,MAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;AAEtB,QAAI,IAAI,KAAK,CAAC;AACd,QAAI,IAAI,KAAK,CAAC;AACd,QAAI;AACJ,QAAI;AACJ,UAAM,IAAI,SAAS,OAAO,KAAK,CAAC;AAChC,QAAI,IAAI,CAAC;AAET,QAAI,EAAE,OAAO,MAAM,IAAI,IAAI,GAAG;AAC5B,UAAI,KAAK,MAAM,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC;AAClC,UAAI,IAAI;AAAG,eAAO,KAAK,GAAG,EAAE,GAAG;AAC7B,eAAK,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AACzB,gBAAI,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC;AACrC,gBAAI,IAAI;AAAG;AACX,gBAAI,IAAI;AAAG;AACX,cAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AAAA;AAAO,eAAO,KAAK,GAAG,EAAE,GAAG;AACzB,eAAK,IAAI,OAAO,GAAG,KAAK,GAAG,EAAE,GAAG;AAC9B,gBAAI,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC;AACrC,gBAAI,IAAI;AAAG;AACX,gBAAI,IAAI;AAAG;AACX,cAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AACA,UAAI,EAAE,SAAS,IAAI;AAAG,YAAI,cAAM,GAAG,GAAG,CAAC;AAAA,IACzC,OAAO;AACL,UAAI,cAAM,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,IAAI;AAAA,IAC9C;AACA,WAAO,IAAI,EAAE,QAAQ,IAAI;AAAA,EAC3B;AAEA,QAAM,aAAa,CAAC,OAAO,cAAc;AACvC,QAAI,SAAS;AAAM,cAAQ;AAC3B,QAAI,aAAa;AAAM,kBAAY,SAAS,KAAK,MAAM;AACvD,QAAI,OAAO,cAAc,YAAY;AACnC,UAAI,EAAE,OAAO,OAAO,YAAY,gBAAgB,SAAS,GAAG,aAAa;AAAM,kBAAU,OAAO;AAChG,kBAAY,OAAO,SAAS;AAAA,IAC9B;AACA,QAAI,UAAU;AAAU,aAAO;AAC/B,UAAM,IAAI,KAAK,IAAI,GAAG,OAAO,QAAQ,MAAM,MAAM,EAAE,MAAM;AACzD,WAAO,OAAK;AACV,UAAI,IAAI,IAAI,KAAK,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC;AACpC,UAAI,IAAI,OAAO,OAAO;AAAK,aAAK;AAChC,aAAO,KAAK,IAAI,UAAU,CAAC,IAAI;AAAA,IACjC;AAAA,EACF;AAEA,QAAM,OAAO,MAAM;AACjB,WAAO,OAAO,KAAK,OAAO,GAAG;AAAA,MAC3B,OAAO,OAAK,KAAK,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC;AAAA,MACpC,MAAM,OAAK,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,IACpC,CAAC,CAAC;AAAA,EACJ;AAEA,SAAO;AACT;AAEe,SAAR,MAAuB;AAC5B,QAAM,QAAQ,QAAQ,YAAY,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AACnD,QAAM,OAAO,MAAM,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AACvD,YAAU,MAAM,OAAO,SAAS;AAChC,SAAO;AACT;;;ACvIA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,SAAS,GAAG;AACjB,WAAO,KAAK,KAAK,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,CAAC;AAAA,EAClD;AACF;AAEA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,SAAS,GAAG;AACjB,WAAO,KAAK,KAAK,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC,IAAI;AAAA,EAClD;AACF;AAEO,SAAS,UAAU,WAAW;AACnC,MAAI,IAAI,GAAG,QAAQ,UAAU,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,CAAC;AAEnE,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,SAAS,UAAU,gBAAgB,IAAI,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,IAAI;AAAA,EACrF;AAEA,SAAO,UAAU,KAAK;AACxB;AAEe,SAAR,SAA0B;AAC/B,MAAI,QAAQ,UAAU,YAAY,CAAC;AAEnC,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,OAAO,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EACxD;AAEA,SAAO,UAAU,MAAM,OAAO,SAAS;AACzC;;;AC9BA,SAAS,aAAa,UAAU;AAC9B,SAAO,SAAS,GAAG;AACjB,WAAO,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,IAAI,KAAK,IAAI,GAAG,QAAQ;AAAA,EAC/D;AACF;AAEA,SAAS,cAAc,GAAG;AACxB,SAAO,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC;AAC7C;AAEA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI;AAC9B;AAEO,SAAS,OAAO,WAAW;AAChC,MAAI,QAAQ,UAAU,UAAU,QAAQ,GACpC,WAAW;AAEf,WAAS,UAAU;AACjB,WAAO,aAAa,IAAI,UAAU,UAAU,QAAQ,IAC9C,aAAa,MAAM,UAAU,eAAe,eAAe,IAC3D,UAAU,aAAa,QAAQ,GAAG,aAAa,IAAI,QAAQ,CAAC;AAAA,EACpE;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,QAAQ,KAAK;AAAA,EACzD;AAEA,SAAO,UAAU,KAAK;AACxB;AAEe,SAAR,MAAuB;AAC5B,MAAI,QAAQ,OAAO,YAAY,CAAC;AAEhC,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,IAAI,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EACrD;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO;AACT;AAEO,SAAS,OAAO;AACrB,SAAO,IAAI,MAAM,MAAM,SAAS,EAAE,SAAS,GAAG;AAChD;;;AC5CA,SAAS,OAAO,GAAG;AACjB,SAAO,KAAK,KAAK,CAAC,IAAI,IAAI;AAC5B;AAEA,SAAS,SAAS,GAAG;AACnB,SAAO,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC;AAC7C;AAEe,SAAR,SAA0B;AAC/B,MAAI,UAAU,WAAW,GACrB,QAAQ,CAAC,GAAG,CAAC,GACb,QAAQ,OACR;AAEJ,WAAS,MAAM,GAAG;AAChB,QAAI,IAAI,SAAS,QAAQ,CAAC,CAAC;AAC3B,WAAO,MAAM,CAAC,IAAI,UAAU,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,EACtD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAAA,EACjC;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,QAAQ,OAAO,CAAC,GAAG,SAAS,QAAQ,OAAO;AAAA,EACxE;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,OAAO,QAAQ,MAAM,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,SAAS,MAAM,MAAM;AAAA,EAC9G;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,MAAM,MAAM,CAAC,EAAE,MAAM,IAAI;AAAA,EAClC;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,MAAM,CAAC,GAAG,SAAS,QAAQ,MAAM;AAAA,EACtE;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,OAAO,QAAQ,OAAO,GAAG,KAAK,EAChC,MAAM,KAAK,EACX,MAAM,QAAQ,MAAM,CAAC,EACrB,QAAQ,OAAO;AAAA,EACtB;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO,UAAU,KAAK;AACxB;;;AC3De,SAARC,YAA4B;AACjC,MAAI,SAAS,CAAC,GACV,QAAQ,CAAC,GACT,aAAa,CAAC,GACd;AAEJ,WAAS,UAAU;AACjB,QAAI,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM;AACvC,iBAAa,IAAI,MAAM,IAAI,CAAC;AAC5B,WAAO,EAAE,IAAI;AAAG,iBAAW,IAAI,CAAC,IAAI,eAAU,QAAQ,IAAI,CAAC;AAC3D,WAAO;AAAA,EACT;AAEA,WAAS,MAAM,GAAG;AAChB,WAAO,KAAK,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAI,UAAU,MAAM,eAAO,YAAY,CAAC,CAAC;AAAA,EAC3E;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,QAAI,IAAI,MAAM,QAAQ,CAAC;AACvB,WAAO,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI;AAAA,MAC1B,IAAI,IAAI,WAAW,IAAI,CAAC,IAAI,OAAO,CAAC;AAAA,MACpC,IAAI,WAAW,SAAS,WAAW,CAAC,IAAI,OAAO,OAAO,SAAS,CAAC;AAAA,IAClE;AAAA,EACF;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU;AAAQ,aAAO,OAAO,MAAM;AAC3C,aAAS,CAAC;AACV,aAAS,KAAK;AAAG,UAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;AAAG,eAAO,KAAK,CAAC;AAC/D,WAAO,KAAK,iBAAS;AACrB,WAAO,QAAQ;AAAA,EACjB;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM;AAAA,EAC7E;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,YAAY,WAAW;AAC3B,WAAO,WAAW,MAAM;AAAA,EAC1B;AAEA,QAAM,OAAO,WAAW;AACtB,WAAOA,UAAS,EACX,OAAO,MAAM,EACb,MAAM,KAAK,EACX,QAAQ,OAAO;AAAA,EACtB;AAEA,SAAO,UAAU,MAAM,OAAO,SAAS;AACzC;;;ACpDe,SAAR,WAA4B;AACjC,MAAI,KAAK,GACL,KAAK,GACL,IAAI,GACJ,SAAS,CAAC,GAAG,GACb,QAAQ,CAAC,GAAG,CAAC,GACb;AAEJ,WAAS,MAAM,GAAG;AAChB,WAAO,KAAK,QAAQ,KAAK,IAAI,MAAM,eAAO,QAAQ,GAAG,GAAG,CAAC,CAAC,IAAI;AAAA,EAChE;AAEA,WAAS,UAAU;AACjB,QAAI,IAAI;AACR,aAAS,IAAI,MAAM,CAAC;AACpB,WAAO,EAAE,IAAI;AAAG,aAAO,CAAC,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI;AACjE,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,EAAE;AAAA,EACnF;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,KAAK,QAAQ,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,QAAQ,KAAK,MAAM,MAAM;AAAA,EAC9F;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,QAAI,IAAI,MAAM,QAAQ,CAAC;AACvB,WAAO,IAAI,IAAI,CAAC,KAAK,GAAG,IAClB,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,IACtB,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,IAC3B,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,EACjC;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,aAAa,WAAW;AAC5B,WAAO,OAAO,MAAM;AAAA,EACtB;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,SAAS,EACX,OAAO,CAAC,IAAI,EAAE,CAAC,EACf,MAAM,KAAK,EACX,QAAQ,OAAO;AAAA,EACtB;AAEA,SAAO,UAAU,MAAM,UAAU,KAAK,GAAG,SAAS;AACpD;;;ACpDe,SAAR,YAA6B;AAClC,MAAI,SAAS,CAAC,GAAG,GACb,QAAQ,CAAC,GAAG,CAAC,GACb,SACA,IAAI;AAER,WAAS,MAAM,GAAG;AAChB,WAAO,KAAK,QAAQ,KAAK,IAAI,MAAM,eAAO,QAAQ,GAAG,GAAG,CAAC,CAAC,IAAI;AAAA,EAChE;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI,KAAK,IAAI,OAAO,QAAQ,MAAM,SAAS,CAAC,GAAG,SAAS,OAAO,MAAM;AAAA,EAC1H;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAI,KAAK,IAAI,OAAO,QAAQ,MAAM,SAAS,CAAC,GAAG,SAAS,MAAM,MAAM;AAAA,EACxH;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,QAAI,IAAI,MAAM,QAAQ,CAAC;AACvB,WAAO,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,EAClC;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,UAAU,EACZ,OAAO,MAAM,EACb,MAAM,KAAK,EACX,QAAQ,OAAO;AAAA,EACtB;AAEA,SAAO,UAAU,MAAM,OAAO,SAAS;AACzC;;;ACtCA,IAAM,KAAK,oBAAI;AAAf,IAAqB,KAAK,oBAAI;AAEvB,SAAS,aAAa,QAAQ,SAAS,OAAO,OAAO;AAE1D,WAAS,SAASC,OAAM;AACtB,WAAO,OAAOA,QAAO,UAAU,WAAW,IAAI,oBAAI,SAAO,oBAAI,KAAK,CAACA,KAAI,CAAC,GAAGA;AAAA,EAC7E;AAEA,WAAS,QAAQ,CAACA,UAAS;AACzB,WAAO,OAAOA,QAAO,oBAAI,KAAK,CAACA,KAAI,CAAC,GAAGA;AAAA,EACzC;AAEA,WAAS,OAAO,CAACA,UAAS;AACxB,WAAO,OAAOA,QAAO,IAAI,KAAKA,QAAO,CAAC,CAAC,GAAG,QAAQA,OAAM,CAAC,GAAG,OAAOA,KAAI,GAAGA;AAAA,EAC5E;AAEA,WAAS,QAAQ,CAACA,UAAS;AACzB,UAAM,KAAK,SAASA,KAAI,GAAG,KAAK,SAAS,KAAKA,KAAI;AAClD,WAAOA,QAAO,KAAK,KAAKA,QAAO,KAAK;AAAA,EACtC;AAEA,WAAS,SAAS,CAACA,OAAM,SAAS;AAChC,WAAO,QAAQA,QAAO,oBAAI,KAAK,CAACA,KAAI,GAAG,QAAQ,OAAO,IAAI,KAAK,MAAM,IAAI,CAAC,GAAGA;AAAA,EAC/E;AAEA,WAAS,QAAQ,CAAC,OAAO,MAAM,SAAS;AACtC,UAAM,QAAQ,CAAC;AACf,YAAQ,SAAS,KAAK,KAAK;AAC3B,WAAO,QAAQ,OAAO,IAAI,KAAK,MAAM,IAAI;AACzC,QAAI,EAAE,QAAQ,SAAS,EAAE,OAAO;AAAI,aAAO;AAC3C,QAAI;AACJ;AAAG,YAAM,KAAK,WAAW,oBAAI,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,OAAO,IAAI,GAAG,OAAO,KAAK;AAAA,WACvE,WAAW,SAAS,QAAQ;AACnC,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,CAAC,SAAS;AAC1B,WAAO,aAAa,CAACA,UAAS;AAC5B,UAAIA,SAAQA;AAAM,eAAO,OAAOA,KAAI,GAAG,CAAC,KAAKA,KAAI;AAAG,UAAAA,MAAK,QAAQA,QAAO,CAAC;AAAA,IAC3E,GAAG,CAACA,OAAM,SAAS;AACjB,UAAIA,SAAQA,OAAM;AAChB,YAAI,OAAO;AAAG,iBAAO,EAAE,QAAQ,GAAG;AAChC,mBAAO,QAAQA,OAAM,EAAE,GAAG,CAAC,KAAKA,KAAI,GAAG;AAAA,YAAC;AAAA,UAC1C;AAAA;AAAO,iBAAO,EAAE,QAAQ,GAAG;AACzB,mBAAO,QAAQA,OAAM,CAAE,GAAG,CAAC,KAAKA,KAAI,GAAG;AAAA,YAAC;AAAA,UAC1C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,OAAO;AACT,aAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,SAAG,QAAQ,CAAC,KAAK,GAAG,GAAG,QAAQ,CAAC,GAAG;AACnC,aAAO,EAAE,GAAG,OAAO,EAAE;AACrB,aAAO,KAAK,MAAM,MAAM,IAAI,EAAE,CAAC;AAAA,IACjC;AAEA,aAAS,QAAQ,CAAC,SAAS;AACzB,aAAO,KAAK,MAAM,IAAI;AACtB,aAAO,CAAC,SAAS,IAAI,KAAK,EAAE,OAAO,KAAK,OAClC,EAAE,OAAO,KAAK,WACd,SAAS,OAAO,QACZ,CAAC,MAAM,MAAM,CAAC,IAAI,SAAS,IAC3B,CAAC,MAAM,SAAS,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC;AAAA,IACpD;AAAA,EACF;AAEA,SAAO;AACT;;;AClEO,IAAM,cAAc,aAAa,MAAM;AAE9C,GAAG,CAACC,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,IAAI;AAC3B,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,MAAM;AACf,CAAC;AAGD,YAAY,QAAQ,CAAC,MAAM;AACzB,MAAI,KAAK,MAAM,CAAC;AAChB,MAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI;AAAI,WAAO;AACrC,MAAI,EAAE,IAAI;AAAI,WAAO;AACrB,SAAO,aAAa,CAACA,UAAS;AAC5B,IAAAA,MAAK,QAAQ,KAAK,MAAMA,QAAO,CAAC,IAAI,CAAC;AAAA,EACvC,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,CAAC;AAAA,EAC/B,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS;AAAA,EACzB,CAAC;AACH;AAEO,IAAM,eAAe,YAAY;;;ACxBjC,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,iBAAiB;AACxC,IAAM,eAAe,iBAAiB;AACtC,IAAM,cAAc,eAAe;AACnC,IAAM,eAAe,cAAc;AACnC,IAAM,gBAAgB,cAAc;AACpC,IAAM,eAAe,cAAc;;;ACHnC,IAAM,SAAS,aAAa,CAACC,UAAS;AAC3C,EAAAA,MAAK,QAAQA,QAAOA,MAAK,gBAAgB,CAAC;AAC5C,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,cAAc;AAC5B,CAAC;AAEM,IAAM,UAAU,OAAO;;;ACVvB,IAAM,aAAa,aAAa,CAACC,UAAS;AAC/C,EAAAA,MAAK,QAAQA,QAAOA,MAAK,gBAAgB,IAAIA,MAAK,WAAW,IAAI,cAAc;AACjF,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,WAAW;AACzB,CAAC;AAEM,IAAM,cAAc,WAAW;AAE/B,IAAM,YAAY,aAAa,CAACA,UAAS;AAC9C,EAAAA,MAAK,cAAc,GAAG,CAAC;AACzB,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,cAAc;AAC5B,CAAC;AAEM,IAAM,aAAa,UAAU;;;ACtB7B,IAAM,WAAW,aAAa,CAACC,UAAS;AAC7C,EAAAA,MAAK,QAAQA,QAAOA,MAAK,gBAAgB,IAAIA,MAAK,WAAW,IAAI,iBAAiBA,MAAK,WAAW,IAAI,cAAc;AACtH,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,YAAY;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,SAAS;AACvB,CAAC;AAEM,IAAM,YAAY,SAAS;AAE3B,IAAM,UAAU,aAAa,CAACA,UAAS;AAC5C,EAAAA,MAAK,cAAc,GAAG,GAAG,CAAC;AAC5B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,YAAY;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,YAAY;AAC1B,CAAC;AAEM,IAAM,WAAW,QAAQ;;;ACtBzB,IAAM,UAAU;AAAA,EACrB,CAAAC,UAAQA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAChC,CAACA,OAAM,SAASA,MAAK,QAAQA,MAAK,QAAQ,IAAI,IAAI;AAAA,EAClD,CAAC,OAAO,SAAS,MAAM,SAAS,IAAI,kBAAkB,IAAI,MAAM,kBAAkB,KAAK,kBAAkB;AAAA,EACzG,CAAAA,UAAQA,MAAK,QAAQ,IAAI;AAC3B;AAEO,IAAM,WAAW,QAAQ;AAEzB,IAAM,SAAS,aAAa,CAACA,UAAS;AAC3C,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,WAAWA,MAAK,WAAW,IAAI,IAAI;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,WAAW,IAAI;AAC7B,CAAC;AAEM,IAAM,UAAU,OAAO;AAEvB,IAAM,UAAU,aAAa,CAACA,UAAS;AAC5C,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,WAAWA,MAAK,WAAW,IAAI,IAAI;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAO,KAAK,MAAMA,QAAO,WAAW;AACtC,CAAC;AAEM,IAAM,WAAW,QAAQ;;;AC/BhC,SAAS,YAAY,GAAG;AACtB,SAAO,aAAa,CAACC,UAAS;AAC5B,IAAAA,MAAK,QAAQA,MAAK,QAAQ,KAAKA,MAAK,OAAO,IAAI,IAAI,KAAK,CAAC;AACzD,IAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,QAAQA,MAAK,QAAQ,IAAI,OAAO,CAAC;AAAA,EACxC,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS,IAAI,kBAAkB,IAAI,MAAM,kBAAkB,KAAK,kBAAkB;AAAA,EAClG,CAAC;AACH;AAEO,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,cAAc,YAAY,CAAC;AACjC,IAAM,gBAAgB,YAAY,CAAC;AACnC,IAAM,eAAe,YAAY,CAAC;AAClC,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,eAAe,YAAY,CAAC;AAElC,IAAM,cAAc,WAAW;AAC/B,IAAM,cAAc,WAAW;AAC/B,IAAM,eAAe,YAAY;AACjC,IAAM,iBAAiB,cAAc;AACrC,IAAM,gBAAgB,aAAa;AACnC,IAAM,cAAc,WAAW;AAC/B,IAAM,gBAAgB,aAAa;AAE1C,SAAS,WAAW,GAAG;AACrB,SAAO,aAAa,CAACA,UAAS;AAC5B,IAAAA,MAAK,WAAWA,MAAK,WAAW,KAAKA,MAAK,UAAU,IAAI,IAAI,KAAK,CAAC;AAClE,IAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAAA,EAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,WAAWA,MAAK,WAAW,IAAI,OAAO,CAAC;AAAA,EAC9C,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS;AAAA,EACzB,CAAC;AACH;AAEO,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,aAAa,WAAW,CAAC;AAC/B,IAAM,eAAe,WAAW,CAAC;AACjC,IAAM,cAAc,WAAW,CAAC;AAChC,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,cAAc,WAAW,CAAC;AAEhC,IAAM,aAAa,UAAU;AAC7B,IAAM,aAAa,UAAU;AAC7B,IAAM,cAAc,WAAW;AAC/B,IAAM,gBAAgB,aAAa;AACnC,IAAM,eAAe,YAAY;AACjC,IAAM,aAAa,UAAU;AAC7B,IAAM,eAAe,YAAY;;;ACrDjC,IAAM,YAAY,aAAa,CAACC,UAAS;AAC9C,EAAAA,MAAK,QAAQ,CAAC;AACd,EAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,SAASA,MAAK,SAAS,IAAI,IAAI;AACtC,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,SAAS,IAAI,MAAM,SAAS,KAAK,IAAI,YAAY,IAAI,MAAM,YAAY,KAAK;AACzF,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,SAAS;AACvB,CAAC;AAEM,IAAM,aAAa,UAAU;AAE7B,IAAM,WAAW,aAAa,CAACA,UAAS;AAC7C,EAAAA,MAAK,WAAW,CAAC;AACjB,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,YAAYA,MAAK,YAAY,IAAI,IAAI;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,YAAY,IAAI,MAAM,YAAY,KAAK,IAAI,eAAe,IAAI,MAAM,eAAe,KAAK;AACrG,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,YAAY;AAC1B,CAAC;AAEM,IAAM,YAAY,SAAS;;;ACxB3B,IAAM,WAAW,aAAa,CAACC,UAAS;AAC7C,EAAAA,MAAK,SAAS,GAAG,CAAC;AAClB,EAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,YAAYA,MAAK,YAAY,IAAI,IAAI;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,YAAY,IAAI,MAAM,YAAY;AAC/C,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,YAAY;AAC1B,CAAC;AAGD,SAAS,QAAQ,CAAC,MAAM;AACtB,SAAO,CAAC,SAAS,IAAI,KAAK,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,OAAO,aAAa,CAACA,UAAS;AAC9E,IAAAA,MAAK,YAAY,KAAK,MAAMA,MAAK,YAAY,IAAI,CAAC,IAAI,CAAC;AACvD,IAAAA,MAAK,SAAS,GAAG,CAAC;AAClB,IAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,YAAYA,MAAK,YAAY,IAAI,OAAO,CAAC;AAAA,EAChD,CAAC;AACH;AAEO,IAAM,YAAY,SAAS;AAE3B,IAAM,UAAU,aAAa,CAACA,UAAS;AAC5C,EAAAA,MAAK,YAAY,GAAG,CAAC;AACrB,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,eAAeA,MAAK,eAAe,IAAI,IAAI;AAClD,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,eAAe,IAAI,MAAM,eAAe;AACrD,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,eAAe;AAC7B,CAAC;AAGD,QAAQ,QAAQ,CAAC,MAAM;AACrB,SAAO,CAAC,SAAS,IAAI,KAAK,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,OAAO,aAAa,CAACA,UAAS;AAC9E,IAAAA,MAAK,eAAe,KAAK,MAAMA,MAAK,eAAe,IAAI,CAAC,IAAI,CAAC;AAC7D,IAAAA,MAAK,YAAY,GAAG,CAAC;AACrB,IAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAAA,EAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,eAAeA,MAAK,eAAe,IAAI,OAAO,CAAC;AAAA,EACtD,CAAC;AACH;AAEO,IAAM,WAAW,QAAQ;;;ACrChC,SAAS,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQ;AAEpD,QAAM,gBAAgB;AAAA,IACpB,CAAC,QAAS,GAAQ,cAAc;AAAA,IAChC,CAAC,QAAS,GAAI,IAAI,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAS,GAAQ,cAAc;AAAA,IAChC,CAAC,QAAS,GAAI,IAAI,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,IAChC,CAAG,MAAO,GAAI,IAAI,YAAc;AAAA,IAChC,CAAG,MAAO,GAAI,IAAI,YAAc;AAAA,IAChC,CAAG,MAAM,IAAI,KAAK,YAAc;AAAA,IAChC,CAAI,KAAM,GAAQ,WAAc;AAAA,IAChC,CAAI,KAAM,GAAI,IAAI,WAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,IAChC,CAAE,OAAQ,GAAQ,aAAc;AAAA,IAChC,CAAE,OAAQ,GAAI,IAAI,aAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,EAClC;AAEA,WAAS,MAAM,OAAO,MAAM,OAAO;AACjC,UAAM,UAAU,OAAO;AACvB,QAAI;AAAS,OAAC,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK;AACzC,UAAM,WAAW,SAAS,OAAO,MAAM,UAAU,aAAa,QAAQ,aAAa,OAAO,MAAM,KAAK;AACrG,UAAMC,SAAQ,WAAW,SAAS,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAC7D,WAAO,UAAUA,OAAM,QAAQ,IAAIA;AAAA,EACrC;AAEA,WAAS,aAAa,OAAO,MAAM,OAAO;AACxC,UAAM,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI;AACxC,UAAM,IAAI,iBAAS,CAAC,CAAC,EAAC,EAAEC,KAAI,MAAMA,KAAI,EAAE,MAAM,eAAe,MAAM;AACnE,QAAI,MAAM,cAAc;AAAQ,aAAO,KAAK,MAAM,SAAS,QAAQ,cAAc,OAAO,cAAc,KAAK,CAAC;AAC5G,QAAI,MAAM;AAAG,aAAO,YAAY,MAAM,KAAK,IAAI,SAAS,OAAO,MAAM,KAAK,GAAG,CAAC,CAAC;AAC/E,UAAM,CAAC,GAAG,IAAI,IAAI,cAAc,SAAS,cAAc,IAAI,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,EAAE,CAAC,IAAI,SAAS,IAAI,IAAI,CAAC;AAC3G,WAAO,EAAE,MAAM,IAAI;AAAA,EACrB;AAEA,SAAO,CAAC,OAAO,YAAY;AAC7B;AAEA,IAAM,CAAC,UAAU,eAAe,IAAI,OAAO,SAAS,UAAU,WAAW,SAAS,SAAS,SAAS;AACpG,IAAM,CAAC,WAAW,gBAAgB,IAAI,OAAO,UAAU,WAAW,YAAY,SAAS,UAAU,UAAU;;;AC1C3G,SAAS,UAAU,GAAG;AACpB,MAAI,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK;AACzB,QAAIC,QAAO,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACpD,IAAAA,MAAK,YAAY,EAAE,CAAC;AACpB,WAAOA;AAAA,EACT;AACA,SAAO,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnD;AAEA,SAAS,QAAQ,GAAG;AAClB,MAAI,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK;AACzB,QAAIA,QAAO,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC9D,IAAAA,MAAK,eAAe,EAAE,CAAC;AACvB,WAAOA;AAAA,EACT;AACA,SAAO,IAAI,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7D;AAEA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACxB,SAAO,EAAC,GAAM,GAAM,GAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC;AAClD;AAEe,SAAR,aAA8BC,SAAQ;AAC3C,MAAI,kBAAkBA,QAAO,UACzB,cAAcA,QAAO,MACrB,cAAcA,QAAO,MACrB,iBAAiBA,QAAO,SACxB,kBAAkBA,QAAO,MACzB,uBAAuBA,QAAO,WAC9B,gBAAgBA,QAAO,QACvB,qBAAqBA,QAAO;AAEhC,MAAI,WAAW,SAAS,cAAc,GAClC,eAAe,aAAa,cAAc,GAC1C,YAAY,SAAS,eAAe,GACpC,gBAAgB,aAAa,eAAe,GAC5C,iBAAiB,SAAS,oBAAoB,GAC9C,qBAAqB,aAAa,oBAAoB,GACtD,UAAU,SAAS,aAAa,GAChC,cAAc,aAAa,aAAa,GACxC,eAAe,SAAS,kBAAkB,GAC1C,mBAAmB,aAAa,kBAAkB;AAEtD,MAAI,UAAU;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,MAAI,aAAa;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,MAAI,SAAS;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAGA,UAAQ,IAAI,UAAU,aAAa,OAAO;AAC1C,UAAQ,IAAI,UAAU,aAAa,OAAO;AAC1C,UAAQ,IAAI,UAAU,iBAAiB,OAAO;AAC9C,aAAW,IAAI,UAAU,aAAa,UAAU;AAChD,aAAW,IAAI,UAAU,aAAa,UAAU;AAChD,aAAW,IAAI,UAAU,iBAAiB,UAAU;AAEpD,WAAS,UAAU,WAAWC,UAAS;AACrC,WAAO,SAASF,OAAM;AACpB,UAAI,SAAS,CAAC,GACV,IAAI,IACJ,IAAI,GACJ,IAAI,UAAU,QACd,GACAG,MACAC;AAEJ,UAAI,EAAEJ,iBAAgB;AAAO,QAAAA,QAAO,oBAAI,KAAK,CAACA,KAAI;AAElD,aAAO,EAAE,IAAI,GAAG;AACd,YAAI,UAAU,WAAW,CAAC,MAAM,IAAI;AAClC,iBAAO,KAAK,UAAU,MAAM,GAAG,CAAC,CAAC;AACjC,eAAKG,OAAM,KAAK,IAAI,UAAU,OAAO,EAAE,CAAC,CAAC,MAAM;AAAM,gBAAI,UAAU,OAAO,EAAE,CAAC;AAAA;AACxE,YAAAA,OAAM,MAAM,MAAM,MAAM;AAC7B,cAAIC,UAASF,SAAQ,CAAC;AAAG,gBAAIE,QAAOJ,OAAMG,IAAG;AAC7C,iBAAO,KAAK,CAAC;AACb,cAAI,IAAI;AAAA,QACV;AAAA,MACF;AAEA,aAAO,KAAK,UAAU,MAAM,GAAG,CAAC,CAAC;AACjC,aAAO,OAAO,KAAK,EAAE;AAAA,IACvB;AAAA,EACF;AAEA,WAAS,SAAS,WAAW,GAAG;AAC9B,WAAO,SAAS,QAAQ;AACtB,UAAI,IAAI,QAAQ,MAAM,QAAW,CAAC,GAC9B,IAAI,eAAe,GAAG,WAAW,UAAU,IAAI,CAAC,GAChD,MAAM;AACV,UAAI,KAAK,OAAO;AAAQ,eAAO;AAG/B,UAAI,OAAO;AAAG,eAAO,IAAI,KAAK,EAAE,CAAC;AACjC,UAAI,OAAO;AAAG,eAAO,IAAI,KAAK,EAAE,IAAI,OAAQ,OAAO,IAAI,EAAE,IAAI,EAAE;AAG/D,UAAI,KAAK,EAAE,OAAO;AAAI,UAAE,IAAI;AAG5B,UAAI,OAAO;AAAG,UAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI;AAGrC,UAAI,EAAE,MAAM;AAAW,UAAE,IAAI,OAAO,IAAI,EAAE,IAAI;AAG9C,UAAI,OAAO,GAAG;AACZ,YAAI,EAAE,IAAI,KAAK,EAAE,IAAI;AAAI,iBAAO;AAChC,YAAI,EAAE,OAAO;AAAI,YAAE,IAAI;AACvB,YAAI,OAAO,GAAG;AACZ,iBAAO,QAAQ,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,UAAU;AACzD,iBAAO,MAAM,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,IAAI;AACnE,iBAAO,OAAO,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC;AACxC,YAAE,IAAI,KAAK,eAAe;AAC1B,YAAE,IAAI,KAAK,YAAY;AACvB,YAAE,IAAI,KAAK,WAAW,KAAK,EAAE,IAAI,KAAK;AAAA,QACxC,OAAO;AACL,iBAAO,UAAU,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,OAAO;AACxD,iBAAO,MAAM,KAAK,QAAQ,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,IAAI;AACrE,iBAAO,QAAQ,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC;AACzC,YAAE,IAAI,KAAK,YAAY;AACvB,YAAE,IAAI,KAAK,SAAS;AACpB,YAAE,IAAI,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK;AAAA,QACrC;AAAA,MACF,WAAW,OAAO,KAAK,OAAO,GAAG;AAC/B,YAAI,EAAE,OAAO;AAAI,YAAE,IAAI,OAAO,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI;AAC3D,cAAM,OAAO,IAAI,QAAQ,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,UAAU,IAAI,UAAU,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO;AAChG,UAAE,IAAI;AACN,UAAE,IAAI,OAAO,KAAK,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,MAAM,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,MAAM,KAAK;AAAA,MACzF;AAIA,UAAI,OAAO,GAAG;AACZ,UAAE,KAAK,EAAE,IAAI,MAAM;AACnB,UAAE,KAAK,EAAE,IAAI;AACb,eAAO,QAAQ,CAAC;AAAA,MAClB;AAGA,aAAO,UAAU,CAAC;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,eAAe,GAAG,WAAW,QAAQ,GAAG;AAC/C,QAAI,IAAI,GACJ,IAAI,UAAU,QACd,IAAI,OAAO,QACX,GACA;AAEJ,WAAO,IAAI,GAAG;AACZ,UAAI,KAAK;AAAG,eAAO;AACnB,UAAI,UAAU,WAAW,GAAG;AAC5B,UAAI,MAAM,IAAI;AACZ,YAAI,UAAU,OAAO,GAAG;AACxB,gBAAQ,OAAO,KAAK,OAAO,UAAU,OAAO,GAAG,IAAI,CAAC;AACpD,YAAI,CAAC,UAAW,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK;AAAI,iBAAO;AAAA,MACxD,WAAW,KAAK,OAAO,WAAW,GAAG,GAAG;AACtC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,GAAG,QAAQ,GAAG;AACjC,QAAI,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC,CAAC;AACrC,WAAO,KAAK,EAAE,IAAI,aAAa,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EAC7E;AAEA,WAAS,kBAAkB,GAAG,QAAQ,GAAG;AACvC,QAAI,IAAI,eAAe,KAAK,OAAO,MAAM,CAAC,CAAC;AAC3C,WAAO,KAAK,EAAE,IAAI,mBAAmB,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EACnF;AAEA,WAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,QAAI,IAAI,UAAU,KAAK,OAAO,MAAM,CAAC,CAAC;AACtC,WAAO,KAAK,EAAE,IAAI,cAAc,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EAC9E;AAEA,WAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,QAAI,IAAI,aAAa,KAAK,OAAO,MAAM,CAAC,CAAC;AACzC,WAAO,KAAK,EAAE,IAAI,iBAAiB,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EACjF;AAEA,WAAS,WAAW,GAAG,QAAQ,GAAG;AAChC,QAAI,IAAI,QAAQ,KAAK,OAAO,MAAM,CAAC,CAAC;AACpC,WAAO,KAAK,EAAE,IAAI,YAAY,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EAC5E;AAEA,WAAS,oBAAoB,GAAG,QAAQ,GAAG;AACzC,WAAO,eAAe,GAAG,iBAAiB,QAAQ,CAAC;AAAA,EACrD;AAEA,WAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,WAAO,eAAe,GAAG,aAAa,QAAQ,CAAC;AAAA,EACjD;AAEA,WAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,WAAO,eAAe,GAAG,aAAa,QAAQ,CAAC;AAAA,EACjD;AAEA,WAAS,mBAAmB,GAAG;AAC7B,WAAO,qBAAqB,EAAE,OAAO,CAAC;AAAA,EACxC;AAEA,WAAS,cAAc,GAAG;AACxB,WAAO,gBAAgB,EAAE,OAAO,CAAC;AAAA,EACnC;AAEA,WAAS,iBAAiB,GAAG;AAC3B,WAAO,mBAAmB,EAAE,SAAS,CAAC;AAAA,EACxC;AAEA,WAAS,YAAY,GAAG;AACtB,WAAO,cAAc,EAAE,SAAS,CAAC;AAAA,EACnC;AAEA,WAAS,aAAa,GAAG;AACvB,WAAO,eAAe,EAAE,EAAE,SAAS,KAAK,GAAG;AAAA,EAC7C;AAEA,WAAS,cAAc,GAAG;AACxB,WAAO,IAAI,CAAC,EAAE,EAAE,SAAS,IAAI;AAAA,EAC/B;AAEA,WAAS,sBAAsB,GAAG;AAChC,WAAO,qBAAqB,EAAE,UAAU,CAAC;AAAA,EAC3C;AAEA,WAAS,iBAAiB,GAAG;AAC3B,WAAO,gBAAgB,EAAE,UAAU,CAAC;AAAA,EACtC;AAEA,WAAS,oBAAoB,GAAG;AAC9B,WAAO,mBAAmB,EAAE,YAAY,CAAC;AAAA,EAC3C;AAEA,WAAS,eAAe,GAAG;AACzB,WAAO,cAAc,EAAE,YAAY,CAAC;AAAA,EACtC;AAEA,WAAS,gBAAgB,GAAG;AAC1B,WAAO,eAAe,EAAE,EAAE,YAAY,KAAK,GAAG;AAAA,EAChD;AAEA,WAAS,iBAAiB,GAAG;AAC3B,WAAO,IAAI,CAAC,EAAE,EAAE,YAAY,IAAI;AAAA,EAClC;AAEA,SAAO;AAAA,IACL,QAAQ,SAAS,WAAW;AAC1B,UAAI,IAAI,UAAU,aAAa,IAAI,OAAO;AAC1C,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAAS,WAAW;AACzB,UAAI,IAAI,SAAS,aAAa,IAAI,KAAK;AACvC,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAS,WAAW;AAC7B,UAAI,IAAI,UAAU,aAAa,IAAI,UAAU;AAC7C,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,IACA,UAAU,SAAS,WAAW;AAC5B,UAAI,IAAI,SAAS,aAAa,IAAI,IAAI;AACtC,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAI,OAAO,EAAC,KAAK,IAAI,KAAK,KAAK,KAAK,IAAG;AAAvC,IACI,WAAW;AADf,IAEI,YAAY;AAFhB,IAGI,YAAY;AAEhB,SAAS,IAAI,OAAO,MAAM,OAAO;AAC/B,MAAI,OAAO,QAAQ,IAAI,MAAM,IACzB,UAAU,OAAO,CAAC,QAAQ,SAAS,IACnC,SAAS,OAAO;AACpB,SAAO,QAAQ,SAAS,QAAQ,IAAI,MAAM,QAAQ,SAAS,CAAC,EAAE,KAAK,IAAI,IAAI,SAAS;AACtF;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,EAAE,QAAQ,WAAW,MAAM;AACpC;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,IAAI,OAAO,SAAS,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,IAAI,KAAK,GAAG;AACpE;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,YAAY,GAAG,CAAC,CAAC,CAAC;AAChE;AAEA,SAAS,yBAAyB,GAAG,QAAQ,GAAG;AAC9C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,yBAAyB,GAAG,QAAQ,GAAG;AAC9C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,sBAAsB,GAAG,QAAQ,GAAG;AAC3C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,mBAAmB,GAAG,QAAQ,GAAG;AACxC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,sBAAsB,GAAG,QAAQ,GAAG;AAC3C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,cAAc,GAAG,QAAQ,GAAG;AACnC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,UAAU,GAAG,QAAQ,GAAG;AAC/B,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,MAAO,IAAI,EAAE,CAAC,EAAE,UAAU;AAC3E;AAEA,SAAS,UAAU,GAAG,QAAQ,GAAG;AAC/B,MAAI,IAAI,+BAA+B,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAClE,SAAO,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,QAAQ,IAAI,EAAE,CAAC,EAAE,UAAU;AAC5E;AAEA,SAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AACrD;AAEA,SAAS,iBAAiB,GAAG,QAAQ,GAAG;AACtC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AACjD;AAEA,SAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,eAAe,GAAG,QAAQ,GAAG;AACpC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AACvD;AAEA,SAAS,YAAY,GAAG,QAAQ,GAAG;AACjC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,kBAAkB,GAAG,QAAQ,GAAG;AACvC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,kBAAkB,GAAG,QAAQ,GAAG;AACvC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC,IAAI,GAAI,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAChE;AAEA,SAAS,oBAAoB,GAAG,QAAQ,GAAG;AACzC,MAAI,IAAI,UAAU,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC7C,SAAO,IAAI,IAAI,EAAE,CAAC,EAAE,SAAS;AAC/B;AAEA,SAAS,mBAAmB,GAAG,QAAQ,GAAG;AACxC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC,CAAC;AACrC,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,0BAA0B,GAAG,QAAQ,GAAG;AAC/C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC,CAAC;AACrC,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,IAAI,EAAE,QAAQ,GAAG,GAAG,CAAC;AAC9B;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,IAAI,EAAE,SAAS,GAAG,GAAG,CAAC;AAC/B;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,IAAI,EAAE,SAAS,IAAI,MAAM,IAAI,GAAG,CAAC;AAC1C;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,IAAI,IAAI,QAAQ,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACpD;AAEA,SAAS,mBAAmB,GAAG,GAAG;AAChC,SAAO,IAAI,EAAE,gBAAgB,GAAG,GAAG,CAAC;AACtC;AAEA,SAAS,mBAAmB,GAAG,GAAG;AAChC,SAAO,mBAAmB,GAAG,CAAC,IAAI;AACpC;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,SAAO,IAAI,EAAE,SAAS,IAAI,GAAG,GAAG,CAAC;AACnC;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,IAAI,EAAE,WAAW,GAAG,GAAG,CAAC;AACjC;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,IAAI,EAAE,WAAW,GAAG,GAAG,CAAC;AACjC;AAEA,SAAS,0BAA0B,GAAG;AACpC,MAAI,MAAM,EAAE,OAAO;AACnB,SAAO,QAAQ,IAAI,IAAI;AACzB;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,IAAI,WAAW,MAAM,SAAS,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACvD;AAEA,SAAS,KAAK,GAAG;AACf,MAAI,MAAM,EAAE,OAAO;AACnB,SAAQ,OAAO,KAAK,QAAQ,IAAK,aAAa,CAAC,IAAI,aAAa,KAAK,CAAC;AACxE;AAEA,SAAS,oBAAoB,GAAG,GAAG;AACjC,MAAI,KAAK,CAAC;AACV,SAAO,IAAI,aAAa,MAAM,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,EAAE,OAAO,MAAM,IAAI,GAAG,CAAC;AACpF;AAEA,SAAS,0BAA0B,GAAG;AACpC,SAAO,EAAE,OAAO;AAClB;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,IAAI,WAAW,MAAM,SAAS,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACvD;AAEA,SAAS,WAAW,GAAG,GAAG;AACxB,SAAO,IAAI,EAAE,YAAY,IAAI,KAAK,GAAG,CAAC;AACxC;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,KAAK,CAAC;AACV,SAAO,IAAI,EAAE,YAAY,IAAI,KAAK,GAAG,CAAC;AACxC;AAEA,SAAS,eAAe,GAAG,GAAG;AAC5B,SAAO,IAAI,EAAE,YAAY,IAAI,KAAO,GAAG,CAAC;AAC1C;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,MAAM,EAAE,OAAO;AACnB,MAAK,OAAO,KAAK,QAAQ,IAAK,aAAa,CAAC,IAAI,aAAa,KAAK,CAAC;AACnE,SAAO,IAAI,EAAE,YAAY,IAAI,KAAO,GAAG,CAAC;AAC1C;AAEA,SAAS,WAAW,GAAG;AACrB,MAAI,IAAI,EAAE,kBAAkB;AAC5B,UAAQ,IAAI,IAAI,OAAO,KAAK,IAAI,QAC1B,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,IACtB,IAAI,IAAI,IAAI,KAAK,CAAC;AAC1B;AAEA,SAAS,oBAAoB,GAAG,GAAG;AACjC,SAAO,IAAI,EAAE,WAAW,GAAG,GAAG,CAAC;AACjC;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,IAAI,EAAE,YAAY,GAAG,GAAG,CAAC;AAClC;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,IAAI,EAAE,YAAY,IAAI,MAAM,IAAI,GAAG,CAAC;AAC7C;AAEA,SAAS,mBAAmB,GAAG,GAAG;AAChC,SAAO,IAAI,IAAI,OAAO,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAClD;AAEA,SAAS,sBAAsB,GAAG,GAAG;AACnC,SAAO,IAAI,EAAE,mBAAmB,GAAG,GAAG,CAAC;AACzC;AAEA,SAAS,sBAAsB,GAAG,GAAG;AACnC,SAAO,sBAAsB,GAAG,CAAC,IAAI;AACvC;AAEA,SAAS,qBAAqB,GAAG,GAAG;AAClC,SAAO,IAAI,EAAE,YAAY,IAAI,GAAG,GAAG,CAAC;AACtC;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,IAAI,EAAE,cAAc,GAAG,GAAG,CAAC;AACpC;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,IAAI,EAAE,cAAc,GAAG,GAAG,CAAC;AACpC;AAEA,SAAS,6BAA6B,GAAG;AACvC,MAAI,MAAM,EAAE,UAAU;AACtB,SAAO,QAAQ,IAAI,IAAI;AACzB;AAEA,SAAS,0BAA0B,GAAG,GAAG;AACvC,SAAO,IAAI,UAAU,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACrD;AAEA,SAAS,QAAQ,GAAG;AAClB,MAAI,MAAM,EAAE,UAAU;AACtB,SAAQ,OAAO,KAAK,QAAQ,IAAK,YAAY,CAAC,IAAI,YAAY,KAAK,CAAC;AACtE;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,MAAI,QAAQ,CAAC;AACb,SAAO,IAAI,YAAY,MAAM,QAAQ,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,IAAI,GAAG,CAAC;AACpF;AAEA,SAAS,6BAA6B,GAAG;AACvC,SAAO,EAAE,UAAU;AACrB;AAEA,SAAS,0BAA0B,GAAG,GAAG;AACvC,SAAO,IAAI,UAAU,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACrD;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,IAAI,EAAE,eAAe,IAAI,KAAK,GAAG,CAAC;AAC3C;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,MAAI,QAAQ,CAAC;AACb,SAAO,IAAI,EAAE,eAAe,IAAI,KAAK,GAAG,CAAC;AAC3C;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,SAAO,IAAI,EAAE,eAAe,IAAI,KAAO,GAAG,CAAC;AAC7C;AAEA,SAAS,qBAAqB,GAAG,GAAG;AAClC,MAAI,MAAM,EAAE,UAAU;AACtB,MAAK,OAAO,KAAK,QAAQ,IAAK,YAAY,CAAC,IAAI,YAAY,KAAK,CAAC;AACjE,SAAO,IAAI,EAAE,eAAe,IAAI,KAAO,GAAG,CAAC;AAC7C;AAEA,SAAS,gBAAgB;AACvB,SAAO;AACT;AAEA,SAAS,uBAAuB;AAC9B,SAAO;AACT;AAEA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,CAAC;AACV;AAEA,SAAS,2BAA2B,GAAG;AACrC,SAAO,KAAK,MAAM,CAAC,IAAI,GAAI;AAC7B;;;ACtrBA,IAAIE;AACG,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEXC,eAAc;AAAA,EACZ,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS,CAAC,MAAM,IAAI;AAAA,EACpB,MAAM,CAAC,UAAU,UAAU,WAAW,aAAa,YAAY,UAAU,UAAU;AAAA,EACnF,WAAW,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3D,QAAQ,CAAC,WAAW,YAAY,SAAS,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU;AAAA,EACjI,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAClG,CAAC;AAEc,SAARA,eAA+B,YAAY;AAChD,EAAAD,UAAS,aAAa,UAAU;AAChC,eAAaA,QAAO;AACpB,cAAYA,QAAO;AACnB,cAAYA,QAAO;AACnB,aAAWA,QAAO;AAClB,SAAOA;AACT;;;ACxBO,IAAI,eAAe;AAE1B,SAAS,gBAAgBE,OAAM;AAC7B,SAAOA,MAAK,YAAY;AAC1B;AAEA,IAAI,YAAY,KAAK,UAAU,cACzB,kBACA,UAAU,YAAY;;;ACP5B,SAAS,eAAe,QAAQ;AAC9B,MAAIC,QAAO,IAAI,KAAK,MAAM;AAC1B,SAAO,MAAMA,KAAI,IAAI,OAAOA;AAC9B;AAEA,IAAI,WAAW,CAAC,oBAAI,KAAK,0BAA0B,IAC7C,iBACA,SAAS,YAAY;;;ACJ3B,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC;AACnB;AAEA,SAASC,QAAO,GAAG;AACjB,SAAO,aAAa,OAAO,CAAC,IAAI,CAAC,oBAAI,KAAK,CAAC,CAAC;AAC9C;AAEO,SAAS,SAAS,OAAO,cAAc,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQC,SAAQC,SAAQ;AAClG,MAAI,QAAQ,WAAW,GACnB,SAAS,MAAM,QACf,SAAS,MAAM;AAEnB,MAAI,oBAAoBA,QAAO,KAAK,GAChC,eAAeA,QAAO,KAAK,GAC3B,eAAeA,QAAO,OAAO,GAC7B,aAAaA,QAAO,OAAO,GAC3B,YAAYA,QAAO,OAAO,GAC1B,aAAaA,QAAO,OAAO,GAC3B,cAAcA,QAAO,IAAI,GACzBC,cAAaD,QAAO,IAAI;AAE5B,WAASE,YAAWC,OAAM;AACxB,YAAQJ,QAAOI,KAAI,IAAIA,QAAO,oBACxB,OAAOA,KAAI,IAAIA,QAAO,eACtB,KAAKA,KAAI,IAAIA,QAAO,eACpB,IAAIA,KAAI,IAAIA,QAAO,aACnB,MAAMA,KAAI,IAAIA,QAAQ,KAAKA,KAAI,IAAIA,QAAO,YAAY,aACtD,KAAKA,KAAI,IAAIA,QAAO,cACpBF,aAAYE,KAAI;AAAA,EACxB;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,IAAI,KAAK,OAAO,CAAC,CAAC;AAAA,EAC3B;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,SAAS,OAAO,MAAM,KAAK,GAAGL,OAAM,CAAC,IAAI,OAAO,EAAE,IAAI,IAAI;AAAA,EAC7E;AAEA,QAAM,QAAQ,SAAS,UAAU;AAC/B,QAAI,IAAI,OAAO;AACf,WAAO,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,YAAY,OAAO,KAAK,QAAQ;AAAA,EACtE;AAEA,QAAM,aAAa,SAAS,OAAO,WAAW;AAC5C,WAAO,aAAa,OAAOI,cAAaF,QAAO,SAAS;AAAA,EAC1D;AAEA,QAAM,OAAO,SAAS,UAAU;AAC9B,QAAI,IAAI,OAAO;AACf,QAAI,CAAC,YAAY,OAAO,SAAS,UAAU;AAAY,iBAAW,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,YAAY,OAAO,KAAK,QAAQ;AACtI,WAAO,WAAW,OAAO,KAAK,GAAG,QAAQ,CAAC,IAAI;AAAA,EAChD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,SAAS,OAAO,cAAc,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQD,SAAQC,OAAM,CAAC;AAAA,EACxG;AAEA,SAAO;AACT;AAEe,SAAR,OAAwB;AAC7B,SAAO,UAAU,MAAM,SAAS,WAAW,kBAAkB,UAAU,WAAW,YAAU,SAAS,UAAU,YAAY,QAAY,UAAU,EAAE,OAAO,CAAC,IAAI,KAAK,KAAM,GAAG,CAAC,GAAG,IAAI,KAAK,KAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS;AACpN;;;ACjEe,SAAR,UAA2B;AAChC,SAAO,UAAU,MAAM,SAAS,UAAU,iBAAiB,SAAS,UAAU,WAAS,QAAQ,SAAS,WAAW,QAAW,SAAS,EAAE,OAAO,CAAC,KAAK,IAAI,KAAM,GAAG,CAAC,GAAG,KAAK,IAAI,KAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS;AAC1M;;;ACCA,SAASI,eAAc;AACrB,MAAI,KAAK,GACL,KAAK,GACLC,KACAC,KACA,KACA,WACA,eAAe,UACf,QAAQ,OACR;AAEJ,WAAS,MAAM,GAAG;AAChB,WAAO,KAAK,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAI,UAAU,aAAa,QAAQ,IAAI,OAAO,KAAK,UAAU,CAAC,IAAID,OAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE;AAAA,EACvJ;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAGA,MAAK,UAAU,KAAK,CAAC,EAAE,GAAGC,MAAK,UAAU,KAAK,CAAC,EAAE,GAAG,MAAMD,QAAOC,MAAK,IAAI,KAAKA,MAAKD,MAAK,SAAS,CAAC,IAAI,EAAE;AAAA,EACpJ;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,GAAG,SAAS;AAAA,EACxD;AAEA,WAAS,MAAM,aAAa;AAC1B,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI;AACR,aAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,eAAe,YAAY,IAAI,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;AAAA,IACzH;AAAA,EACF;AAEA,QAAM,QAAQ,MAAM,aAAW;AAE/B,QAAM,aAAa,MAAM,aAAgB;AAEzC,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,SAAO,SAAS,GAAG;AACjB,gBAAY,GAAGA,MAAK,EAAE,EAAE,GAAGC,MAAK,EAAE,EAAE,GAAG,MAAMD,QAAOC,MAAK,IAAI,KAAKA,MAAKD;AACvE,WAAO;AAAA,EACT;AACF;AAEO,SAASE,MAAK,QAAQ,QAAQ;AACnC,SAAO,OACF,OAAO,OAAO,OAAO,CAAC,EACtB,aAAa,OAAO,aAAa,CAAC,EAClC,MAAM,OAAO,MAAM,CAAC,EACpB,QAAQ,OAAO,QAAQ,CAAC;AAC/B;AAEe,SAAR,aAA8B;AACnC,MAAI,QAAQ,UAAUH,aAAY,EAAE,QAAQ,CAAC;AAE7C,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,WAAW,CAAC;AAAA,EACjC;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,gBAAgB;AAC9B,MAAI,QAAQ,QAAQH,aAAY,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AAEjD,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,cAAc,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,EACvD;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,mBAAmB;AACjC,MAAI,QAAQ,UAAUH,aAAY,CAAC;AAEnC,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,iBAAiB,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EAClE;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,gBAAgB;AAC9B,MAAI,QAAQ,OAAOH,aAAY,CAAC;AAEhC,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,cAAc,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EAC/D;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,iBAAiB;AAC/B,SAAO,cAAc,MAAM,MAAM,SAAS,EAAE,SAAS,GAAG;AAC1D;;;ACtGe,SAAR,qBAAsC;AAC3C,MAAI,SAAS,CAAC,GACV,eAAe;AAEnB,WAAS,MAAM,GAAG;AAChB,QAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;AAAG,aAAO,cAAc,eAAO,QAAQ,GAAG,CAAC,IAAI,MAAM,OAAO,SAAS,EAAE;AAAA,EACvG;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU;AAAQ,aAAO,OAAO,MAAM;AAC3C,aAAS,CAAC;AACV,aAAS,KAAK;AAAG,UAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;AAAG,eAAO,KAAK,CAAC;AAC/D,WAAO,KAAK,iBAAS;AACrB,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,GAAG,SAAS;AAAA,EACxD;AAEA,QAAM,QAAQ,WAAW;AACvB,WAAO,OAAO,IAAI,CAAC,GAAG,MAAM,aAAa,KAAK,OAAO,SAAS,EAAE,CAAC;AAAA,EACnE;AAEA,QAAM,YAAY,SAAS,GAAG;AAC5B,WAAO,MAAM,KAAK,EAAC,QAAQ,IAAI,EAAC,GAAG,CAAC,GAAG,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC;AAAA,EACtE;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,mBAAmB,YAAY,EAAE,OAAO,MAAM;AAAA,EACvD;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;;;AC5BA,SAASC,eAAc;AACrB,MAAI,KAAK,GACL,KAAK,KACL,KAAK,GACL,IAAI,GACJC,KACAC,KACA,IACA,KACA,KACA,eAAe,UACf,WACA,QAAQ,OACR;AAEJ,WAAS,MAAM,GAAG;AAChB,WAAO,MAAM,IAAI,CAAC,CAAC,IAAI,WAAW,IAAI,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAKA,QAAO,IAAI,IAAI,IAAIA,MAAK,MAAM,MAAM,aAAa,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AAAA,EAC7J;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,CAAC,IAAI,IAAI,EAAE,IAAI,GAAGD,MAAK,UAAU,KAAK,CAAC,EAAE,GAAGC,MAAK,UAAU,KAAK,CAAC,EAAE,GAAG,KAAK,UAAU,KAAK,CAAC,EAAE,GAAG,MAAMD,QAAOC,MAAK,IAAI,OAAOA,MAAKD,MAAK,MAAMC,QAAO,KAAK,IAAI,OAAO,KAAKA,MAAK,IAAIA,MAAKD,MAAK,KAAK,GAAG,SAAS,CAAC,IAAI,IAAI,EAAE;AAAA,EACrP;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,GAAG,SAAS;AAAA,EACxD;AAEA,WAAS,MAAM,aAAa;AAC1B,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI,IAAI;AACZ,aAAO,UAAU,UAAU,CAAC,IAAI,IAAI,EAAE,IAAI,GAAG,eAAe,UAAU,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,CAAC;AAAA,IACjK;AAAA,EACF;AAEA,QAAM,QAAQ,MAAM,aAAW;AAE/B,QAAM,aAAa,MAAM,aAAgB;AAEzC,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,SAAO,SAAS,GAAG;AACjB,gBAAY,GAAGA,MAAK,EAAE,EAAE,GAAGC,MAAK,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,MAAMD,QAAOC,MAAK,IAAI,OAAOA,MAAKD,MAAK,MAAMC,QAAO,KAAK,IAAI,OAAO,KAAKA,MAAK,IAAIA,MAAKD,MAAK,KAAK;AACpJ,WAAO;AAAA,EACT;AACF;AAEe,SAAR,YAA6B;AAClC,MAAI,QAAQ,UAAUD,aAAY,EAAE,QAAQ,CAAC;AAE7C,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,UAAU,CAAC;AAAA,EAChC;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,eAAe;AAC7B,MAAI,QAAQ,QAAQH,aAAY,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;AAEtD,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,aAAa,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,EACtD;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,kBAAkB;AAChC,MAAI,QAAQ,UAAUH,aAAY,CAAC;AAEnC,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,gBAAgB,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EACjE;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,eAAe;AAC7B,MAAI,QAAQ,OAAOH,aAAY,CAAC;AAEhC,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,aAAa,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EAC9D;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,gBAAgB;AAC9B,SAAO,aAAa,MAAM,MAAM,SAAS,EAAE,SAAS,GAAG;AACzD;", "names": ["copy", "i", "piecewise", "locale", "format", "formatPrefix", "value", "identity", "quantile", "date", "date", "date", "date", "date", "date", "date", "date", "date", "ticks", "step", "date", "locale", "formats", "pad", "format", "locale", "defaultLocale", "date", "date", "number", "second", "format", "formatYear", "tickFormat", "date", "transformer", "t0", "t1", "copy", "transformer", "t0", "t1", "copy"]}