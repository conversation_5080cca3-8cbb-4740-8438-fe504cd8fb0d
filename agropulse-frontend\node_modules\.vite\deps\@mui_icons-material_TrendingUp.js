"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-YKCWYOVU.js";
import "./chunk-HE35I2H7.js";
import "./chunk-6EVIHVLY.js";
import {
  require_jsx_runtime
} from "./chunk-TOXUORXJ.js";
import "./chunk-K5YNGTCN.js";
import {
  __toESM
} from "./chunk-ZC22LKFR.js";

// node_modules/@mui/icons-material/esm/TrendingUp.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var TrendingUp_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"
}), "TrendingUp");
export {
  TrendingUp_default as default
};
//# sourceMappingURL=@mui_icons-material_TrendingUp.js.map
