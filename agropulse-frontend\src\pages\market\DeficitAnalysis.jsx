import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js'
import { Bar, Pie } from 'react-chartjs-2'

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, BarElement, Title, Tooltip, Legend, ArcElement)

// Icons
import { 
  ArrowLeftIcon,
  GlobeAltIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  MapIcon
} from '@heroicons/react/24/outline'

const DeficitAnalysis = () => {
  const { t } = useTranslation()
  
  const [deficitData, setDeficitData] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  
  // Fetch deficit data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // Mock data for deficit analysis
        const mockDeficitData = [
          { country: 'Egypt', product: 'Wheat', deficit_percentage: 35, estimated_demand: 12000000, population: 104000000 },
          { country: 'Saudi Arabia', product: 'Rice', deficit_percentage: 28, estimated_demand: 8500000, population: 35000000 },
          { country: 'Nigeria', product: 'Corn', deficit_percentage: 42, estimated_demand: 15000000, population: 218000000 },
          { country: 'UAE', product: 'Fruits', deficit_percentage: 65, estimated_demand: 3500000, population: 10000000 },
          { country: 'Kenya', product: 'Coffee', deficit_percentage: 15, estimated_demand: 2000000, population: 54000000 },
          { country: 'Algeria', product: 'Wheat', deficit_percentage: 45, estimated_demand: 8000000, population: 44000000 },
          { country: 'Morocco', product: 'Corn', deficit_percentage: 32, estimated_demand: 6500000, population: 37000000 },
          { country: 'Jordan', product: 'Rice', deficit_percentage: 55, estimated_demand: 1200000, population: 11000000 }
        ]
        
        setDeficitData(mockDeficitData)
      } catch (error) {
        console.error('Error fetching deficit data:', error)
        setError(t('common.error'))
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [t])

  // Prepare chart data
  const chartData = {
    labels: deficitData.map(item => item.country),
    datasets: [
      {
        label: t('home.countryDeficit.deficit') + ' %',
        data: deficitData.map(item => item.deficit_percentage),
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 101, 101, 0.8)',
          'rgba(248, 113, 113, 0.8)',
          'rgba(252, 165, 165, 0.8)',
          'rgba(254, 202, 202, 0.8)',
          'rgba(239, 68, 68, 0.6)',
          'rgba(245, 101, 101, 0.6)',
          'rgba(248, 113, 113, 0.6)'
        ],
        borderColor: [
          'rgba(239, 68, 68, 1)',
          'rgba(245, 101, 101, 1)',
          'rgba(248, 113, 113, 1)',
          'rgba(252, 165, 165, 1)',
          'rgba(254, 202, 202, 1)',
          'rgba(239, 68, 68, 1)',
          'rgba(245, 101, 101, 1)',
          'rgba(248, 113, 113, 1)'
        ],
        borderWidth: 1
      }
    ]
  }

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top'
      },
      title: {
        display: true,
        text: t('home.countryDeficit.title')
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value) {
            return value + '%'
          }
        }
      }
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                to="/"
                className="flex items-center text-gray-500 hover:text-gray-700 mr-4"
              >
                <ArrowLeftIcon className="h-5 w-5 mr-2" />
                {t('common.back')}
              </Link>
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-500 mr-3" />
                <h1 className="text-2xl font-bold text-gray-900">
                  {t('home.countryDeficit.title')}
                </h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <GlobeAltIcon className="h-8 w-8 text-red-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-500">Total Countries</p>
                <p className="text-2xl font-bold text-gray-900">{deficitData.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 text-orange-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-500">Average Deficit</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.round(deficitData.reduce((sum, item) => sum + item.deficit_percentage, 0) / deficitData.length)}%
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <MapIcon className="h-8 w-8 text-blue-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-500">High Risk Countries</p>
                <p className="text-2xl font-bold text-gray-900">
                  {deficitData.filter(item => item.deficit_percentage > 40).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Chart */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            {t('home.countryDeficit.title')} - Chart Analysis
          </h2>
          <div className="h-96">
            <Bar data={chartData} options={chartOptions} />
          </div>
        </div>

        {/* Detailed Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">
              Detailed Deficit Analysis
            </h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Country
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deficit %
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Estimated Demand
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Population
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {deficitData.map((deficit, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {deficit.country}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{deficit.product}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        deficit.deficit_percentage > 50
                          ? 'bg-red-100 text-red-800'
                          : deficit.deficit_percentage > 30
                          ? 'bg-orange-100 text-orange-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {deficit.deficit_percentage}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {(deficit.estimated_demand / 1000000).toFixed(1)}M {t('home.countryDeficit.tons')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {(deficit.population / 1000000).toFixed(1)}M
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link
                        to={`/market/country/${deficit.country}`}
                        className="text-green-600 hover:text-green-900 mr-4"
                      >
                        {t('common.viewDetails')}
                      </Link>
                      <Link
                        to={`/market/product/${deficit.product}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View Product
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Trade Opportunities */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            {t('home.countryDeficit.opportunity')} Insights
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {deficitData.filter(item => item.deficit_percentage > 40).map((deficit, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-lg text-gray-900 mb-2">
                  {deficit.country} - {deficit.product}
                </h3>
                <p className="text-sm text-gray-600 mb-3">
                  High deficit of {deficit.deficit_percentage}% presents significant trade opportunity
                </p>
                <div className="flex space-x-2">
                  <Link
                    to={`/market/country/${deficit.country}`}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                  >
                    Explore Market
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DeficitAnalysis