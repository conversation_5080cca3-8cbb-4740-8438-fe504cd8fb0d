"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-NK322AVL.js";
import "./chunk-HE35I2H7.js";
import {
  require_jsx_runtime
} from "./chunk-TOXUORXJ.js";
import "./chunk-6EVIHVLY.js";
import "./chunk-K5YNGTCN.js";
import {
  __toESM
} from "./chunk-ZC22LKFR.js";

// node_modules/@mui/icons-material/esm/Analytics.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Analytics_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-5h2zm4 0h-2v-3h2zm0-5h-2v-2h2zm4 5h-2V7h2z"
}), "Analytics");
export {
  Analytics_default as default
};
//# sourceMappingURL=@mui_icons-material_Analytics.js.map
