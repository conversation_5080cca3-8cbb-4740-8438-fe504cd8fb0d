{"version": 3, "sources": ["../../react-tooltip/dist/react-tooltip.min.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.mjs", "../../@floating-ui/core/dist/floating-ui.core.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../@floating-ui/dom/dist/floating-ui.dom.mjs"], "sourcesContent": ["/*\n* React Tooltip\n* {@link https://github.com/ReactTooltip/react-tooltip}\n* @copyright ReactTooltip Team\n* @license MIT\n*/\nimport e,{useLayoutEffect as t,useEffect as o,createContext as l,useState as r,useCallback as n,useMemo as i,useContext as c,useRef as s,useImperativeHandle as a}from\"react\";import{arrow as u,computePosition as d,offset as p,flip as v,shift as m,autoUpdate as f}from\"@floating-ui/dom\";import y from\"classnames\";const h=\"react-tooltip-core-styles\",w=\"react-tooltip-base-styles\",b={core:!1,base:!1};function S({css:e,id:t=w,type:o=\"base\",ref:l}){var r,n;if(!e||\"undefined\"==typeof document||b[o])return;if(\"core\"===o&&\"undefined\"!=typeof process&&(null===(r=null===process||void 0===process?void 0:process.env)||void 0===r?void 0:r.REACT_TOOLTIP_DISABLE_CORE_STYLES))return;if(\"base\"!==o&&\"undefined\"!=typeof process&&(null===(n=null===process||void 0===process?void 0:process.env)||void 0===n?void 0:n.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;\"core\"===o&&(t=h),l||(l={});const{insertAt:i}=l;if(document.getElementById(t))return;const c=document.head||document.getElementsByTagName(\"head\")[0],s=document.createElement(\"style\");s.id=t,s.type=\"text/css\",\"top\"===i&&c.firstChild?c.insertBefore(s,c.firstChild):c.appendChild(s),s.styleSheet?s.styleSheet.cssText=e:s.appendChild(document.createTextNode(e)),b[o]=!0}function g({type:e=\"base\",id:t=w}={}){if(!b[e])return;\"core\"===e&&(t=h);const o=document.getElementById(t);\"style\"===(null==o?void 0:o.tagName)?null==o||o.remove():console.warn(`[react-tooltip] Failed to remove 'style' element with id '${t}'. Call \\`injectStyle()\\` first`),b[e]=!1}const E=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:o=null,place:l=\"top\",offset:r=10,strategy:n=\"absolute\",middlewares:i=[p(Number(r)),v({fallbackAxisSideDirection:\"start\"}),m({padding:5})],border:c})=>{if(!e)return{tooltipStyles:{},tooltipArrowStyles:{},place:l};if(null===t)return{tooltipStyles:{},tooltipArrowStyles:{},place:l};const s=i;return o?(s.push(u({element:o,padding:5})),d(e,t,{placement:l,strategy:n,middleware:s}).then((({x:e,y:t,placement:o,middlewareData:l})=>{var r,n;const i={left:`${e}px`,top:`${t}px`,border:c},{x:s,y:a}=null!==(r=l.arrow)&&void 0!==r?r:{x:0,y:0},u=null!==(n={top:\"bottom\",right:\"left\",bottom:\"top\",left:\"right\"}[o.split(\"-\")[0]])&&void 0!==n?n:\"bottom\",d=c&&{borderBottom:c,borderRight:c};let p=0;if(c){const e=`${c}`.match(/(\\d+)px/);p=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:i,tooltipArrowStyles:{left:null!=s?`${s}px`:\"\",top:null!=a?`${a}px`:\"\",right:\"\",bottom:\"\",...d,[u]:`-${4+p}px`},place:o}}))):d(e,t,{placement:\"bottom\",strategy:n,middleware:s}).then((({x:e,y:t,placement:o})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:o})))},A=(e,t)=>!(\"CSS\"in window&&\"supports\"in window.CSS)||window.CSS.supports(e,t),_=(e,t,o)=>{let l=null;const r=function(...r){const n=()=>{l=null,o||e.apply(this,r)};o&&!l&&(e.apply(this,r),l=setTimeout(n,t)),o||(l&&clearTimeout(l),l=setTimeout(n,t))};return r.cancel=()=>{l&&(clearTimeout(l),l=null)},r},O=e=>null!==e&&!Array.isArray(e)&&\"object\"==typeof e,k=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every(((e,o)=>k(e,t[o])));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!O(e)||!O(t))return e===t;const o=Object.keys(e),l=Object.keys(t);return o.length===l.length&&o.every((o=>k(e[o],t[o])))},T=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;const t=getComputedStyle(e);return[\"overflow\",\"overflow-x\",\"overflow-y\"].some((e=>{const o=t.getPropertyValue(e);return\"auto\"===o||\"scroll\"===o}))},L=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(T(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},C=\"undefined\"!=typeof window?t:o,R=e=>{e.current&&(clearTimeout(e.current),e.current=null)},x=\"DEFAULT_TOOLTIP_ID\",N={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},$=l({getTooltipData:()=>N}),I=({children:t})=>{const[o,l]=r({[x]:new Set}),[c,s]=r({[x]:{current:null}}),a=(e,...t)=>{l((o=>{var l;const r=null!==(l=o[e])&&void 0!==l?l:new Set;return t.forEach((e=>r.add(e))),{...o,[e]:new Set(r)}}))},u=(e,...t)=>{l((o=>{const l=o[e];return l?(t.forEach((e=>l.delete(e))),{...o}):o}))},d=n(((e=x)=>{var t,l;return{anchorRefs:null!==(t=o[e])&&void 0!==t?t:new Set,activeAnchor:null!==(l=c[e])&&void 0!==l?l:{current:null},attach:(...t)=>a(e,...t),detach:(...t)=>u(e,...t),setActiveAnchor:t=>((e,t)=>{s((o=>{var l;return(null===(l=o[e])||void 0===l?void 0:l.current)===t.current?o:{...o,[e]:t}}))})(e,t)}}),[o,c,a,u]),p=i((()=>({getTooltipData:d})),[d]);return e.createElement($.Provider,{value:p},t)};function j(e=x){return c($).getTooltipData(e)}const B=({tooltipId:t,children:l,className:r,place:n,content:i,html:c,variant:a,offset:u,wrapper:d,events:p,positionStrategy:v,delayShow:m,delayHide:f})=>{const{attach:h,detach:w}=j(t),b=s(null);return o((()=>(h(b),()=>{w(b)})),[]),e.createElement(\"span\",{ref:b,className:y(\"react-tooltip-wrapper\",r),\"data-tooltip-place\":n,\"data-tooltip-content\":i,\"data-tooltip-html\":c,\"data-tooltip-variant\":a,\"data-tooltip-offset\":u,\"data-tooltip-wrapper\":d,\"data-tooltip-events\":p,\"data-tooltip-position-strategy\":v,\"data-tooltip-delay-show\":m,\"data-tooltip-delay-hide\":f},l)};var z={tooltip:\"core-styles-module_tooltip__3vRRp\",fixed:\"core-styles-module_fixed__pcSol\",arrow:\"core-styles-module_arrow__cvMwQ\",noArrow:\"core-styles-module_noArrow__xock6\",clickable:\"core-styles-module_clickable__ZuTTB\",show:\"core-styles-module_show__Nt9eE\",closing:\"core-styles-module_closing__sGnxF\"},D={tooltip:\"styles-module_tooltip__mnnfp\",arrow:\"styles-module_arrow__K0L3T\",dark:\"styles-module_dark__xNqje\",light:\"styles-module_light__Z6W-X\",success:\"styles-module_success__A2AKt\",warning:\"styles-module_warning__SCK0X\",error:\"styles-module_error__JvumD\",info:\"styles-module_info__BWdHW\"};const q=({forwardRef:t,id:l,className:i,classNameArrow:c,variant:u=\"dark\",anchorId:d,anchorSelect:p,place:v=\"top\",offset:m=10,events:h=[\"hover\"],openOnClick:w=!1,positionStrategy:b=\"absolute\",middlewares:S,wrapper:g,delayShow:A=0,delayHide:O=0,float:T=!1,hidden:x=!1,noArrow:N=!1,clickable:$=!1,closeOnEsc:I=!1,closeOnScroll:B=!1,closeOnResize:q=!1,openEvents:H,closeEvents:M,globalCloseEvents:W,imperativeModeOnly:P,style:V,position:F,afterShow:K,afterHide:U,disableTooltip:X,content:Y,contentWrapperRef:G,isOpen:Z,defaultIsOpen:J=!1,setIsOpen:Q,activeAnchor:ee,setActiveAnchor:te,border:oe,opacity:le,arrowColor:re,role:ne=\"tooltip\"})=>{var ie;const ce=s(null),se=s(null),ae=s(null),ue=s(null),de=s(null),[pe,ve]=r({tooltipStyles:{},tooltipArrowStyles:{},place:v}),[me,fe]=r(!1),[ye,he]=r(!1),[we,be]=r(null),Se=s(!1),ge=s(null),{anchorRefs:Ee,setActiveAnchor:Ae}=j(l),_e=s(!1),[Oe,ke]=r([]),Te=s(!1),Le=w||h.includes(\"click\"),Ce=Le||(null==H?void 0:H.click)||(null==H?void 0:H.dblclick)||(null==H?void 0:H.mousedown),Re=H?{...H}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!H&&Le&&Object.assign(Re,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const xe=M?{...M}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!M&&Le&&Object.assign(xe,{mouseleave:!1,blur:!1,mouseout:!1});const Ne=W?{...W}:{escape:I||!1,scroll:B||!1,resize:q||!1,clickOutsideAnchor:Ce||!1};P&&(Object.assign(Re,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(xe,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(Ne,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),C((()=>(Te.current=!0,()=>{Te.current=!1})),[]);const $e=e=>{Te.current&&(e&&he(!0),setTimeout((()=>{Te.current&&(null==Q||Q(e),void 0===Z&&fe(e))}),10))};o((()=>{if(void 0===Z)return()=>null;Z&&he(!0);const e=setTimeout((()=>{fe(Z)}),10);return()=>{clearTimeout(e)}}),[Z]),o((()=>{if(me!==Se.current)if(R(de),Se.current=me,me)null==K||K();else{const e=(e=>{const t=e.match(/^([\\d.]+)(ms|s)$/);if(!t)return 0;const[,o,l]=t;return Number(o)*(\"ms\"===l?1:1e3)})(getComputedStyle(document.body).getPropertyValue(\"--rt-transition-show-delay\"));de.current=setTimeout((()=>{he(!1),be(null),null==U||U()}),e+25)}}),[me]);const Ie=e=>{ve((t=>k(t,e)?t:e))},je=(e=A)=>{R(ae),ye?$e(!0):ae.current=setTimeout((()=>{$e(!0)}),e)},Be=(e=O)=>{R(ue),ue.current=setTimeout((()=>{_e.current||$e(!1)}),e)},ze=e=>{var t;if(!e)return;const o=null!==(t=e.currentTarget)&&void 0!==t?t:e.target;if(!(null==o?void 0:o.isConnected))return te(null),void Ae({current:null});A?je():$e(!0),te(o),Ae({current:o}),R(ue)},De=()=>{$?Be(O||100):O?Be():$e(!1),R(ae)},qe=({x:e,y:t})=>{var o;const l={getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})};E({place:null!==(o=null==we?void 0:we.place)&&void 0!==o?o:v,offset:m,elementReference:l,tooltipReference:ce.current,tooltipArrowReference:se.current,strategy:b,middlewares:S,border:oe}).then((e=>{Ie(e)}))},He=e=>{if(!e)return;const t=e,o={x:t.clientX,y:t.clientY};qe(o),ge.current=o},Me=e=>{var t;if(!me)return;const o=e.target;if(!o.isConnected)return;if(null===(t=ce.current)||void 0===t?void 0:t.contains(o))return;[document.querySelector(`[id='${d}']`),...Oe].some((e=>null==e?void 0:e.contains(o)))||($e(!1),R(ae))},We=_(ze,50,!0),Pe=_(De,50,!0),Ve=e=>{Pe.cancel(),We(e)},Fe=()=>{We.cancel(),Pe()},Ke=n((()=>{var e,t;const o=null!==(e=null==we?void 0:we.position)&&void 0!==e?e:F;o?qe(o):T?ge.current&&qe(ge.current):(null==ee?void 0:ee.isConnected)&&E({place:null!==(t=null==we?void 0:we.place)&&void 0!==t?t:v,offset:m,elementReference:ee,tooltipReference:ce.current,tooltipArrowReference:se.current,strategy:b,middlewares:S,border:oe}).then((e=>{Te.current&&Ie(e)}))}),[me,ee,Y,V,v,null==we?void 0:we.place,m,b,F,null==we?void 0:we.position,T]);o((()=>{var e,t;const o=new Set(Ee);Oe.forEach((e=>{(null==X?void 0:X(e))||o.add({current:e})}));const l=document.querySelector(`[id='${d}']`);l&&!(null==X?void 0:X(l))&&o.add({current:l});const r=()=>{$e(!1)},n=L(ee),i=L(ce.current);Ne.scroll&&(window.addEventListener(\"scroll\",r),null==n||n.addEventListener(\"scroll\",r),null==i||i.addEventListener(\"scroll\",r));let c=null;Ne.resize?window.addEventListener(\"resize\",r):ee&&ce.current&&(c=f(ee,ce.current,Ke,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const s=e=>{\"Escape\"===e.key&&$e(!1)};Ne.escape&&window.addEventListener(\"keydown\",s),Ne.clickOutsideAnchor&&window.addEventListener(\"click\",Me);const a=[],u=e=>Boolean((null==e?void 0:e.target)&&(null==ee?void 0:ee.contains(e.target))),p=e=>{me&&u(e)||ze(e)},v=e=>{me&&u(e)&&De()},m=[\"mouseover\",\"mouseout\",\"mouseenter\",\"mouseleave\",\"focus\",\"blur\"],y=[\"click\",\"dblclick\",\"mousedown\",\"mouseup\"];Object.entries(Re).forEach((([e,t])=>{t&&(m.includes(e)?a.push({event:e,listener:Ve}):y.includes(e)&&a.push({event:e,listener:p}))})),Object.entries(xe).forEach((([e,t])=>{t&&(m.includes(e)?a.push({event:e,listener:Fe}):y.includes(e)&&a.push({event:e,listener:v}))})),T&&a.push({event:\"pointermove\",listener:He});const h=()=>{_e.current=!0},w=()=>{_e.current=!1,De()},b=$&&(xe.mouseout||xe.mouseleave);return b&&(null===(e=ce.current)||void 0===e||e.addEventListener(\"mouseover\",h),null===(t=ce.current)||void 0===t||t.addEventListener(\"mouseout\",w)),a.forEach((({event:e,listener:t})=>{o.forEach((o=>{var l;null===(l=o.current)||void 0===l||l.addEventListener(e,t)}))})),()=>{var e,t;Ne.scroll&&(window.removeEventListener(\"scroll\",r),null==n||n.removeEventListener(\"scroll\",r),null==i||i.removeEventListener(\"scroll\",r)),Ne.resize?window.removeEventListener(\"resize\",r):null==c||c(),Ne.clickOutsideAnchor&&window.removeEventListener(\"click\",Me),Ne.escape&&window.removeEventListener(\"keydown\",s),b&&(null===(e=ce.current)||void 0===e||e.removeEventListener(\"mouseover\",h),null===(t=ce.current)||void 0===t||t.removeEventListener(\"mouseout\",w)),a.forEach((({event:e,listener:t})=>{o.forEach((o=>{var l;null===(l=o.current)||void 0===l||l.removeEventListener(e,t)}))}))}}),[ee,Ke,ye,Ee,Oe,H,M,W,Le,A,O]),o((()=>{var e,t;let o=null!==(t=null!==(e=null==we?void 0:we.anchorSelect)&&void 0!==e?e:p)&&void 0!==t?t:\"\";!o&&l&&(o=`[data-tooltip-id='${l.replace(/'/g,\"\\\\'\")}']`);const r=new MutationObserver((e=>{const t=[],r=[];e.forEach((e=>{if(\"attributes\"===e.type&&\"data-tooltip-id\"===e.attributeName){e.target.getAttribute(\"data-tooltip-id\")===l?t.push(e.target):e.oldValue===l&&r.push(e.target)}if(\"childList\"===e.type){if(ee){const t=[...e.removedNodes].filter((e=>1===e.nodeType));if(o)try{r.push(...t.filter((e=>e.matches(o)))),r.push(...t.flatMap((e=>[...e.querySelectorAll(o)])))}catch(e){}t.some((e=>{var t;return!!(null===(t=null==e?void 0:e.contains)||void 0===t?void 0:t.call(e,ee))&&(he(!1),$e(!1),te(null),R(ae),R(ue),!0)}))}if(o)try{const l=[...e.addedNodes].filter((e=>1===e.nodeType));t.push(...l.filter((e=>e.matches(o)))),t.push(...l.flatMap((e=>[...e.querySelectorAll(o)])))}catch(e){}}})),(t.length||r.length)&&ke((e=>[...e.filter((e=>!r.includes(e))),...t]))}));return r.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:[\"data-tooltip-id\"],attributeOldValue:!0}),()=>{r.disconnect()}}),[l,p,null==we?void 0:we.anchorSelect,ee]),o((()=>{Ke()}),[Ke]),o((()=>{if(!(null==G?void 0:G.current))return()=>null;const e=new ResizeObserver((()=>{setTimeout((()=>Ke()))}));return e.observe(G.current),()=>{e.disconnect()}}),[Y,null==G?void 0:G.current]),o((()=>{var e;const t=document.querySelector(`[id='${d}']`),o=[...Oe,t];ee&&o.includes(ee)||te(null!==(e=Oe[0])&&void 0!==e?e:t)}),[d,Oe,ee]),o((()=>(J&&$e(!0),()=>{R(ae),R(ue)})),[]),o((()=>{var e;let t=null!==(e=null==we?void 0:we.anchorSelect)&&void 0!==e?e:p;if(!t&&l&&(t=`[data-tooltip-id='${l.replace(/'/g,\"\\\\'\")}']`),t)try{const e=Array.from(document.querySelectorAll(t));ke(e)}catch(e){ke([])}}),[l,p,null==we?void 0:we.anchorSelect]),o((()=>{ae.current&&(R(ae),je(A))}),[A]);const Ue=null!==(ie=null==we?void 0:we.content)&&void 0!==ie?ie:Y,Xe=me&&Object.keys(pe.tooltipStyles).length>0;return a(t,(()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] \"${e.anchorSelect}\" is not a valid CSS selector`)}be(null!=e?e:null),(null==e?void 0:e.delay)?je(e.delay):$e(!0)},close:e=>{(null==e?void 0:e.delay)?Be(e.delay):$e(!1)},activeAnchor:ee,place:pe.place,isOpen:Boolean(ye&&!x&&Ue&&Xe)}))),ye&&!x&&Ue?e.createElement(g,{id:l,role:ne,className:y(\"react-tooltip\",z.tooltip,D.tooltip,D[u],i,`react-tooltip__place-${pe.place}`,z[Xe?\"show\":\"closing\"],Xe?\"react-tooltip__show\":\"react-tooltip__closing\",\"fixed\"===b&&z.fixed,$&&z.clickable),onTransitionEnd:e=>{R(de),me||\"opacity\"!==e.propertyName||(he(!1),be(null),null==U||U())},style:{...V,...pe.tooltipStyles,opacity:void 0!==le&&Xe?le:void 0},ref:ce},Ue,e.createElement(g,{className:y(\"react-tooltip-arrow\",z.arrow,D.arrow,c,N&&z.noArrow),style:{...pe.tooltipArrowStyles,background:re?`linear-gradient(to right bottom, transparent 50%, ${re} 50%)`:void 0},ref:se})):null},H=({content:t})=>e.createElement(\"span\",{dangerouslySetInnerHTML:{__html:t}}),M=e.forwardRef((({id:t,anchorId:l,anchorSelect:n,content:i,html:c,render:a,className:u,classNameArrow:d,variant:p=\"dark\",place:v=\"top\",offset:m=10,wrapper:f=\"div\",children:h=null,events:w=[\"hover\"],openOnClick:b=!1,positionStrategy:S=\"absolute\",middlewares:g,delayShow:E=0,delayHide:_=0,float:O=!1,hidden:k=!1,noArrow:T=!1,clickable:L=!1,closeOnEsc:C=!1,closeOnScroll:R=!1,closeOnResize:x=!1,openEvents:N,closeEvents:$,globalCloseEvents:I,imperativeModeOnly:B=!1,style:z,position:D,isOpen:M,defaultIsOpen:W=!1,disableStyleInjection:P=!1,border:V,opacity:F,arrowColor:K,setIsOpen:U,afterShow:X,afterHide:Y,disableTooltip:G,role:Z=\"tooltip\"},J)=>{const[Q,ee]=r(i),[te,oe]=r(c),[le,re]=r(v),[ne,ie]=r(p),[ce,se]=r(m),[ae,ue]=r(E),[de,pe]=r(_),[ve,me]=r(O),[fe,ye]=r(k),[he,we]=r(f),[be,Se]=r(w),[ge,Ee]=r(S),[Ae,_e]=r(null),[Oe,ke]=r(null),Te=s(P),{anchorRefs:Le,activeAnchor:Ce}=j(t),Re=e=>null==e?void 0:e.getAttributeNames().reduce(((t,o)=>{var l;if(o.startsWith(\"data-tooltip-\")){t[o.replace(/^data-tooltip-/,\"\")]=null!==(l=null==e?void 0:e.getAttribute(o))&&void 0!==l?l:null}return t}),{}),xe=e=>{const t={place:e=>{var t;re(null!==(t=e)&&void 0!==t?t:v)},content:e=>{ee(null!=e?e:i)},html:e=>{oe(null!=e?e:c)},variant:e=>{var t;ie(null!==(t=e)&&void 0!==t?t:p)},offset:e=>{se(null===e?m:Number(e))},wrapper:e=>{var t;we(null!==(t=e)&&void 0!==t?t:f)},events:e=>{const t=null==e?void 0:e.split(\" \");Se(null!=t?t:w)},\"position-strategy\":e=>{var t;Ee(null!==(t=e)&&void 0!==t?t:S)},\"delay-show\":e=>{ue(null===e?E:Number(e))},\"delay-hide\":e=>{pe(null===e?_:Number(e))},float:e=>{me(null===e?O:\"true\"===e)},hidden:e=>{ye(null===e?k:\"true\"===e)},\"class-name\":e=>{_e(e)}};Object.values(t).forEach((e=>e(null))),Object.entries(e).forEach((([e,o])=>{var l;null===(l=t[e])||void 0===l||l.call(t,o)}))};o((()=>{ee(i)}),[i]),o((()=>{oe(c)}),[c]),o((()=>{re(v)}),[v]),o((()=>{ie(p)}),[p]),o((()=>{se(m)}),[m]),o((()=>{ue(E)}),[E]),o((()=>{pe(_)}),[_]),o((()=>{me(O)}),[O]),o((()=>{ye(k)}),[k]),o((()=>{Ee(S)}),[S]),o((()=>{Te.current!==P&&console.warn(\"[react-tooltip] Do not change `disableStyleInjection` dynamically.\")}),[P]),o((()=>{\"undefined\"!=typeof window&&window.dispatchEvent(new CustomEvent(\"react-tooltip-inject-styles\",{detail:{disableCore:\"core\"===P,disableBase:P}}))}),[]),o((()=>{var e;const o=new Set(Le);let r=n;if(!r&&t&&(r=`[data-tooltip-id='${t.replace(/'/g,\"\\\\'\")}']`),r)try{document.querySelectorAll(r).forEach((e=>{o.add({current:e})}))}catch(e){console.warn(`[react-tooltip] \"${r}\" is not a valid CSS selector`)}const i=document.querySelector(`[id='${l}']`);if(i&&o.add({current:i}),!o.size)return()=>null;const c=null!==(e=null!=Oe?Oe:i)&&void 0!==e?e:Ce.current,s=new MutationObserver((e=>{e.forEach((e=>{var t;if(!c||\"attributes\"!==e.type||!(null===(t=e.attributeName)||void 0===t?void 0:t.startsWith(\"data-tooltip-\")))return;const o=Re(c);xe(o)}))})),a={attributes:!0,childList:!1,subtree:!1};if(c){const e=Re(c);xe(e),s.observe(c,a)}return()=>{s.disconnect()}}),[Le,Ce,Oe,l,n]),o((()=>{(null==z?void 0:z.border)&&console.warn(\"[react-tooltip] Do not set `style.border`. Use `border` prop instead.\"),V&&!A(\"border\",`${V}`)&&console.warn(`[react-tooltip] \"${V}\" is not a valid \\`border\\`.`),(null==z?void 0:z.opacity)&&console.warn(\"[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead.\"),F&&!A(\"opacity\",`${F}`)&&console.warn(`[react-tooltip] \"${F}\" is not a valid \\`opacity\\`.`)}),[]);let Ne=h;const $e=s(null);if(a){const t=a({content:(null==Oe?void 0:Oe.getAttribute(\"data-tooltip-content\"))||Q||null,activeAnchor:Oe});Ne=t?e.createElement(\"div\",{ref:$e,className:\"react-tooltip-content-wrapper\"},t):null}else Q&&(Ne=Q);te&&(Ne=e.createElement(H,{content:te}));const Ie={forwardRef:J,id:t,anchorId:l,anchorSelect:n,className:y(u,Ae),classNameArrow:d,content:Ne,contentWrapperRef:$e,place:le,variant:ne,offset:ce,wrapper:he,events:be,openOnClick:b,positionStrategy:ge,middlewares:g,delayShow:ae,delayHide:de,float:ve,hidden:fe,noArrow:T,clickable:L,closeOnEsc:C,closeOnScroll:R,closeOnResize:x,openEvents:N,closeEvents:$,globalCloseEvents:I,imperativeModeOnly:B,style:z,position:D,isOpen:M,defaultIsOpen:W,border:V,opacity:F,arrowColor:K,setIsOpen:U,afterShow:X,afterHide:Y,disableTooltip:G,activeAnchor:Oe,setActiveAnchor:e=>ke(e),role:Z};return e.createElement(q,{...Ie})}));\"undefined\"!=typeof window&&window.addEventListener(\"react-tooltip-inject-styles\",(e=>{e.detail.disableCore||S({css:`:root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}`,type:\"core\"}),e.detail.disableBase||S({css:`\n.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:\"base\"})}));export{M as Tooltip,I as TooltipProvider,B as TooltipWrapper,g as removeStyle};\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n"], "mappings": ";;;;;;;;;;;AAMA,mBAAsK;;;ACDtK,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI;AAAK,eAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ,GAAAA;AAAA,IACA,GAAAC;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAKA;AAAA,IACL,MAAMD;AAAA,IACN,OAAOA,KAAI;AAAA,IACX,QAAQC,KAAI;AAAA,IACZ,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;;;ACpIA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF,GAAAC;AAAA,IACA,GAAAC;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAASC,KAAI,GAAGA,KAAI,gBAAgB,QAAQA,MAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgBA,EAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX,GAAAF;AAAA,MACA,GAAAC;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAF;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,IAAAC,KAAI,SAAS,OAAO,QAAQA;AAC5B,IAAAC,KAAI,SAAS,OAAO,QAAQA;AAC5B,qBAAiB;AAAA,MACf,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,QACN,GAAG,eAAe,IAAI;AAAA,QACtB,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMF,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC,GAAAC;AAAA,UACA,GAAAC;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,MAAAC,KAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAAF;AAAA,IACA,GAAAC;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,GAAAD;AAAA,IACA,GAAAC;AAAA,IACA,UAAAF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C,GAAAC;AAAA,IACA,GAAAC;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOF,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ,GAAAC;AAAA,MACA,GAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb,GAAAC;AAAA,MACA,GAAAC;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMF,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMI,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,QAChC,GAAI,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AA+GA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAM3B,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,YAAY,gBAAgB;AACpD,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAM,+BAA+B,8BAA8B;AACnE,UAAI,CAAC,+BAA+B,8BAA8B;AAChE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMC,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBF,YAAW,SAAS;AAC1C,YAAI,eAAe;AACjB,cAAI;AACJ,gBAAM,0BAA0B,mBAAmB,cAAc,oBAAoB,YAAY,aAAa,IAAI;AAClH,gBAAM,+BAA+B,kBAAkB,cAAc,CAAC,MAAM,OAAO,SAAS,gBAAgB,UAAU,CAAC,KAAK;AAC5H,cAAI,CAAC,2BAA2B,4BAA4B;AAE1D,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAACG,IAAGC,OAAMD,GAAE,UAAU,CAAC,IAAIC,GAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMC,cAAa,yBAAyB,cAAc,OAAO,OAAK;AACpE,oBAAI,8BAA8B;AAChC,wBAAM,kBAAkB,YAAY,EAAE,SAAS;AAC/C,yBAAO,oBAAoB;AAAA;AAAA,kBAG3B,oBAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAACH,IAAGC,OAAMD,GAAE,CAAC,IAAIC,GAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,kBAAIC,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AA6MA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ,GAAAC;AAAA,QACA,GAAAC;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAGD,KAAI,WAAW;AAAA,QAClB,GAAGC,KAAI,WAAW;AAAA,QAClB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ,GAAAD;AAAA,QACA,GAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,UAAU;AAAA,UACR,IAAI,UAAQ;AACV,gBAAI;AAAA,cACF,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF,IAAI;AACJ,mBAAO;AAAA,cACL,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb,GAAAD;AAAA,QACA,GAAAC;AAAA,MACF;AACA,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,UAAI,eAAe;AACjB,cAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,cAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,wBAAgB,MAAMD,MAAK,eAAeC,IAAG;AAAA,MAC/C;AACA,UAAI,gBAAgB;AAClB,cAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,cAAMD,OAAM,iBAAiB,SAAS,OAAO;AAC7C,cAAMC,OAAM,iBAAiB,SAAS,OAAO;AAC7C,yBAAiB,MAAMD,MAAK,gBAAgBC,IAAG;AAAA,MACjD;AACA,YAAM,gBAAgB,QAAQ,GAAG;AAAA,QAC/B,GAAG;AAAA,QACH,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,UACJ,GAAG,cAAc,IAAIH;AAAA,UACrB,GAAG,cAAc,IAAIC;AAAA,UACrB,SAAS;AAAA,YACP,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC33BA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIG,kBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAASC,IAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAID,kBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI;AAAU,WAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAASA,kBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;;;AClJA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAME,kBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,GAAAC;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAIC,MAAKD,KAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAIE,MAAKF,KAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAACC,MAAK,CAAC,OAAO,SAASA,EAAC,GAAG;AAC7B,IAAAA,KAAI;AAAA,EACN;AACA,MAAI,CAACC,MAAK,CAAC,OAAO,SAASA,EAAC,GAAG;AAC7B,IAAAA,KAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAID,MAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAIC,MAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAMH,kBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,MAAAE,MAAK,YAAY;AACjB,MAAAC,MAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,MAAAD,MAAK;AACL,MAAAC,MAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAMD,KAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAMC,KAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAID,KAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAMC,KAAI,CAAC,OAAO;AAClB,MAAIH,kBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,IAAAE,MAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAA;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAID,KAAI;AACR,MAAIC,KAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,MAAAD,KAAI,eAAe;AACnB,MAAAC,KAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAMD,KAAI,OAAO,MAAM;AACvB,QAAMC,KAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAOH,kBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiBA,kBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgBA,kBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAI9B,WAAS,4BAA4B;AACnC,YAAQ,IAAI,oBAAoB,eAAe;AAAA,EACjD;AACA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAC1B,gCAA0B;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,WAAW,CAAC,2BAA2B,iBAAiB;AAC1D,8BAA0B;AAAA,EAC5B;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAME,KAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAMC,KAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL,GAAAD;AAAA,IACA,GAAAC;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAOH,kBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAKA,kBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,QAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,SAAO;AAAA,IACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IAC9G,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,mBAAmB;AAAA,MAC1B,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAOA,kBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cAAcI,IAAGC,IAAG;AAC3B,SAAOD,GAAE,MAAMC,GAAE,KAAKD,GAAE,MAAMC,GAAE,KAAKD,GAAE,UAAUC,GAAE,SAASD,GAAE,WAAWC,GAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAAS,IAAI;AACX,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAmBA,IAAMC,UAAS;AAef,IAAMC,SAAQ;AAQd,IAAMC,QAAO;AAsBb,IAAMC,SAAQ;AAkBd,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB;AAAA,IACxB,GAAG,cAAc;AAAA,IACjB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU;AAAA,IAC5C,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH;;;AJ9uB6R,wBAAa;AAAa,IAAM,IAAE;AAAR,IAAoC,IAAE;AAAtC,IAAkE,IAAE,EAAC,MAAK,OAAG,MAAK,MAAE;AAAE,SAAS,EAAE,EAAC,KAAIC,IAAE,IAAGC,KAAE,GAAE,MAAKC,KAAE,QAAO,KAAIC,GAAC,GAAE;AAAC,MAAIC,IAAEC;AAAE,MAAG,CAACL,MAAG,eAAa,OAAO,YAAU,EAAEE,EAAC;AAAE;AAAO,MAAG,WAASA,MAAG,eAAa,OAAO,YAAU,UAAQE,KAAE,SAAO,WAAS,WAAS,UAAQ,SAAO,QAAQ,QAAM,WAASA,KAAE,SAAOA,GAAE;AAAmC;AAAO,MAAG,WAASF,MAAG,eAAa,OAAO,YAAU,UAAQG,KAAE,SAAO,WAAS,WAAS,UAAQ,SAAO,QAAQ,QAAM,WAASA,KAAE,SAAOA,GAAE;AAAmC;AAAO,aAASH,OAAID,KAAE,IAAGE,OAAIA,KAAE,CAAC;AAAG,QAAK,EAAC,UAASG,GAAC,IAAEH;AAAE,MAAG,SAAS,eAAeF,EAAC;AAAE;AAAO,QAAMM,KAAE,SAAS,QAAM,SAAS,qBAAqB,MAAM,EAAE,CAAC,GAAEC,KAAE,SAAS,cAAc,OAAO;AAAE,EAAAA,GAAE,KAAGP,IAAEO,GAAE,OAAK,YAAW,UAAQF,MAAGC,GAAE,aAAWA,GAAE,aAAaC,IAAED,GAAE,UAAU,IAAEA,GAAE,YAAYC,EAAC,GAAEA,GAAE,aAAWA,GAAE,WAAW,UAAQR,KAAEQ,GAAE,YAAY,SAAS,eAAeR,EAAC,CAAC,GAAE,EAAEE,EAAC,IAAE;AAAE;AAAC,SAAS,EAAE,EAAC,MAAKF,KAAE,QAAO,IAAGC,KAAE,EAAC,IAAE,CAAC,GAAE;AAAC,MAAG,CAAC,EAAED,EAAC;AAAE;AAAO,aAASA,OAAIC,KAAE;AAAG,QAAMC,KAAE,SAAS,eAAeD,EAAC;AAAE,eAAW,QAAMC,KAAE,SAAOA,GAAE,WAAS,QAAMA,MAAGA,GAAE,OAAO,IAAE,QAAQ,KAAK,6DAA6DD,EAAC,iCAAiC,GAAE,EAAED,EAAC,IAAE;AAAE;AAAC,IAAM,IAAE,OAAM,EAAC,kBAAiBA,KAAE,MAAK,kBAAiBC,KAAE,MAAK,uBAAsBC,KAAE,MAAK,OAAMC,KAAE,OAAM,QAAOC,KAAE,IAAG,UAASC,KAAE,YAAW,aAAYC,KAAE,CAACG,QAAE,OAAOL,EAAC,CAAC,GAAEM,MAAE,EAAC,2BAA0B,QAAO,CAAC,GAAEC,OAAE,EAAC,SAAQ,EAAC,CAAC,CAAC,GAAE,QAAOJ,GAAC,MAAI;AAAC,MAAG,CAACP;AAAE,WAAM,EAAC,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,OAAMG,GAAC;AAAE,MAAG,SAAOF;AAAE,WAAM,EAAC,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,OAAME,GAAC;AAAE,QAAMK,KAAEF;AAAE,SAAOJ,MAAGM,GAAE,KAAKI,OAAE,EAAC,SAAQV,IAAE,SAAQ,EAAC,CAAC,CAAC,GAAEW,iBAAEb,IAAEC,IAAE,EAAC,WAAUE,IAAE,UAASE,IAAE,YAAWG,GAAC,CAAC,EAAE,KAAM,CAAC,EAAC,GAAER,IAAE,GAAEC,IAAE,WAAUC,IAAE,gBAAeC,GAAC,MAAI;AAAC,QAAIC,IAAEC;AAAE,UAAMC,KAAE,EAAC,MAAK,GAAGN,EAAC,MAAK,KAAI,GAAGC,EAAC,MAAK,QAAOM,GAAC,GAAE,EAAC,GAAEC,IAAE,GAAEM,GAAC,IAAE,UAAQV,KAAED,GAAE,UAAQ,WAASC,KAAEA,KAAE,EAAC,GAAE,GAAE,GAAE,EAAC,GAAE,IAAE,UAAQC,KAAE,EAAC,KAAI,UAAS,OAAM,QAAO,QAAO,OAAM,MAAK,QAAO,EAAEH,GAAE,MAAM,GAAG,EAAE,CAAC,CAAC,MAAI,WAASG,KAAEA,KAAE,UAAS,IAAEE,MAAG,EAAC,cAAaA,IAAE,aAAYA,GAAC;AAAE,QAAI,IAAE;AAAE,QAAGA,IAAE;AAAC,YAAMP,KAAE,GAAGO,EAAC,GAAG,MAAM,SAAS;AAAE,WAAG,QAAMP,KAAE,SAAOA,GAAE,CAAC,KAAG,OAAOA,GAAE,CAAC,CAAC,IAAE;AAAA,IAAC;AAAC,WAAM,EAAC,eAAcM,IAAE,oBAAmB,EAAC,MAAK,QAAME,KAAE,GAAGA,EAAC,OAAK,IAAG,KAAI,QAAMM,KAAE,GAAGA,EAAC,OAAK,IAAG,OAAM,IAAG,QAAO,IAAG,GAAG,GAAE,CAAC,CAAC,GAAE,IAAI,IAAE,CAAC,KAAI,GAAE,OAAMZ,GAAC;AAAA,EAAC,CAAE,KAAGW,iBAAEb,IAAEC,IAAE,EAAC,WAAU,UAAS,UAASI,IAAE,YAAWG,GAAC,CAAC,EAAE,KAAM,CAAC,EAAC,GAAER,IAAE,GAAEC,IAAE,WAAUC,GAAC,OAAK,EAAC,eAAc,EAAC,MAAK,GAAGF,EAAC,MAAK,KAAI,GAAGC,EAAC,KAAI,GAAE,oBAAmB,CAAC,GAAE,OAAMC,GAAC,EAAG;AAAC;AAAjoC,IAAmoC,IAAE,CAACF,IAAEC,OAAI,EAAE,SAAQ,UAAQ,cAAa,OAAO,QAAM,OAAO,IAAI,SAASD,IAAEC,EAAC;AAA/sC,IAAitC,IAAE,CAACD,IAAEC,IAAEC,OAAI;AAAC,MAAIC,KAAE;AAAK,QAAMC,KAAE,YAAYA,IAAE;AAAC,UAAMC,KAAE,MAAI;AAAC,MAAAF,KAAE,MAAKD,MAAGF,GAAE,MAAM,MAAKI,EAAC;AAAA,IAAC;AAAE,IAAAF,MAAG,CAACC,OAAIH,GAAE,MAAM,MAAKI,EAAC,GAAED,KAAE,WAAWE,IAAEJ,EAAC,IAAGC,OAAIC,MAAG,aAAaA,EAAC,GAAEA,KAAE,WAAWE,IAAEJ,EAAC;AAAA,EAAE;AAAE,SAAOG,GAAE,SAAO,MAAI;AAAC,IAAAD,OAAI,aAAaA,EAAC,GAAEA,KAAE;AAAA,EAAK,GAAEC;AAAC;AAAh7C,IAAk7C,IAAE,CAAAJ,OAAG,SAAOA,MAAG,CAAC,MAAM,QAAQA,EAAC,KAAG,YAAU,OAAOA;AAAr+C,IAAu+C,IAAE,CAACA,IAAEC,OAAI;AAAC,MAAGD,OAAIC;AAAE,WAAM;AAAG,MAAG,MAAM,QAAQD,EAAC,KAAG,MAAM,QAAQC,EAAC;AAAE,WAAOD,GAAE,WAASC,GAAE,UAAQD,GAAE,MAAO,CAACA,IAAEE,OAAI,EAAEF,IAAEC,GAAEC,EAAC,CAAC,CAAE;AAAE,MAAG,MAAM,QAAQF,EAAC,MAAI,MAAM,QAAQC,EAAC;AAAE,WAAM;AAAG,MAAG,CAAC,EAAED,EAAC,KAAG,CAAC,EAAEC,EAAC;AAAE,WAAOD,OAAIC;AAAE,QAAMC,KAAE,OAAO,KAAKF,EAAC,GAAEG,KAAE,OAAO,KAAKF,EAAC;AAAE,SAAOC,GAAE,WAASC,GAAE,UAAQD,GAAE,MAAO,CAAAA,OAAG,EAAEF,GAAEE,EAAC,GAAED,GAAEC,EAAC,CAAC,CAAE;AAAC;AAA5wD,IAA8wD,IAAE,CAAAF,OAAG;AAAC,MAAG,EAAEA,cAAa,eAAaA,cAAa;AAAY,WAAM;AAAG,QAAMC,KAAE,iBAAiBD,EAAC;AAAE,SAAM,CAAC,YAAW,cAAa,YAAY,EAAE,KAAM,CAAAA,OAAG;AAAC,UAAME,KAAED,GAAE,iBAAiBD,EAAC;AAAE,WAAM,WAASE,MAAG,aAAWA;AAAA,EAAC,CAAE;AAAC;AAAv+D,IAAy+D,IAAE,CAAAF,OAAG;AAAC,MAAG,CAACA;AAAE,WAAO;AAAK,MAAIC,KAAED,GAAE;AAAc,SAAKC,MAAG;AAAC,QAAG,EAAEA,EAAC;AAAE,aAAOA;AAAE,IAAAA,KAAEA,GAAE;AAAA,EAAa;AAAC,SAAO,SAAS,oBAAkB,SAAS;AAAe;AAA7nE,IAA+nE,IAAE,eAAa,OAAO,SAAO,aAAAA,kBAAE,aAAAC;AAA9pE,IAAgqE,IAAE,CAAAF,OAAG;AAAC,EAAAA,GAAE,YAAU,aAAaA,GAAE,OAAO,GAAEA,GAAE,UAAQ;AAAK;AAAztE,IAA2tE,IAAE;AAA7tE,IAAkvE,IAAE,EAAC,YAAW,oBAAI,OAAI,cAAa,EAAC,SAAQ,KAAI,GAAE,QAAO,MAAI;AAAC,GAAE,QAAO,MAAI;AAAC,GAAE,iBAAgB,MAAI;AAAC,EAAC;AAAt1E,IAAw1E,QAAE,aAAAG,eAAE,EAAC,gBAAe,MAAI,EAAC,CAAC;AAAl3E,IAAo3E,IAAE,CAAC,EAAC,UAASF,GAAC,MAAI;AAAC,QAAK,CAACC,IAAEC,EAAC,QAAE,aAAAC,UAAE,EAAC,CAAC,CAAC,GAAE,oBAAI,MAAG,CAAC,GAAE,CAACG,IAAEC,EAAC,QAAE,aAAAJ,UAAE,EAAC,CAAC,CAAC,GAAE,EAAC,SAAQ,KAAI,EAAC,CAAC,GAAEU,KAAE,CAACd,OAAKC,OAAI;AAAC,IAAAE,GAAG,CAAAD,OAAG;AAAC,UAAIC;AAAE,YAAMC,KAAE,UAAQD,KAAED,GAAEF,EAAC,MAAI,WAASG,KAAEA,KAAE,oBAAI;AAAI,aAAOF,GAAE,QAAS,CAAAD,OAAGI,GAAE,IAAIJ,EAAC,CAAE,GAAE,EAAC,GAAGE,IAAE,CAACF,EAAC,GAAE,IAAI,IAAII,EAAC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,IAAE,CAACJ,OAAKC,OAAI;AAAC,IAAAE,GAAG,CAAAD,OAAG;AAAC,YAAMC,KAAED,GAAEF,EAAC;AAAE,aAAOG,MAAGF,GAAE,QAAS,CAAAD,OAAGG,GAAE,OAAOH,EAAC,CAAE,GAAE,EAAC,GAAGE,GAAC,KAAGA;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,QAAE,aAAAG,aAAG,CAACL,KAAE,MAAI;AAAC,QAAIC,IAAEE;AAAE,WAAM,EAAC,YAAW,UAAQF,KAAEC,GAAEF,EAAC,MAAI,WAASC,KAAEA,KAAE,oBAAI,OAAI,cAAa,UAAQE,KAAEI,GAAEP,EAAC,MAAI,WAASG,KAAEA,KAAE,EAAC,SAAQ,KAAI,GAAE,QAAO,IAAIF,OAAIa,GAAEd,IAAE,GAAGC,EAAC,GAAE,QAAO,IAAIA,OAAI,EAAED,IAAE,GAAGC,EAAC,GAAE,iBAAgB,CAAAA,QAAI,CAACD,IAAEC,OAAI;AAAC,MAAAO,GAAG,CAAAN,OAAG;AAAC,YAAIC;AAAE,gBAAO,UAAQA,KAAED,GAAEF,EAAC,MAAI,WAASG,KAAE,SAAOA,GAAE,aAAWF,GAAE,UAAQC,KAAE,EAAC,GAAGA,IAAE,CAACF,EAAC,GAAEC,GAAC;AAAA,MAAC,CAAE;AAAA,IAAC,GAAGD,IAAEC,EAAC,EAAC;AAAA,EAAC,GAAG,CAACC,IAAEK,IAAEO,IAAE,CAAC,CAAC,GAAE,QAAE,aAAAR,SAAG,OAAK,EAAC,gBAAe,EAAC,IAAI,CAAC,CAAC,CAAC;AAAE,SAAO,aAAAN,QAAE,cAAc,EAAE,UAAS,EAAC,OAAM,EAAC,GAAEC,EAAC;AAAC;AAAE,SAAS,EAAED,KAAE,GAAE;AAAC,aAAO,aAAAO,YAAE,CAAC,EAAE,eAAeP,EAAC;AAAC;AAAC,IAAM,IAAE,CAAC,EAAC,WAAUC,IAAE,UAASE,IAAE,WAAUC,IAAE,OAAMC,IAAE,SAAQC,IAAE,MAAKC,IAAE,SAAQO,IAAE,QAAO,GAAE,SAAQ,GAAE,QAAO,GAAE,kBAAiB,GAAE,WAAU,GAAE,WAAU,EAAC,MAAI;AAAC,QAAK,EAAC,QAAOC,IAAE,QAAOC,GAAC,IAAE,EAAEf,EAAC,GAAEgB,SAAE,aAAAT,QAAE,IAAI;AAAE,aAAO,aAAAN,WAAG,OAAKa,GAAEE,EAAC,GAAE,MAAI;AAAC,IAAAD,GAAEC,EAAC;AAAA,EAAC,IAAI,CAAC,CAAC,GAAE,aAAAjB,QAAE,cAAc,QAAO,EAAC,KAAIiB,IAAE,eAAU,kBAAAC,SAAE,yBAAwBd,EAAC,GAAE,sBAAqBC,IAAE,wBAAuBC,IAAE,qBAAoBC,IAAE,wBAAuBO,IAAE,uBAAsB,GAAE,wBAAuB,GAAE,uBAAsB,GAAE,kCAAiC,GAAE,2BAA0B,GAAE,2BAA0B,EAAC,GAAEX,EAAC;AAAC;AAAE,IAAI,IAAE,EAAC,SAAQ,qCAAoC,OAAM,mCAAkC,OAAM,mCAAkC,SAAQ,qCAAoC,WAAU,uCAAsC,MAAK,kCAAiC,SAAQ,oCAAmC;AAAhT,IAAkT,IAAE,EAAC,SAAQ,gCAA+B,OAAM,8BAA6B,MAAK,6BAA4B,OAAM,8BAA6B,SAAQ,gCAA+B,SAAQ,gCAA+B,OAAM,8BAA6B,MAAK,4BAA2B;AAAE,IAAM,IAAE,CAAC,EAAC,YAAWF,IAAE,IAAGE,IAAE,WAAUG,IAAE,gBAAeC,IAAE,SAAQ,IAAE,QAAO,UAAS,GAAE,cAAa,GAAE,OAAM,IAAE,OAAM,QAAO,IAAE,IAAG,QAAOQ,KAAE,CAAC,OAAO,GAAE,aAAYC,KAAE,OAAG,kBAAiBC,KAAE,YAAW,aAAYE,IAAE,SAAQC,IAAE,WAAUC,KAAE,GAAE,WAAUC,KAAE,GAAE,OAAMC,KAAE,OAAG,QAAOC,KAAE,OAAG,SAAQC,KAAE,OAAG,WAAUC,KAAE,OAAG,YAAWC,KAAE,OAAG,eAAcC,KAAE,OAAG,eAAcC,KAAE,OAAG,YAAWC,IAAE,aAAYC,IAAE,mBAAkB,GAAE,oBAAmB,GAAE,OAAM,GAAE,UAAS,GAAE,WAAU,GAAE,WAAU,GAAE,gBAAe,GAAE,SAAQ,GAAE,mBAAkB,GAAE,QAAO,GAAE,eAAc,IAAE,OAAG,WAAU,GAAE,cAAa,IAAG,iBAAgB,IAAG,QAAO,IAAG,SAAQ,IAAG,YAAW,IAAG,MAAK,KAAG,UAAS,MAAI;AAAC,MAAI;AAAG,QAAM,SAAG,aAAAvB,QAAE,IAAI,GAAE,SAAG,aAAAA,QAAE,IAAI,GAAE,SAAG,aAAAA,QAAE,IAAI,GAAE,SAAG,aAAAA,QAAE,IAAI,GAAE,SAAG,aAAAA,QAAE,IAAI,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAJ,UAAE,EAAC,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,OAAM,EAAC,CAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAE,KAAE,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAE,KAAE,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAE,IAAI,GAAE,SAAG,aAAAI,QAAE,KAAE,GAAE,SAAG,aAAAA,QAAE,IAAI,GAAE,EAAC,YAAW,IAAG,iBAAgB,GAAE,IAAE,EAAEL,EAAC,GAAE,SAAG,aAAAK,QAAE,KAAE,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAJ,UAAE,CAAC,CAAC,GAAE,SAAG,aAAAI,QAAE,KAAE,GAAE,KAAGQ,MAAGD,GAAE,SAAS,OAAO,GAAE,KAAG,OAAK,QAAMe,KAAE,SAAOA,GAAE,WAAS,QAAMA,KAAE,SAAOA,GAAE,cAAY,QAAMA,KAAE,SAAOA,GAAE,YAAW,KAAGA,KAAE,EAAC,GAAGA,GAAC,IAAE,EAAC,WAAU,MAAG,OAAM,MAAG,YAAW,OAAG,OAAM,OAAG,UAAS,OAAG,WAAU,MAAE;AAAE,GAACA,MAAG,MAAI,OAAO,OAAO,IAAG,EAAC,YAAW,OAAG,OAAM,OAAG,WAAU,OAAG,OAAM,KAAE,CAAC;AAAE,QAAM,KAAGC,KAAE,EAAC,GAAGA,GAAC,IAAE,EAAC,UAAS,MAAG,MAAK,MAAG,YAAW,OAAG,OAAM,OAAG,UAAS,OAAG,SAAQ,MAAE;AAAE,GAACA,MAAG,MAAI,OAAO,OAAO,IAAG,EAAC,YAAW,OAAG,MAAK,OAAG,UAAS,MAAE,CAAC;AAAE,QAAM,KAAG,IAAE,EAAC,GAAG,EAAC,IAAE,EAAC,QAAOJ,MAAG,OAAG,QAAOC,MAAG,OAAG,QAAOC,MAAG,OAAG,oBAAmB,MAAI,MAAE;AAAE,QAAI,OAAO,OAAO,IAAG,EAAC,WAAU,OAAG,OAAM,OAAG,YAAW,OAAG,OAAM,OAAG,UAAS,OAAG,WAAU,MAAE,CAAC,GAAE,OAAO,OAAO,IAAG,EAAC,UAAS,OAAG,MAAK,OAAG,YAAW,OAAG,OAAM,OAAG,UAAS,OAAG,SAAQ,MAAE,CAAC,GAAE,OAAO,OAAO,IAAG,EAAC,QAAO,OAAG,QAAO,OAAG,QAAO,OAAG,oBAAmB,MAAE,CAAC,IAAG,EAAG,OAAK,GAAG,UAAQ,MAAG,MAAI;AAAC,OAAG,UAAQ;AAAA,EAAE,IAAI,CAAC,CAAC;AAAE,QAAM,KAAG,CAAA7B,OAAG;AAAC,OAAG,YAAUA,MAAG,GAAG,IAAE,GAAE,WAAY,MAAI;AAAC,SAAG,YAAU,QAAM,KAAG,EAAEA,EAAC,GAAE,WAAS,KAAG,GAAGA,EAAC;AAAA,IAAE,GAAG,EAAE;AAAA,EAAE;AAAE,mBAAAE,WAAG,MAAI;AAAC,QAAG,WAAS;AAAE,aAAM,MAAI;AAAK,SAAG,GAAG,IAAE;AAAE,UAAMF,KAAE,WAAY,MAAI;AAAC,SAAG,CAAC;AAAA,IAAC,GAAG,EAAE;AAAE,WAAM,MAAI;AAAC,mBAAaA,EAAC;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC,CAAC,CAAC,OAAE,aAAAE,WAAG,MAAI;AAAC,QAAG,OAAK,GAAG;AAAQ,UAAG,EAAE,EAAE,GAAE,GAAG,UAAQ,IAAG;AAAG,gBAAM,KAAG,EAAE;AAAA,WAAM;AAAC,cAAMF,MAAG,CAAAA,OAAG;AAAC,gBAAMC,KAAED,GAAE,MAAM,kBAAkB;AAAE,cAAG,CAACC;AAAE,mBAAO;AAAE,gBAAK,CAAC,EAACC,IAAEC,EAAC,IAAEF;AAAE,iBAAO,OAAOC,EAAC,KAAG,SAAOC,KAAE,IAAE;AAAA,QAAI,GAAG,iBAAiB,SAAS,IAAI,EAAE,iBAAiB,4BAA4B,CAAC;AAAE,WAAG,UAAQ,WAAY,MAAI;AAAC,aAAG,KAAE,GAAE,GAAG,IAAI,GAAE,QAAM,KAAG,EAAE;AAAA,QAAC,GAAGH,KAAE,EAAE;AAAA,MAAC;AAAA,EAAC,GAAG,CAAC,EAAE,CAAC;AAAE,QAAM,KAAG,CAAAA,OAAG;AAAC,OAAI,CAAAC,OAAG,EAAEA,IAAED,EAAC,IAAEC,KAAED,EAAE;AAAA,EAAC,GAAE,KAAG,CAACA,KAAEqB,OAAI;AAAC,MAAE,EAAE,GAAE,KAAG,GAAG,IAAE,IAAE,GAAG,UAAQ,WAAY,MAAI;AAAC,SAAG,IAAE;AAAA,IAAC,GAAGrB,EAAC;AAAA,EAAC,GAAE,KAAG,CAACA,KAAEsB,OAAI;AAAC,MAAE,EAAE,GAAE,GAAG,UAAQ,WAAY,MAAI;AAAC,SAAG,WAAS,GAAG,KAAE;AAAA,IAAC,GAAGtB,EAAC;AAAA,EAAC,GAAE,KAAG,CAAAA,OAAG;AAAC,QAAIC;AAAE,QAAG,CAACD;AAAE;AAAO,UAAME,KAAE,UAAQD,KAAED,GAAE,kBAAgB,WAASC,KAAEA,KAAED,GAAE;AAAO,QAAG,EAAE,QAAME,KAAE,SAAOA,GAAE;AAAa,aAAO,GAAG,IAAI,GAAE,KAAK,GAAG,EAAC,SAAQ,KAAI,CAAC;AAAE,IAAAmB,KAAE,GAAG,IAAE,GAAG,IAAE,GAAE,GAAGnB,EAAC,GAAE,GAAG,EAAC,SAAQA,GAAC,CAAC,GAAE,EAAE,EAAE;AAAA,EAAC,GAAE,KAAG,MAAI;AAAC,IAAAwB,KAAE,GAAGJ,MAAG,GAAG,IAAEA,KAAE,GAAG,IAAE,GAAG,KAAE,GAAE,EAAE,EAAE;AAAA,EAAC,GAAE,KAAG,CAAC,EAAC,GAAEtB,IAAE,GAAEC,GAAC,MAAI;AAAC,QAAIC;AAAE,UAAMC,KAAE,EAAC,uBAAsB,OAAK,EAAC,GAAEH,IAAE,GAAEC,IAAE,OAAM,GAAE,QAAO,GAAE,KAAIA,IAAE,MAAKD,IAAE,OAAMA,IAAE,QAAOC,GAAC,GAAE;AAAE,MAAE,EAAC,OAAM,UAAQC,KAAE,QAAM,KAAG,SAAO,GAAG,UAAQ,WAASA,KAAEA,KAAE,GAAE,QAAO,GAAE,kBAAiBC,IAAE,kBAAiB,GAAG,SAAQ,uBAAsB,GAAG,SAAQ,UAASc,IAAE,aAAYE,IAAE,QAAO,GAAE,CAAC,EAAE,KAAM,CAAAnB,OAAG;AAAC,SAAGA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,KAAG,CAAAA,OAAG;AAAC,QAAG,CAACA;AAAE;AAAO,UAAMC,KAAED,IAAEE,KAAE,EAAC,GAAED,GAAE,SAAQ,GAAEA,GAAE,QAAO;AAAE,OAAGC,EAAC,GAAE,GAAG,UAAQA;AAAA,EAAC,GAAE,KAAG,CAAAF,OAAG;AAAC,QAAIC;AAAE,QAAG,CAAC;AAAG;AAAO,UAAMC,KAAEF,GAAE;AAAO,QAAG,CAACE,GAAE;AAAY;AAAO,QAAG,UAAQD,KAAE,GAAG,YAAU,WAASA,KAAE,SAAOA,GAAE,SAASC,EAAC;AAAE;AAAO,KAAC,SAAS,cAAc,QAAQ,CAAC,IAAI,GAAE,GAAG,EAAE,EAAE,KAAM,CAAAF,OAAG,QAAMA,KAAE,SAAOA,GAAE,SAASE,EAAC,CAAE,MAAI,GAAG,KAAE,GAAE,EAAE,EAAE;AAAA,EAAE,GAAE,KAAG,EAAE,IAAG,IAAG,IAAE,GAAE,KAAG,EAAE,IAAG,IAAG,IAAE,GAAE,KAAG,CAAAF,OAAG;AAAC,OAAG,OAAO,GAAE,GAAGA,EAAC;AAAA,EAAC,GAAE,KAAG,MAAI;AAAC,OAAG,OAAO,GAAE,GAAG;AAAA,EAAC,GAAE,SAAG,aAAAK,aAAG,MAAI;AAAC,QAAIL,IAAEC;AAAE,UAAMC,KAAE,UAAQF,KAAE,QAAM,KAAG,SAAO,GAAG,aAAW,WAASA,KAAEA,KAAE;AAAE,IAAAE,KAAE,GAAGA,EAAC,IAAEqB,KAAE,GAAG,WAAS,GAAG,GAAG,OAAO,KAAG,QAAM,KAAG,SAAO,GAAG,gBAAc,EAAE,EAAC,OAAM,UAAQtB,KAAE,QAAM,KAAG,SAAO,GAAG,UAAQ,WAASA,KAAEA,KAAE,GAAE,QAAO,GAAE,kBAAiB,IAAG,kBAAiB,GAAG,SAAQ,uBAAsB,GAAG,SAAQ,UAASgB,IAAE,aAAYE,IAAE,QAAO,GAAE,CAAC,EAAE,KAAM,CAAAnB,OAAG;AAAC,SAAG,WAAS,GAAGA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAG,CAAC,IAAG,IAAG,GAAE,GAAE,GAAE,QAAM,KAAG,SAAO,GAAG,OAAM,GAAEiB,IAAE,GAAE,QAAM,KAAG,SAAO,GAAG,UAASM,EAAC,CAAC;AAAE,mBAAArB,WAAG,MAAI;AAAC,QAAIF,IAAEC;AAAE,UAAMC,KAAE,IAAI,IAAI,EAAE;AAAE,OAAG,QAAS,CAAAF,OAAG;AAAC,OAAC,QAAM,IAAE,SAAO,EAAEA,EAAC,MAAIE,GAAE,IAAI,EAAC,SAAQF,GAAC,CAAC;AAAA,IAAC,CAAE;AAAE,UAAMG,KAAE,SAAS,cAAc,QAAQ,CAAC,IAAI;AAAE,IAAAA,MAAG,EAAE,QAAM,IAAE,SAAO,EAAEA,EAAC,MAAID,GAAE,IAAI,EAAC,SAAQC,GAAC,CAAC;AAAE,UAAMC,KAAE,MAAI;AAAC,SAAG,KAAE;AAAA,IAAC,GAAEC,KAAE,EAAE,EAAE,GAAEC,KAAE,EAAE,GAAG,OAAO;AAAE,OAAG,WAAS,OAAO,iBAAiB,UAASF,EAAC,GAAE,QAAMC,MAAGA,GAAE,iBAAiB,UAASD,EAAC,GAAE,QAAME,MAAGA,GAAE,iBAAiB,UAASF,EAAC;AAAG,QAAIG,KAAE;AAAK,OAAG,SAAO,OAAO,iBAAiB,UAASH,EAAC,IAAE,MAAI,GAAG,YAAUG,KAAE,WAAE,IAAG,GAAG,SAAQ,IAAG,EAAC,gBAAe,MAAG,eAAc,MAAG,aAAY,KAAE,CAAC;AAAG,UAAMC,KAAE,CAAAR,OAAG;AAAC,mBAAWA,GAAE,OAAK,GAAG,KAAE;AAAA,IAAC;AAAE,OAAG,UAAQ,OAAO,iBAAiB,WAAUQ,EAAC,GAAE,GAAG,sBAAoB,OAAO,iBAAiB,SAAQ,EAAE;AAAE,UAAMM,KAAE,CAAC,GAAEkB,KAAE,CAAAhC,OAAG,SAAS,QAAMA,KAAE,SAAOA,GAAE,YAAU,QAAM,KAAG,SAAO,GAAG,SAASA,GAAE,MAAM,EAAE,GAAEiC,KAAE,CAAAjC,OAAG;AAAC,YAAIgC,GAAEhC,EAAC,KAAG,GAAGA,EAAC;AAAA,IAAC,GAAEkC,KAAE,CAAAlC,OAAG;AAAC,YAAIgC,GAAEhC,EAAC,KAAG,GAAG;AAAA,IAAC,GAAEmC,KAAE,CAAC,aAAY,YAAW,cAAa,cAAa,SAAQ,MAAM,GAAEjB,KAAE,CAAC,SAAQ,YAAW,aAAY,SAAS;AAAE,WAAO,QAAQ,EAAE,EAAE,QAAS,CAAC,CAAClB,IAAEC,EAAC,MAAI;AAAC,MAAAA,OAAIkC,GAAE,SAASnC,EAAC,IAAEc,GAAE,KAAK,EAAC,OAAMd,IAAE,UAAS,GAAE,CAAC,IAAEkB,GAAE,SAASlB,EAAC,KAAGc,GAAE,KAAK,EAAC,OAAMd,IAAE,UAASiC,GAAC,CAAC;AAAA,IAAE,CAAE,GAAE,OAAO,QAAQ,EAAE,EAAE,QAAS,CAAC,CAACjC,IAAEC,EAAC,MAAI;AAAC,MAAAA,OAAIkC,GAAE,SAASnC,EAAC,IAAEc,GAAE,KAAK,EAAC,OAAMd,IAAE,UAAS,GAAE,CAAC,IAAEkB,GAAE,SAASlB,EAAC,KAAGc,GAAE,KAAK,EAAC,OAAMd,IAAE,UAASkC,GAAC,CAAC;AAAA,IAAE,CAAE,GAAEX,MAAGT,GAAE,KAAK,EAAC,OAAM,eAAc,UAAS,GAAE,CAAC;AAAE,UAAMC,KAAE,MAAI;AAAC,SAAG,UAAQ;AAAA,IAAE,GAAEC,KAAE,MAAI;AAAC,SAAG,UAAQ,OAAG,GAAG;AAAA,IAAC,GAAEC,KAAES,OAAI,GAAG,YAAU,GAAG;AAAY,WAAOT,OAAI,UAAQjB,KAAE,GAAG,YAAU,WAASA,MAAGA,GAAE,iBAAiB,aAAYe,EAAC,GAAE,UAAQd,KAAE,GAAG,YAAU,WAASA,MAAGA,GAAE,iBAAiB,YAAWe,EAAC,IAAGF,GAAE,QAAS,CAAC,EAAC,OAAMd,IAAE,UAASC,GAAC,MAAI;AAAC,MAAAC,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAIC;AAAE,kBAAQA,KAAED,GAAE,YAAU,WAASC,MAAGA,GAAE,iBAAiBH,IAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,MAAI;AAAC,UAAID,IAAEC;AAAE,SAAG,WAAS,OAAO,oBAAoB,UAASG,EAAC,GAAE,QAAMC,MAAGA,GAAE,oBAAoB,UAASD,EAAC,GAAE,QAAME,MAAGA,GAAE,oBAAoB,UAASF,EAAC,IAAG,GAAG,SAAO,OAAO,oBAAoB,UAASA,EAAC,IAAE,QAAMG,MAAGA,GAAE,GAAE,GAAG,sBAAoB,OAAO,oBAAoB,SAAQ,EAAE,GAAE,GAAG,UAAQ,OAAO,oBAAoB,WAAUC,EAAC,GAAES,OAAI,UAAQjB,KAAE,GAAG,YAAU,WAASA,MAAGA,GAAE,oBAAoB,aAAYe,EAAC,GAAE,UAAQd,KAAE,GAAG,YAAU,WAASA,MAAGA,GAAE,oBAAoB,YAAWe,EAAC,IAAGF,GAAE,QAAS,CAAC,EAAC,OAAMd,IAAE,UAASC,GAAC,MAAI;AAAC,QAAAC,GAAE,QAAS,CAAAA,OAAG;AAAC,cAAIC;AAAE,oBAAQA,KAAED,GAAE,YAAU,WAASC,MAAGA,GAAE,oBAAoBH,IAAEC,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG6B,IAAEC,IAAE,GAAE,IAAGV,IAAEC,EAAC,CAAC,OAAE,aAAApB,WAAG,MAAI;AAAC,QAAIF,IAAEC;AAAE,QAAIC,KAAE,UAAQD,KAAE,UAAQD,KAAE,QAAM,KAAG,SAAO,GAAG,iBAAe,WAASA,KAAEA,KAAE,MAAI,WAASC,KAAEA,KAAE;AAAG,KAACC,MAAGC,OAAID,KAAE,qBAAqBC,GAAE,QAAQ,MAAK,KAAK,CAAC;AAAM,UAAMC,KAAE,IAAI,iBAAkB,CAAAJ,OAAG;AAAC,YAAMC,KAAE,CAAC,GAAEG,KAAE,CAAC;AAAE,MAAAJ,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAG,iBAAeA,GAAE,QAAM,sBAAoBA,GAAE,eAAc;AAAC,UAAAA,GAAE,OAAO,aAAa,iBAAiB,MAAIG,KAAEF,GAAE,KAAKD,GAAE,MAAM,IAAEA,GAAE,aAAWG,MAAGC,GAAE,KAAKJ,GAAE,MAAM;AAAA,QAAC;AAAC,YAAG,gBAAcA,GAAE,MAAK;AAAC,cAAG,IAAG;AAAC,kBAAMC,KAAE,CAAC,GAAGD,GAAE,YAAY,EAAE,OAAQ,CAAAA,OAAG,MAAIA,GAAE,QAAS;AAAE,gBAAGE;AAAE,kBAAG;AAAC,gBAAAE,GAAE,KAAK,GAAGH,GAAE,OAAQ,CAAAD,OAAGA,GAAE,QAAQE,EAAC,CAAE,CAAC,GAAEE,GAAE,KAAK,GAAGH,GAAE,QAAS,CAAAD,OAAG,CAAC,GAAGA,GAAE,iBAAiBE,EAAC,CAAC,CAAE,CAAC;AAAA,cAAC,SAAOF,IAAE;AAAA,cAAC;AAAC,YAAAC,GAAE,KAAM,CAAAD,OAAG;AAAC,kBAAIC;AAAE,qBAAM,CAAC,EAAE,UAAQA,KAAE,QAAMD,KAAE,SAAOA,GAAE,aAAW,WAASC,KAAE,SAAOA,GAAE,KAAKD,IAAE,EAAE,OAAK,GAAG,KAAE,GAAE,GAAG,KAAE,GAAE,GAAG,IAAI,GAAE,EAAE,EAAE,GAAE,EAAE,EAAE,GAAE;AAAA,YAAG,CAAE;AAAA,UAAC;AAAC,cAAGE;AAAE,gBAAG;AAAC,oBAAMC,KAAE,CAAC,GAAGH,GAAE,UAAU,EAAE,OAAQ,CAAAA,OAAG,MAAIA,GAAE,QAAS;AAAE,cAAAC,GAAE,KAAK,GAAGE,GAAE,OAAQ,CAAAH,OAAGA,GAAE,QAAQE,EAAC,CAAE,CAAC,GAAED,GAAE,KAAK,GAAGE,GAAE,QAAS,CAAAH,OAAG,CAAC,GAAGA,GAAE,iBAAiBE,EAAC,CAAC,CAAE,CAAC;AAAA,YAAC,SAAOF,IAAE;AAAA,YAAC;AAAA,QAAC;AAAA,MAAC,CAAE,IAAGC,GAAE,UAAQG,GAAE,WAAS,GAAI,CAAAJ,OAAG,CAAC,GAAGA,GAAE,OAAQ,CAAAA,OAAG,CAACI,GAAE,SAASJ,EAAC,CAAE,GAAE,GAAGC,EAAC,CAAE;AAAA,IAAC,CAAE;AAAE,WAAOG,GAAE,QAAQ,SAAS,MAAK,EAAC,WAAU,MAAG,SAAQ,MAAG,YAAW,MAAG,iBAAgB,CAAC,iBAAiB,GAAE,mBAAkB,KAAE,CAAC,GAAE,MAAI;AAAC,MAAAA,GAAE,WAAW;AAAA,IAAC;AAAA,EAAC,GAAG,CAACD,IAAE,GAAE,QAAM,KAAG,SAAO,GAAG,cAAa,EAAE,CAAC,OAAE,aAAAD,WAAG,MAAI;AAAC,OAAG;AAAA,EAAC,GAAG,CAAC,EAAE,CAAC,OAAE,aAAAA,WAAG,MAAI;AAAC,QAAG,EAAE,QAAM,IAAE,SAAO,EAAE;AAAS,aAAM,MAAI;AAAK,UAAMF,KAAE,IAAI,eAAgB,MAAI;AAAC,iBAAY,MAAI,GAAG,CAAE;AAAA,IAAC,CAAE;AAAE,WAAOA,GAAE,QAAQ,EAAE,OAAO,GAAE,MAAI;AAAC,MAAAA,GAAE,WAAW;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC,GAAE,QAAM,IAAE,SAAO,EAAE,OAAO,CAAC,OAAE,aAAAE,WAAG,MAAI;AAAC,QAAIF;AAAE,UAAMC,KAAE,SAAS,cAAc,QAAQ,CAAC,IAAI,GAAEC,KAAE,CAAC,GAAG,IAAGD,EAAC;AAAE,UAAIC,GAAE,SAAS,EAAE,KAAG,GAAG,UAAQF,KAAE,GAAG,CAAC,MAAI,WAASA,KAAEA,KAAEC,EAAC;AAAA,EAAC,GAAG,CAAC,GAAE,IAAG,EAAE,CAAC,OAAE,aAAAC,WAAG,OAAK,KAAG,GAAG,IAAE,GAAE,MAAI;AAAC,MAAE,EAAE,GAAE,EAAE,EAAE;AAAA,EAAC,IAAI,CAAC,CAAC,OAAE,aAAAA,WAAG,MAAI;AAAC,QAAIF;AAAE,QAAIC,KAAE,UAAQD,KAAE,QAAM,KAAG,SAAO,GAAG,iBAAe,WAASA,KAAEA,KAAE;AAAE,QAAG,CAACC,MAAGE,OAAIF,KAAE,qBAAqBE,GAAE,QAAQ,MAAK,KAAK,CAAC,OAAMF;AAAE,UAAG;AAAC,cAAMD,KAAE,MAAM,KAAK,SAAS,iBAAiBC,EAAC,CAAC;AAAE,WAAGD,EAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,WAAG,CAAC,CAAC;AAAA,MAAC;AAAA,EAAC,GAAG,CAACG,IAAE,GAAE,QAAM,KAAG,SAAO,GAAG,YAAY,CAAC,OAAE,aAAAD,WAAG,MAAI;AAAC,OAAG,YAAU,EAAE,EAAE,GAAE,GAAGmB,EAAC;AAAA,EAAE,GAAG,CAACA,EAAC,CAAC;AAAE,QAAM,KAAG,UAAQ,KAAG,QAAM,KAAG,SAAO,GAAG,YAAU,WAAS,KAAG,KAAG,GAAE,KAAG,MAAI,OAAO,KAAK,GAAG,aAAa,EAAE,SAAO;AAAE,aAAO,aAAAP,qBAAEb,IAAG,OAAK,EAAC,MAAK,CAAAD,OAAG;AAAC,QAAG,QAAMA,KAAE,SAAOA,GAAE;AAAa,UAAG;AAAC,iBAAS,cAAcA,GAAE,YAAY;AAAA,MAAC,SAAOC,IAAE;AAAC,eAAO,KAAK,QAAQ,KAAK,oBAAoBD,GAAE,YAAY,+BAA+B;AAAA,MAAC;AAAC,OAAG,QAAMA,KAAEA,KAAE,IAAI,IAAG,QAAMA,KAAE,SAAOA,GAAE,SAAO,GAAGA,GAAE,KAAK,IAAE,GAAG,IAAE;AAAA,EAAC,GAAE,OAAM,CAAAA,OAAG;AAAC,KAAC,QAAMA,KAAE,SAAOA,GAAE,SAAO,GAAGA,GAAE,KAAK,IAAE,GAAG,KAAE;AAAA,EAAC,GAAE,cAAa,IAAG,OAAM,GAAG,OAAM,QAAO,QAAQ,MAAI,CAACwB,MAAG,MAAI,EAAE,EAAC,EAAG,GAAE,MAAI,CAACA,MAAG,KAAG,aAAAxB,QAAE,cAAcoB,IAAE,EAAC,IAAGjB,IAAE,MAAK,IAAG,eAAU,kBAAAe,SAAE,iBAAgB,EAAE,SAAQ,EAAE,SAAQ,EAAE,CAAC,GAAEZ,IAAE,wBAAwB,GAAG,KAAK,IAAG,EAAE,KAAG,SAAO,SAAS,GAAE,KAAG,wBAAsB,0BAAyB,YAAUW,MAAG,EAAE,OAAMS,MAAG,EAAE,SAAS,GAAE,iBAAgB,CAAA1B,OAAG;AAAC,MAAE,EAAE,GAAE,MAAI,cAAYA,GAAE,iBAAe,GAAG,KAAE,GAAE,GAAG,IAAI,GAAE,QAAM,KAAG,EAAE;AAAA,EAAE,GAAE,OAAM,EAAC,GAAG,GAAE,GAAG,GAAG,eAAc,SAAQ,WAAS,MAAI,KAAG,KAAG,OAAM,GAAE,KAAI,GAAE,GAAE,IAAG,aAAAA,QAAE,cAAcoB,IAAE,EAAC,eAAU,kBAAAF,SAAE,uBAAsB,EAAE,OAAM,EAAE,OAAMX,IAAEkB,MAAG,EAAE,OAAO,GAAE,OAAM,EAAC,GAAG,GAAG,oBAAmB,YAAW,KAAG,qDAAqD,EAAE,UAAQ,OAAM,GAAE,KAAI,GAAE,CAAC,CAAC,IAAE;AAAI;AAAh3R,IAAk3R,IAAE,CAAC,EAAC,SAAQxB,GAAC,MAAI,aAAAD,QAAE,cAAc,QAAO,EAAC,yBAAwB,EAAC,QAAOC,GAAC,EAAC,CAAC;AAA97R,IAAg8R,IAAE,aAAAD,QAAE,WAAY,CAAC,EAAC,IAAGC,IAAE,UAASE,IAAE,cAAaE,IAAE,SAAQC,IAAE,MAAKC,IAAE,QAAOO,IAAE,WAAU,GAAE,gBAAe,GAAE,SAAQ,IAAE,QAAO,OAAM,IAAE,OAAM,QAAO,IAAE,IAAG,SAAQ,IAAE,OAAM,UAASC,KAAE,MAAK,QAAOC,KAAE,CAAC,OAAO,GAAE,aAAYC,KAAE,OAAG,kBAAiBE,KAAE,YAAW,aAAYC,IAAE,WAAUgB,KAAE,GAAE,WAAUC,KAAE,GAAE,OAAMf,KAAE,OAAG,QAAOgB,KAAE,OAAG,SAAQf,KAAE,OAAG,WAAUgB,KAAE,OAAG,YAAWC,KAAE,OAAG,eAAcC,KAAE,OAAG,eAAcjB,KAAE,OAAG,YAAWC,IAAE,aAAYC,IAAE,mBAAkBC,IAAE,oBAAmBC,KAAE,OAAG,OAAMc,IAAE,UAASC,IAAE,QAAOZ,IAAE,eAAc,IAAE,OAAG,uBAAsB,IAAE,OAAG,QAAO,GAAE,SAAQ,GAAE,YAAW,GAAE,WAAU,GAAE,WAAU,GAAE,WAAU,GAAE,gBAAe,GAAE,MAAK,IAAE,UAAS,GAAE,MAAI;AAAC,QAAK,CAAC,GAAE,EAAE,QAAE,aAAA3B,UAAEE,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAF,UAAEG,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAH,UAAE,CAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAE,CAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAE,CAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAEgC,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAhC,UAAEiC,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAjC,UAAEkB,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAlB,UAAEkC,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAlC,UAAE,CAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAEY,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAZ,UAAEe,EAAC,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAf,UAAE,IAAI,GAAE,CAAC,IAAG,EAAE,QAAE,aAAAA,UAAE,IAAI,GAAE,SAAG,aAAAI,QAAE,CAAC,GAAE,EAAC,YAAW,IAAG,cAAa,GAAE,IAAE,EAAEP,EAAC,GAAE,KAAG,CAAAD,OAAG,QAAMA,KAAE,SAAOA,GAAE,kBAAkB,EAAE,OAAQ,CAACC,IAAEC,OAAI;AAAC,QAAIC;AAAE,QAAGD,GAAE,WAAW,eAAe,GAAE;AAAC,MAAAD,GAAEC,GAAE,QAAQ,kBAAiB,EAAE,CAAC,IAAE,UAAQC,KAAE,QAAMH,KAAE,SAAOA,GAAE,aAAaE,EAAC,MAAI,WAASC,KAAEA,KAAE;AAAA,IAAI;AAAC,WAAOF;AAAA,EAAC,GAAG,CAAC,CAAC,GAAE,KAAG,CAAAD,OAAG;AAAC,UAAMC,KAAE,EAAC,OAAM,CAAAD,OAAG;AAAC,UAAIC;AAAE,SAAG,UAAQA,KAAED,OAAI,WAASC,KAAEA,KAAE,CAAC;AAAA,IAAC,GAAE,SAAQ,CAAAD,OAAG;AAAC,SAAG,QAAMA,KAAEA,KAAEM,EAAC;AAAA,IAAC,GAAE,MAAK,CAAAN,OAAG;AAAC,SAAG,QAAMA,KAAEA,KAAEO,EAAC;AAAA,IAAC,GAAE,SAAQ,CAAAP,OAAG;AAAC,UAAIC;AAAE,SAAG,UAAQA,KAAED,OAAI,WAASC,KAAEA,KAAE,CAAC;AAAA,IAAC,GAAE,QAAO,CAAAD,OAAG;AAAC,SAAG,SAAOA,KAAE,IAAE,OAAOA,EAAC,CAAC;AAAA,IAAC,GAAE,SAAQ,CAAAA,OAAG;AAAC,UAAIC;AAAE,SAAG,UAAQA,KAAED,OAAI,WAASC,KAAEA,KAAE,CAAC;AAAA,IAAC,GAAE,QAAO,CAAAD,OAAG;AAAC,YAAMC,KAAE,QAAMD,KAAE,SAAOA,GAAE,MAAM,GAAG;AAAE,SAAG,QAAMC,KAAEA,KAAEe,EAAC;AAAA,IAAC,GAAE,qBAAoB,CAAAhB,OAAG;AAAC,UAAIC;AAAE,SAAG,UAAQA,KAAED,OAAI,WAASC,KAAEA,KAAEkB,EAAC;AAAA,IAAC,GAAE,cAAa,CAAAnB,OAAG;AAAC,SAAG,SAAOA,KAAEoC,KAAE,OAAOpC,EAAC,CAAC;AAAA,IAAC,GAAE,cAAa,CAAAA,OAAG;AAAC,SAAG,SAAOA,KAAEqC,KAAE,OAAOrC,EAAC,CAAC;AAAA,IAAC,GAAE,OAAM,CAAAA,OAAG;AAAC,SAAG,SAAOA,KAAEsB,KAAE,WAAStB,EAAC;AAAA,IAAC,GAAE,QAAO,CAAAA,OAAG;AAAC,SAAG,SAAOA,KAAEsC,KAAE,WAAStC,EAAC;AAAA,IAAC,GAAE,cAAa,CAAAA,OAAG;AAAC,SAAGA,EAAC;AAAA,IAAC,EAAC;AAAE,WAAO,OAAOC,EAAC,EAAE,QAAS,CAAAD,OAAGA,GAAE,IAAI,CAAE,GAAE,OAAO,QAAQA,EAAC,EAAE,QAAS,CAAC,CAACA,IAAEE,EAAC,MAAI;AAAC,UAAIC;AAAE,gBAAQA,KAAEF,GAAED,EAAC,MAAI,WAASG,MAAGA,GAAE,KAAKF,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAE,mBAAAA,WAAG,MAAI;AAAC,OAAGI,EAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC,OAAE,aAAAJ,WAAG,MAAI;AAAC,OAAGK,EAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC,OAAE,aAAAL,WAAG,MAAI;AAAC,OAAG,CAAC;AAAA,EAAC,GAAG,CAAC,CAAC,CAAC,OAAE,aAAAA,WAAG,MAAI;AAAC,OAAG,CAAC;AAAA,EAAC,GAAG,CAAC,CAAC,CAAC,OAAE,aAAAA,WAAG,MAAI;AAAC,OAAG,CAAC;AAAA,EAAC,GAAG,CAAC,CAAC,CAAC,OAAE,aAAAA,WAAG,MAAI;AAAC,OAAGkC,EAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC,OAAE,aAAAlC,WAAG,MAAI;AAAC,OAAGmC,EAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC,OAAE,aAAAnC,WAAG,MAAI;AAAC,OAAGoB,EAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC,OAAE,aAAApB,WAAG,MAAI;AAAC,OAAGoC,EAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC,OAAE,aAAApC,WAAG,MAAI;AAAC,OAAGiB,EAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC,OAAE,aAAAjB,WAAG,MAAI;AAAC,OAAG,YAAU,KAAG,QAAQ,KAAK,oEAAoE;AAAA,EAAC,GAAG,CAAC,CAAC,CAAC,OAAE,aAAAA,WAAG,MAAI;AAAC,mBAAa,OAAO,UAAQ,OAAO,cAAc,IAAI,YAAY,+BAA8B,EAAC,QAAO,EAAC,aAAY,WAAS,GAAE,aAAY,EAAC,EAAC,CAAC,CAAC;AAAA,EAAC,GAAG,CAAC,CAAC,OAAE,aAAAA,WAAG,MAAI;AAAC,QAAIF;AAAE,UAAME,KAAE,IAAI,IAAI,EAAE;AAAE,QAAIE,KAAEC;AAAE,QAAG,CAACD,MAAGH,OAAIG,KAAE,qBAAqBH,GAAE,QAAQ,MAAK,KAAK,CAAC,OAAMG;AAAE,UAAG;AAAC,iBAAS,iBAAiBA,EAAC,EAAE,QAAS,CAAAJ,OAAG;AAAC,UAAAE,GAAE,IAAI,EAAC,SAAQF,GAAC,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC,SAAOA,IAAE;AAAC,gBAAQ,KAAK,oBAAoBI,EAAC,+BAA+B;AAAA,MAAC;AAAC,UAAME,KAAE,SAAS,cAAc,QAAQH,EAAC,IAAI;AAAE,QAAGG,MAAGJ,GAAE,IAAI,EAAC,SAAQI,GAAC,CAAC,GAAE,CAACJ,GAAE;AAAK,aAAM,MAAI;AAAK,UAAMK,KAAE,UAAQP,KAAE,QAAM,KAAG,KAAGM,OAAI,WAASN,KAAEA,KAAE,GAAG,SAAQQ,KAAE,IAAI,iBAAkB,CAAAR,OAAG;AAAC,MAAAA,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAIC;AAAE,YAAG,CAACM,MAAG,iBAAeP,GAAE,QAAM,EAAE,UAAQC,KAAED,GAAE,kBAAgB,WAASC,KAAE,SAAOA,GAAE,WAAW,eAAe;AAAG;AAAO,cAAMC,KAAE,GAAGK,EAAC;AAAE,WAAGL,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAEY,KAAE,EAAC,YAAW,MAAG,WAAU,OAAG,SAAQ,MAAE;AAAE,QAAGP,IAAE;AAAC,YAAMP,KAAE,GAAGO,EAAC;AAAE,SAAGP,EAAC,GAAEQ,GAAE,QAAQD,IAAEO,EAAC;AAAA,IAAC;AAAC,WAAM,MAAI;AAAC,MAAAN,GAAE,WAAW;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC,IAAG,IAAG,IAAGL,IAAEE,EAAC,CAAC,OAAE,aAAAH,WAAG,MAAI;AAAC,KAAC,QAAMwC,KAAE,SAAOA,GAAE,WAAS,QAAQ,KAAK,uEAAuE,GAAE,KAAG,CAAC,EAAE,UAAS,GAAG,CAAC,EAAE,KAAG,QAAQ,KAAK,oBAAoB,CAAC,8BAA8B,IAAG,QAAMA,KAAE,SAAOA,GAAE,YAAU,QAAQ,KAAK,yEAAyE,GAAE,KAAG,CAAC,EAAE,WAAU,GAAG,CAAC,EAAE,KAAG,QAAQ,KAAK,oBAAoB,CAAC,+BAA+B;AAAA,EAAC,GAAG,CAAC,CAAC;AAAE,MAAI,KAAG3B;AAAE,QAAM,SAAG,aAAAP,QAAE,IAAI;AAAE,MAAGM,IAAE;AAAC,UAAMb,KAAEa,GAAE,EAAC,UAAS,QAAM,KAAG,SAAO,GAAG,aAAa,sBAAsB,MAAI,KAAG,MAAK,cAAa,GAAE,CAAC;AAAE,SAAGb,KAAE,aAAAD,QAAE,cAAc,OAAM,EAAC,KAAI,IAAG,WAAU,gCAA+B,GAAEC,EAAC,IAAE;AAAA,EAAI;AAAM,UAAI,KAAG;AAAG,SAAK,KAAG,aAAAD,QAAE,cAAc,GAAE,EAAC,SAAQ,GAAE,CAAC;AAAG,QAAM,KAAG,EAAC,YAAW,GAAE,IAAGC,IAAE,UAASE,IAAE,cAAaE,IAAE,eAAU,kBAAAa,SAAE,GAAE,EAAE,GAAE,gBAAe,GAAE,SAAQ,IAAG,mBAAkB,IAAG,OAAM,IAAG,SAAQ,IAAG,QAAO,IAAG,SAAQ,IAAG,QAAO,IAAG,aAAYD,IAAE,kBAAiB,IAAG,aAAYG,IAAE,WAAU,IAAG,WAAU,IAAG,OAAM,IAAG,QAAO,IAAG,SAAQG,IAAE,WAAUgB,IAAE,YAAWC,IAAE,eAAcC,IAAE,eAAcjB,IAAE,YAAWC,IAAE,aAAYC,IAAE,mBAAkBC,IAAE,oBAAmBC,IAAE,OAAMc,IAAE,UAASC,IAAE,QAAOZ,IAAE,eAAc,GAAE,QAAO,GAAE,SAAQ,GAAE,YAAW,GAAE,WAAU,GAAE,WAAU,GAAE,WAAU,GAAE,gBAAe,GAAE,cAAa,IAAG,iBAAgB,CAAA/B,OAAG,GAAGA,EAAC,GAAE,MAAK,EAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,GAAE,EAAC,GAAG,GAAE,CAAC;AAAC,CAAE;AAAE,eAAa,OAAO,UAAQ,OAAO,iBAAiB,+BAA+B,CAAAA,OAAG;AAAC,EAAAA,GAAE,OAAO,eAAa,EAAE,EAAC,KAAI,oyBAAmyB,MAAK,OAAM,CAAC,GAAEA,GAAE,OAAO,eAAa,EAAE,EAAC,KAAI;AAAA,qjCAC7ulB,MAAK,OAAM,CAAC;AAAC,CAAE;", "names": ["x", "y", "platform", "x", "y", "i", "max", "offset", "platform", "placements", "sides", "side", "a", "b", "placement", "overflow", "platform", "x", "y", "min", "max", "getComputedStyle", "e", "getComputedStyle", "$", "x", "y", "a", "b", "offset", "shift", "flip", "arrow", "computePosition", "e", "t", "o", "l", "r", "n", "i", "c", "s", "offset", "flip", "shift", "arrow", "computePosition", "a", "h", "w", "b", "y", "S", "g", "A", "O", "T", "x", "N", "$", "I", "B", "q", "H", "M", "u", "p", "v", "m", "E", "_", "k", "L", "C", "R", "z", "D"]}