import axios from 'axios'
import MockAdvertisementService from './MockAdvertisementService'

const API_URL = 'http://localhost:8000/api/advertisements'
// Use environment variable to determine if we should use mock data
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'

class AdvertisementService {
  // Get all advertisements
  async getAdvertisements(filters = {}) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.getAdvertisements(filters)
    }

    try {
      const response = await axios.get(API_URL, { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching advertisements:', error)
      throw error
    }
  }

  // Get advertisement by ID
  async getAdvertisement(id) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.getAdvertisement(id)
    }

    try {
      const response = await axios.get(`${API_URL}/${id}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching advertisement ${id}:`, error)
      throw error
    }
  }

  // Create new advertisement
  async createAdvertisement(advertisementData) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.createAdvertisement(advertisementData)
    }

    try {
      const response = await axios.post(API_URL, advertisementData)
      return response.data
    } catch (error) {
      console.error('Error creating advertisement:', error)
      throw error
    }
  }

  // Update advertisement
  async updateAdvertisement(id, advertisementData) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.updateAdvertisement(id, advertisementData)
    }

    try {
      const response = await axios.put(`${API_URL}/${id}`, advertisementData)
      return response.data
    } catch (error) {
      console.error(`Error updating advertisement ${id}:`, error)
      throw error
    }
  }

  // Delete advertisement
  async deleteAdvertisement(id) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.deleteAdvertisement(id)
    }

    try {
      const response = await axios.delete(`${API_URL}/${id}`)
      return response.data
    } catch (error) {
      console.error(`Error deleting advertisement ${id}:`, error)
      throw error
    }
  }

  // Get user advertisements
  async getUserAdvertisements() {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.getUserAdvertisements()
    }

    try {
      const response = await axios.get(`${API_URL}/user`)
      return response.data
    } catch (error) {
      console.error('Error fetching user advertisements:', error)
      throw error
    }
  }

  // Get featured advertisements
  async getFeaturedAdvertisements() {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.getFeaturedAdvertisements()
    }

    try {
      const response = await axios.get(`${API_URL}/featured`)
      return response.data
    } catch (error) {
      console.error('Error fetching featured advertisements:', error)
      throw error
    }
  }

  // Get personalized advertisements
  async getPersonalizedAdvertisements(limit = 5) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.getPersonalizedAdvertisements(limit)
    }

    try {
      const response = await axios.get(`${API_URL}/personalized`, { params: { limit } })
      return response.data
    } catch (error) {
      console.error('Error fetching personalized advertisements:', error)
      throw error
    }
  }

  // Get advertisements related to a product or category
  async getRelatedAdvertisements(params = {}) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.getRelatedAdvertisements(params)
    }

    try {
      const response = await axios.get(`${API_URL}/related-to-product`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching related advertisements:', error)
      throw error
    }
  }

  // Track advertisement click
  async trackAdClick(id) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.trackAdClick(id)
    }

    try {
      const response = await axios.post(`${API_URL}/${id}/track-click`)
      return response.data
    } catch (error) {
      console.error(`Error tracking advertisement click ${id}:`, error)
      throw error
    }
  }

  // Approve advertisement (for moderators/admins)
  async approveAdvertisement(id) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.approveAdvertisement(id)
    }

    try {
      const response = await axios.put(`${API_URL}/${id}/approve`)
      return response.data
    } catch (error) {
      console.error(`Error approving advertisement ${id}:`, error)
      throw error
    }
  }

  // Reject advertisement (for moderators/admins)
  async rejectAdvertisement(id, reason) {
    if (USE_MOCK_DATA) {
      return MockAdvertisementService.rejectAdvertisement(id, reason)
    }

    try {
      const response = await axios.put(`${API_URL}/${id}/reject`, { reason })
      return response.data
    } catch (error) {
      console.error(`Error rejecting advertisement ${id}:`, error)
      throw error
    }
  }
}

export default new AdvertisementService()
