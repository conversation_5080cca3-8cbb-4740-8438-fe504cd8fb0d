{"version": 3, "sources": ["../../react-simple-maps/dist/index.es.js", "../../d3-geo/src/index.js", "../../d3-geo/src/math.js", "../../d3-geo/src/noop.js", "../../d3-geo/src/stream.js", "../../d3-geo/src/area.js", "../../d3-geo/src/cartesian.js", "../../d3-geo/src/bounds.js", "../../d3-geo/src/centroid.js", "../../d3-geo/src/constant.js", "../../d3-geo/src/compose.js", "../../d3-geo/src/rotation.js", "../../d3-geo/src/circle.js", "../../d3-geo/src/clip/buffer.js", "../../d3-geo/src/pointEqual.js", "../../d3-geo/src/clip/rejoin.js", "../../d3-geo/src/polygonContains.js", "../../d3-geo/src/clip/index.js", "../../d3-geo/src/clip/antimeridian.js", "../../d3-geo/src/clip/circle.js", "../../d3-geo/src/clip/line.js", "../../d3-geo/src/clip/rectangle.js", "../../d3-geo/src/clip/extent.js", "../../d3-geo/src/length.js", "../../d3-geo/src/distance.js", "../../d3-geo/src/contains.js", "../../d3-geo/src/graticule.js", "../../d3-geo/src/interpolate.js", "../../d3-geo/src/identity.js", "../../d3-geo/src/path/area.js", "../../d3-geo/src/path/bounds.js", "../../d3-geo/src/path/centroid.js", "../../d3-geo/src/path/context.js", "../../d3-geo/src/path/measure.js", "../../d3-geo/src/path/string.js", "../../d3-geo/src/path/index.js", "../../d3-geo/src/transform.js", "../../d3-geo/src/projection/fit.js", "../../d3-geo/src/projection/resample.js", "../../d3-geo/src/projection/index.js", "../../d3-geo/src/projection/conic.js", "../../d3-geo/src/projection/cylindricalEqualArea.js", "../../d3-geo/src/projection/conicEqualArea.js", "../../d3-geo/src/projection/albers.js", "../../d3-geo/src/projection/albersUsa.js", "../../d3-geo/src/projection/azimuthal.js", "../../d3-geo/src/projection/azimuthalEqualArea.js", "../../d3-geo/src/projection/azimuthalEquidistant.js", "../../d3-geo/src/projection/mercator.js", "../../d3-geo/src/projection/conicConformal.js", "../../d3-geo/src/projection/equirectangular.js", "../../d3-geo/src/projection/conicEquidistant.js", "../../d3-geo/src/projection/equalEarth.js", "../../d3-geo/src/projection/gnomonic.js", "../../d3-geo/src/projection/identity.js", "../../d3-geo/src/projection/naturalEarth1.js", "../../d3-geo/src/projection/orthographic.js", "../../d3-geo/src/projection/stereographic.js", "../../d3-geo/src/projection/transverseMercator.js", "../../topojson-client/src/identity.js", "../../topojson-client/src/transform.js", "../../topojson-client/src/reverse.js", "../../topojson-client/src/feature.js", "../../topojson-client/src/stitch.js", "../../topojson-client/src/mesh.js", "../../d3-dispatch/src/dispatch.js", "../../d3-selection/src/namespaces.js", "../../d3-selection/src/namespace.js", "../../d3-selection/src/creator.js", "../../d3-selection/src/selector.js", "../../d3-selection/src/selection/select.js", "../../d3-selection/src/array.js", "../../d3-selection/src/selectorAll.js", "../../d3-selection/src/selection/selectAll.js", "../../d3-selection/src/matcher.js", "../../d3-selection/src/selection/selectChild.js", "../../d3-selection/src/selection/selectChildren.js", "../../d3-selection/src/selection/filter.js", "../../d3-selection/src/selection/sparse.js", "../../d3-selection/src/selection/enter.js", "../../d3-selection/src/constant.js", "../../d3-selection/src/selection/data.js", "../../d3-selection/src/selection/exit.js", "../../d3-selection/src/selection/join.js", "../../d3-selection/src/selection/merge.js", "../../d3-selection/src/selection/order.js", "../../d3-selection/src/selection/sort.js", "../../d3-selection/src/selection/call.js", "../../d3-selection/src/selection/nodes.js", "../../d3-selection/src/selection/node.js", "../../d3-selection/src/selection/size.js", "../../d3-selection/src/selection/empty.js", "../../d3-selection/src/selection/each.js", "../../d3-selection/src/selection/attr.js", "../../d3-selection/src/window.js", "../../d3-selection/src/selection/style.js", "../../d3-selection/src/selection/property.js", "../../d3-selection/src/selection/classed.js", "../../d3-selection/src/selection/text.js", "../../d3-selection/src/selection/html.js", "../../d3-selection/src/selection/raise.js", "../../d3-selection/src/selection/lower.js", "../../d3-selection/src/selection/append.js", "../../d3-selection/src/selection/insert.js", "../../d3-selection/src/selection/remove.js", "../../d3-selection/src/selection/clone.js", "../../d3-selection/src/selection/datum.js", "../../d3-selection/src/selection/on.js", "../../d3-selection/src/selection/dispatch.js", "../../d3-selection/src/selection/iterator.js", "../../d3-selection/src/selection/index.js", "../../d3-selection/src/select.js", "../../d3-selection/src/local.js", "../../d3-selection/src/sourceEvent.js", "../../d3-selection/src/pointer.js", "../../d3-drag/src/noevent.js", "../../d3-drag/src/nodrag.js", "../../d3-drag/src/event.js", "../../d3-timer/src/timer.js", "../../d3-timer/src/timeout.js", "../../d3-transition/src/transition/schedule.js", "../../d3-transition/src/interrupt.js", "../../d3-transition/src/selection/interrupt.js", "../../d3-transition/src/transition/tween.js", "../../d3-transition/src/transition/interpolate.js", "../../d3-transition/src/transition/attr.js", "../../d3-transition/src/transition/attrTween.js", "../../d3-transition/src/transition/delay.js", "../../d3-transition/src/transition/duration.js", "../../d3-transition/src/transition/ease.js", "../../d3-transition/src/transition/easeVarying.js", "../../d3-transition/src/transition/filter.js", "../../d3-transition/src/transition/merge.js", "../../d3-transition/src/transition/on.js", "../../d3-transition/src/transition/remove.js", "../../d3-transition/src/transition/select.js", "../../d3-transition/src/transition/selectAll.js", "../../d3-transition/src/transition/selection.js", "../../d3-transition/src/transition/style.js", "../../d3-transition/src/transition/styleTween.js", "../../d3-transition/src/transition/text.js", "../../d3-transition/src/transition/textTween.js", "../../d3-transition/src/transition/transition.js", "../../d3-transition/src/transition/end.js", "../../d3-transition/src/transition/index.js", "../../d3-ease/src/cubic.js", "../../d3-ease/src/poly.js", "../../d3-ease/src/sin.js", "../../d3-ease/src/math.js", "../../d3-ease/src/bounce.js", "../../d3-ease/src/back.js", "../../d3-ease/src/elastic.js", "../../d3-transition/src/selection/transition.js", "../../d3-transition/src/selection/index.js", "../../d3-zoom/src/constant.js", "../../d3-zoom/src/event.js", "../../d3-zoom/src/transform.js", "../../d3-zoom/src/noevent.js", "../../d3-zoom/src/zoom.js"], "sourcesContent": ["import React, { createContext, useMemo, useCallback, useContext, forwardRef, useState, useEffect, memo, useRef, Fragment } from 'react';\nimport PropTypes from 'prop-types';\nimport * as d3Geo from 'd3-geo';\nimport { geoGraticule } from 'd3-geo';\nimport { feature, mesh } from 'topojson-client';\nimport { zoom, zoomIdentity } from 'd3-zoom';\nimport { select } from 'd3-selection';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar _excluded$a = [\"width\", \"height\", \"projection\", \"projectionConfig\"];\n\nvar geoPath = d3Geo.geoPath,\n    projections = _objectWithoutProperties(d3Geo, [\"geoPath\"]);\n\nvar MapContext = createContext();\n\nvar makeProjection = function makeProjection(_ref) {\n  var _ref$projectionConfig = _ref.projectionConfig,\n      projectionConfig = _ref$projectionConfig === void 0 ? {} : _ref$projectionConfig,\n      _ref$projection = _ref.projection,\n      projection = _ref$projection === void 0 ? \"geoEqualEarth\" : _ref$projection,\n      _ref$width = _ref.width,\n      width = _ref$width === void 0 ? 800 : _ref$width,\n      _ref$height = _ref.height,\n      height = _ref$height === void 0 ? 600 : _ref$height;\n  var isFunc = typeof projection === \"function\";\n  if (isFunc) return projection;\n  var proj = projections[projection]().translate([width / 2, height / 2]);\n  var supported = [proj.center ? \"center\" : null, proj.rotate ? \"rotate\" : null, proj.scale ? \"scale\" : null, proj.parallels ? \"parallels\" : null];\n  supported.forEach(function (d) {\n    if (!d) return;\n    proj = proj[d](projectionConfig[d] || proj[d]());\n  });\n  return proj;\n};\n\nvar MapProvider = function MapProvider(_ref2) {\n  var width = _ref2.width,\n      height = _ref2.height,\n      projection = _ref2.projection,\n      projectionConfig = _ref2.projectionConfig,\n      restProps = _objectWithoutProperties(_ref2, _excluded$a);\n\n  var _ref3 = projectionConfig.center || [],\n      _ref4 = _slicedToArray(_ref3, 2),\n      cx = _ref4[0],\n      cy = _ref4[1];\n\n  var _ref5 = projectionConfig.rotate || [],\n      _ref6 = _slicedToArray(_ref5, 3),\n      rx = _ref6[0],\n      ry = _ref6[1],\n      rz = _ref6[2];\n\n  var _ref7 = projectionConfig.parallels || [],\n      _ref8 = _slicedToArray(_ref7, 2),\n      p1 = _ref8[0],\n      p2 = _ref8[1];\n\n  var s = projectionConfig.scale || null;\n  var projMemo = useMemo(function () {\n    return makeProjection({\n      projectionConfig: {\n        center: cx || cx === 0 || cy || cy === 0 ? [cx, cy] : null,\n        rotate: rx || rx === 0 || ry || ry === 0 ? [rx, ry, rz] : null,\n        parallels: p1 || p1 === 0 || p2 || p2 === 0 ? [p1, p2] : null,\n        scale: s\n      },\n      projection: projection,\n      width: width,\n      height: height\n    });\n  }, [width, height, projection, cx, cy, rx, ry, rz, p1, p2, s]);\n  var proj = useCallback(projMemo, [projMemo]);\n  var value = useMemo(function () {\n    return {\n      width: width,\n      height: height,\n      projection: proj,\n      path: geoPath().projection(proj)\n    };\n  }, [width, height, proj]);\n  return /*#__PURE__*/React.createElement(MapContext.Provider, _extends({\n    value: value\n  }, restProps));\n};\n\nMapProvider.propTypes = {\n  width: PropTypes.number,\n  height: PropTypes.number,\n  projection: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),\n  projectionConfig: PropTypes.object\n};\n\nvar useMapContext = function useMapContext() {\n  return useContext(MapContext);\n};\n\nvar _excluded$9 = [\"width\", \"height\", \"projection\", \"projectionConfig\", \"className\"];\nvar ComposableMap = forwardRef(function (_ref, ref) {\n  var _ref$width = _ref.width,\n      width = _ref$width === void 0 ? 800 : _ref$width,\n      _ref$height = _ref.height,\n      height = _ref$height === void 0 ? 600 : _ref$height,\n      _ref$projection = _ref.projection,\n      projection = _ref$projection === void 0 ? \"geoEqualEarth\" : _ref$projection,\n      _ref$projectionConfig = _ref.projectionConfig,\n      projectionConfig = _ref$projectionConfig === void 0 ? {} : _ref$projectionConfig,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded$9);\n\n  return /*#__PURE__*/React.createElement(MapProvider, {\n    width: width,\n    height: height,\n    projection: projection,\n    projectionConfig: projectionConfig\n  }, /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    viewBox: \"0 0 \".concat(width, \" \").concat(height),\n    className: \"rsm-svg \".concat(className)\n  }, restProps)));\n});\nComposableMap.displayName = \"ComposableMap\";\nComposableMap.propTypes = {\n  width: PropTypes.number,\n  height: PropTypes.number,\n  projection: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),\n  projectionConfig: PropTypes.object,\n  className: PropTypes.string\n};\n\nfunction getCoords(w, h, t) {\n  var xOffset = (w * t.k - w) / 2;\n  var yOffset = (h * t.k - h) / 2;\n  return [w / 2 - (xOffset + t.x) / t.k, h / 2 - (yOffset + t.y) / t.k];\n}\nfunction fetchGeographies(url) {\n  return fetch(url).then(function (res) {\n    if (!res.ok) {\n      throw Error(res.statusText);\n    }\n\n    return res.json();\n  })[\"catch\"](function (error) {\n    console.log(\"There was a problem when fetching the data: \", error);\n  });\n}\nfunction getFeatures(geographies, parseGeographies) {\n  var isTopojson = geographies.type === \"Topology\";\n\n  if (!isTopojson) {\n    return parseGeographies ? parseGeographies(geographies.features || geographies) : geographies.features || geographies;\n  }\n\n  var feats = feature(geographies, geographies.objects[Object.keys(geographies.objects)[0]]).features;\n  return parseGeographies ? parseGeographies(feats) : feats;\n}\nfunction getMesh(geographies) {\n  var isTopojson = geographies.type === \"Topology\";\n  if (!isTopojson) return null;\n  var outline = mesh(geographies, geographies.objects[Object.keys(geographies.objects)[0]], function (a, b) {\n    return a === b;\n  });\n  var borders = mesh(geographies, geographies.objects[Object.keys(geographies.objects)[0]], function (a, b) {\n    return a !== b;\n  });\n  return {\n    outline: outline,\n    borders: borders\n  };\n}\nfunction prepareMesh(outline, borders, path) {\n  return outline && borders ? {\n    outline: _objectSpread2(_objectSpread2({}, outline), {}, {\n      rsmKey: \"outline\",\n      svgPath: path(outline)\n    }),\n    borders: _objectSpread2(_objectSpread2({}, borders), {}, {\n      rsmKey: \"borders\",\n      svgPath: path(borders)\n    })\n  } : {};\n}\nfunction prepareFeatures(geographies, path) {\n  return geographies ? geographies.map(function (d, i) {\n    return _objectSpread2(_objectSpread2({}, d), {}, {\n      rsmKey: \"geo-\".concat(i),\n      svgPath: path(d)\n    });\n  }) : [];\n}\nfunction createConnectorPath() {\n  var dx = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 30;\n  var dy = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 30;\n  var curve = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.5;\n  var curvature = Array.isArray(curve) ? curve : [curve, curve];\n  var curveX = dx / 2 * curvature[0];\n  var curveY = dy / 2 * curvature[1];\n  return \"M\".concat(0, \",\", 0, \" Q\", -dx / 2 - curveX, \",\").concat(-dy / 2 + curveY, \" \").concat(-dx, \",\").concat(-dy);\n}\nfunction isString(geo) {\n  return typeof geo === \"string\";\n}\n\nfunction useGeographies(_ref) {\n  var geography = _ref.geography,\n      parseGeographies = _ref.parseGeographies;\n\n  var _useContext = useContext(MapContext),\n      path = _useContext.path;\n\n  var _useState = useState({}),\n      _useState2 = _slicedToArray(_useState, 2),\n      output = _useState2[0],\n      setOutput = _useState2[1];\n\n  useEffect(function () {\n    if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) === \"undefined\") return;\n    if (!geography) return;\n\n    if (isString(geography)) {\n      fetchGeographies(geography).then(function (geos) {\n        if (geos) {\n          setOutput({\n            geographies: getFeatures(geos, parseGeographies),\n            mesh: getMesh(geos)\n          });\n        }\n      });\n    } else {\n      setOutput({\n        geographies: getFeatures(geography, parseGeographies),\n        mesh: getMesh(geography)\n      });\n    }\n  }, [geography, parseGeographies]);\n\n  var _useMemo = useMemo(function () {\n    var mesh = output.mesh || {};\n    var preparedMesh = prepareMesh(mesh.outline, mesh.borders, path);\n    return {\n      geographies: prepareFeatures(output.geographies, path),\n      outline: preparedMesh.outline,\n      borders: preparedMesh.borders\n    };\n  }, [output, path]),\n      geographies = _useMemo.geographies,\n      outline = _useMemo.outline,\n      borders = _useMemo.borders;\n\n  return {\n    geographies: geographies,\n    outline: outline,\n    borders: borders\n  };\n}\n\nvar _excluded$8 = [\"geography\", \"children\", \"parseGeographies\", \"className\"];\nvar Geographies = forwardRef(function (_ref, ref) {\n  var geography = _ref.geography,\n      children = _ref.children,\n      parseGeographies = _ref.parseGeographies,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded$8);\n\n  var _useContext = useContext(MapContext),\n      path = _useContext.path,\n      projection = _useContext.projection;\n\n  var _useGeographies = useGeographies({\n    geography: geography,\n    parseGeographies: parseGeographies\n  }),\n      geographies = _useGeographies.geographies,\n      outline = _useGeographies.outline,\n      borders = _useGeographies.borders;\n\n  return /*#__PURE__*/React.createElement(\"g\", _extends({\n    ref: ref,\n    className: \"rsm-geographies \".concat(className)\n  }, restProps), geographies && geographies.length > 0 && children({\n    geographies: geographies,\n    outline: outline,\n    borders: borders,\n    path: path,\n    projection: projection\n  }));\n});\nGeographies.displayName = \"Geographies\";\nGeographies.propTypes = {\n  geography: PropTypes.oneOfType([PropTypes.string, PropTypes.object, PropTypes.array]),\n  children: PropTypes.func,\n  parseGeographies: PropTypes.func,\n  className: PropTypes.string\n};\n\nvar _excluded$7 = [\"geography\", \"onMouseEnter\", \"onMouseLeave\", \"onMouseDown\", \"onMouseUp\", \"onFocus\", \"onBlur\", \"style\", \"className\"];\nvar Geography = forwardRef(function (_ref, ref) {\n  var geography = _ref.geography,\n      onMouseEnter = _ref.onMouseEnter,\n      onMouseLeave = _ref.onMouseLeave,\n      onMouseDown = _ref.onMouseDown,\n      onMouseUp = _ref.onMouseUp,\n      onFocus = _ref.onFocus,\n      onBlur = _ref.onBlur,\n      _ref$style = _ref.style,\n      style = _ref$style === void 0 ? {} : _ref$style,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded$7);\n\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      isPressed = _useState2[0],\n      setPressed = _useState2[1];\n\n  var _useState3 = useState(false),\n      _useState4 = _slicedToArray(_useState3, 2),\n      isFocused = _useState4[0],\n      setFocus = _useState4[1];\n\n  function handleMouseEnter(evt) {\n    setFocus(true);\n    if (onMouseEnter) onMouseEnter(evt);\n  }\n\n  function handleMouseLeave(evt) {\n    setFocus(false);\n    if (isPressed) setPressed(false);\n    if (onMouseLeave) onMouseLeave(evt);\n  }\n\n  function handleFocus(evt) {\n    setFocus(true);\n    if (onFocus) onFocus(evt);\n  }\n\n  function handleBlur(evt) {\n    setFocus(false);\n    if (isPressed) setPressed(false);\n    if (onBlur) onBlur(evt);\n  }\n\n  function handleMouseDown(evt) {\n    setPressed(true);\n    if (onMouseDown) onMouseDown(evt);\n  }\n\n  function handleMouseUp(evt) {\n    setPressed(false);\n    if (onMouseUp) onMouseUp(evt);\n  }\n\n  return /*#__PURE__*/React.createElement(\"path\", _extends({\n    ref: ref,\n    tabIndex: \"0\",\n    className: \"rsm-geography \".concat(className),\n    d: geography.svgPath,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    onFocus: handleFocus,\n    onBlur: handleBlur,\n    onMouseDown: handleMouseDown,\n    onMouseUp: handleMouseUp,\n    style: style[isPressed || isFocused ? isPressed ? \"pressed\" : \"hover\" : \"default\"]\n  }, restProps));\n});\nGeography.displayName = \"Geography\";\nGeography.propTypes = {\n  geography: PropTypes.object,\n  onMouseEnter: PropTypes.func,\n  onMouseLeave: PropTypes.func,\n  onMouseDown: PropTypes.func,\n  onMouseUp: PropTypes.func,\n  onFocus: PropTypes.func,\n  onBlur: PropTypes.func,\n  style: PropTypes.object,\n  className: PropTypes.string\n};\nvar Geography$1 = memo(Geography);\n\nvar _excluded$6 = [\"fill\", \"stroke\", \"step\", \"className\"];\nvar Graticule = forwardRef(function (_ref, ref) {\n  var _ref$fill = _ref.fill,\n      fill = _ref$fill === void 0 ? \"transparent\" : _ref$fill,\n      _ref$stroke = _ref.stroke,\n      stroke = _ref$stroke === void 0 ? \"currentcolor\" : _ref$stroke,\n      _ref$step = _ref.step,\n      step = _ref$step === void 0 ? [10, 10] : _ref$step,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded$6);\n\n  var _useContext = useContext(MapContext),\n      path = _useContext.path;\n\n  return /*#__PURE__*/React.createElement(\"path\", _extends({\n    ref: ref,\n    d: path(geoGraticule().step(step)()),\n    fill: fill,\n    stroke: stroke,\n    className: \"rsm-graticule \".concat(className)\n  }, restProps));\n});\nGraticule.displayName = \"Graticule\";\nGraticule.propTypes = {\n  fill: PropTypes.string,\n  stroke: PropTypes.string,\n  step: PropTypes.array,\n  className: PropTypes.string\n};\nvar Graticule$1 = memo(Graticule);\n\nvar _excluded$5 = [\"value\"];\nvar ZoomPanContext = createContext();\nvar defaultValue = {\n  x: 0,\n  y: 0,\n  k: 1,\n  transformString: \"translate(0 0) scale(1)\"\n};\n\nvar ZoomPanProvider = function ZoomPanProvider(_ref) {\n  var _ref$value = _ref.value,\n      value = _ref$value === void 0 ? defaultValue : _ref$value,\n      restProps = _objectWithoutProperties(_ref, _excluded$5);\n\n  return /*#__PURE__*/React.createElement(ZoomPanContext.Provider, _extends({\n    value: value\n  }, restProps));\n};\n\nZoomPanProvider.propTypes = {\n  x: PropTypes.number,\n  y: PropTypes.number,\n  k: PropTypes.number,\n  transformString: PropTypes.string\n};\n\nvar useZoomPanContext = function useZoomPanContext() {\n  return useContext(ZoomPanContext);\n};\n\nfunction useZoomPan(_ref) {\n  var center = _ref.center,\n      filterZoomEvent = _ref.filterZoomEvent,\n      onMoveStart = _ref.onMoveStart,\n      onMoveEnd = _ref.onMoveEnd,\n      onMove = _ref.onMove,\n      _ref$translateExtent = _ref.translateExtent,\n      translateExtent = _ref$translateExtent === void 0 ? [[-Infinity, -Infinity], [Infinity, Infinity]] : _ref$translateExtent,\n      _ref$scaleExtent = _ref.scaleExtent,\n      scaleExtent = _ref$scaleExtent === void 0 ? [1, 8] : _ref$scaleExtent,\n      _ref$zoom = _ref.zoom,\n      zoom$1 = _ref$zoom === void 0 ? 1 : _ref$zoom;\n\n  var _useContext = useContext(MapContext),\n      width = _useContext.width,\n      height = _useContext.height,\n      projection = _useContext.projection;\n\n  var _center = _slicedToArray(center, 2),\n      lon = _center[0],\n      lat = _center[1];\n\n  var _useState = useState({\n    x: 0,\n    y: 0,\n    k: 1\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      position = _useState2[0],\n      setPosition = _useState2[1];\n\n  var lastPosition = useRef({\n    x: 0,\n    y: 0,\n    k: 1\n  });\n  var mapRef = useRef();\n  var zoomRef = useRef();\n  var bypassEvents = useRef(false);\n\n  var _translateExtent = _slicedToArray(translateExtent, 2),\n      a = _translateExtent[0],\n      b = _translateExtent[1];\n\n  var _a = _slicedToArray(a, 2),\n      a1 = _a[0],\n      a2 = _a[1];\n\n  var _b = _slicedToArray(b, 2),\n      b1 = _b[0],\n      b2 = _b[1];\n\n  var _scaleExtent = _slicedToArray(scaleExtent, 2),\n      minZoom = _scaleExtent[0],\n      maxZoom = _scaleExtent[1];\n\n  useEffect(function () {\n    var svg = select(mapRef.current);\n\n    function handleZoomStart(d3Event) {\n      if (!onMoveStart || bypassEvents.current) return;\n      onMoveStart({\n        coordinates: projection.invert(getCoords(width, height, d3Event.transform)),\n        zoom: d3Event.transform.k\n      }, d3Event);\n    }\n\n    function handleZoom(d3Event) {\n      if (bypassEvents.current) return;\n      var transform = d3Event.transform,\n          sourceEvent = d3Event.sourceEvent;\n      setPosition({\n        x: transform.x,\n        y: transform.y,\n        k: transform.k,\n        dragging: sourceEvent\n      });\n      if (!onMove) return;\n      onMove({\n        x: transform.x,\n        y: transform.y,\n        zoom: transform.k,\n        dragging: sourceEvent\n      }, d3Event);\n    }\n\n    function handleZoomEnd(d3Event) {\n      if (bypassEvents.current) {\n        bypassEvents.current = false;\n        return;\n      }\n\n      var _projection$invert = projection.invert(getCoords(width, height, d3Event.transform)),\n          _projection$invert2 = _slicedToArray(_projection$invert, 2),\n          x = _projection$invert2[0],\n          y = _projection$invert2[1];\n\n      lastPosition.current = {\n        x: x,\n        y: y,\n        k: d3Event.transform.k\n      };\n      if (!onMoveEnd) return;\n      onMoveEnd({\n        coordinates: [x, y],\n        zoom: d3Event.transform.k\n      }, d3Event);\n    }\n\n    function filterFunc(d3Event) {\n      if (filterZoomEvent) {\n        return filterZoomEvent(d3Event);\n      }\n\n      return d3Event ? !d3Event.ctrlKey && !d3Event.button : false;\n    }\n\n    var zoom$1 = zoom().filter(filterFunc).scaleExtent([minZoom, maxZoom]).translateExtent([[a1, a2], [b1, b2]]).on(\"start\", handleZoomStart).on(\"zoom\", handleZoom).on(\"end\", handleZoomEnd);\n    zoomRef.current = zoom$1;\n    svg.call(zoom$1);\n  }, [width, height, a1, a2, b1, b2, minZoom, maxZoom, projection, onMoveStart, onMove, onMoveEnd, filterZoomEvent]);\n  useEffect(function () {\n    if (lon === lastPosition.current.x && lat === lastPosition.current.y && zoom$1 === lastPosition.current.k) return;\n    var coords = projection([lon, lat]);\n    var x = coords[0] * zoom$1;\n    var y = coords[1] * zoom$1;\n    var svg = select(mapRef.current);\n    bypassEvents.current = true;\n    svg.call(zoomRef.current.transform, zoomIdentity.translate(width / 2 - x, height / 2 - y).scale(zoom$1));\n    setPosition({\n      x: width / 2 - x,\n      y: height / 2 - y,\n      k: zoom$1\n    });\n    lastPosition.current = {\n      x: lon,\n      y: lat,\n      k: zoom$1\n    };\n  }, [lon, lat, zoom$1, width, height, projection]);\n  return {\n    mapRef: mapRef,\n    position: position,\n    transformString: \"translate(\".concat(position.x, \" \").concat(position.y, \") scale(\").concat(position.k, \")\")\n  };\n}\n\nvar _excluded$4 = [\"center\", \"zoom\", \"minZoom\", \"maxZoom\", \"translateExtent\", \"filterZoomEvent\", \"onMoveStart\", \"onMove\", \"onMoveEnd\", \"className\"];\nvar ZoomableGroup = forwardRef(function (_ref, ref) {\n  var _ref$center = _ref.center,\n      center = _ref$center === void 0 ? [0, 0] : _ref$center,\n      _ref$zoom = _ref.zoom,\n      zoom = _ref$zoom === void 0 ? 1 : _ref$zoom,\n      _ref$minZoom = _ref.minZoom,\n      minZoom = _ref$minZoom === void 0 ? 1 : _ref$minZoom,\n      _ref$maxZoom = _ref.maxZoom,\n      maxZoom = _ref$maxZoom === void 0 ? 8 : _ref$maxZoom,\n      translateExtent = _ref.translateExtent,\n      filterZoomEvent = _ref.filterZoomEvent,\n      onMoveStart = _ref.onMoveStart,\n      onMove = _ref.onMove,\n      onMoveEnd = _ref.onMoveEnd,\n      className = _ref.className,\n      restProps = _objectWithoutProperties(_ref, _excluded$4);\n\n  var _useContext = useContext(MapContext),\n      width = _useContext.width,\n      height = _useContext.height;\n\n  var _useZoomPan = useZoomPan({\n    center: center,\n    filterZoomEvent: filterZoomEvent,\n    onMoveStart: onMoveStart,\n    onMove: onMove,\n    onMoveEnd: onMoveEnd,\n    scaleExtent: [minZoom, maxZoom],\n    translateExtent: translateExtent,\n    zoom: zoom\n  }),\n      mapRef = _useZoomPan.mapRef,\n      transformString = _useZoomPan.transformString,\n      position = _useZoomPan.position;\n\n  return /*#__PURE__*/React.createElement(ZoomPanProvider, {\n    value: {\n      x: position.x,\n      y: position.y,\n      k: position.k,\n      transformString: transformString\n    }\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    ref: mapRef\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    width: width,\n    height: height,\n    fill: \"transparent\"\n  }), /*#__PURE__*/React.createElement(\"g\", _extends({\n    ref: ref,\n    transform: transformString,\n    className: \"rsm-zoomable-group \".concat(className)\n  }, restProps))));\n});\nZoomableGroup.displayName = \"ZoomableGroup\";\nZoomableGroup.propTypes = {\n  center: PropTypes.array,\n  zoom: PropTypes.number,\n  minZoom: PropTypes.number,\n  maxZoom: PropTypes.number,\n  translateExtent: PropTypes.arrayOf(PropTypes.array),\n  onMoveStart: PropTypes.func,\n  onMove: PropTypes.func,\n  onMoveEnd: PropTypes.func,\n  className: PropTypes.string\n};\n\nvar _excluded$3 = [\"id\", \"fill\", \"stroke\", \"strokeWidth\", \"className\"];\nvar Sphere = forwardRef(function (_ref, ref) {\n  var _ref$id = _ref.id,\n      id = _ref$id === void 0 ? \"rsm-sphere\" : _ref$id,\n      _ref$fill = _ref.fill,\n      fill = _ref$fill === void 0 ? \"transparent\" : _ref$fill,\n      _ref$stroke = _ref.stroke,\n      stroke = _ref$stroke === void 0 ? \"currentcolor\" : _ref$stroke,\n      _ref$strokeWidth = _ref.strokeWidth,\n      strokeWidth = _ref$strokeWidth === void 0 ? 0.5 : _ref$strokeWidth,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded$3);\n\n  var _useContext = useContext(MapContext),\n      path = _useContext.path;\n\n  var spherePath = useMemo(function () {\n    return path({\n      type: \"Sphere\"\n    });\n  }, [path]);\n  return /*#__PURE__*/React.createElement(Fragment, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: id\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: spherePath\n  }))), /*#__PURE__*/React.createElement(\"path\", _extends({\n    ref: ref,\n    d: spherePath,\n    fill: fill,\n    stroke: stroke,\n    strokeWidth: strokeWidth,\n    style: {\n      pointerEvents: \"none\"\n    },\n    className: \"rsm-sphere \".concat(className)\n  }, restProps)));\n});\nSphere.displayName = \"Sphere\";\nSphere.propTypes = {\n  id: PropTypes.string,\n  fill: PropTypes.string,\n  stroke: PropTypes.string,\n  strokeWidth: PropTypes.number,\n  className: PropTypes.string\n};\nvar Sphere$1 = memo(Sphere);\n\nvar _excluded$2 = [\"coordinates\", \"children\", \"onMouseEnter\", \"onMouseLeave\", \"onMouseDown\", \"onMouseUp\", \"onFocus\", \"onBlur\", \"style\", \"className\"];\nvar Marker = forwardRef(function (_ref, ref) {\n  var coordinates = _ref.coordinates,\n      children = _ref.children,\n      onMouseEnter = _ref.onMouseEnter,\n      onMouseLeave = _ref.onMouseLeave,\n      onMouseDown = _ref.onMouseDown,\n      onMouseUp = _ref.onMouseUp,\n      onFocus = _ref.onFocus,\n      onBlur = _ref.onBlur,\n      _ref$style = _ref.style,\n      style = _ref$style === void 0 ? {} : _ref$style,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded$2);\n\n  var _useContext = useContext(MapContext),\n      projection = _useContext.projection;\n\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      isPressed = _useState2[0],\n      setPressed = _useState2[1];\n\n  var _useState3 = useState(false),\n      _useState4 = _slicedToArray(_useState3, 2),\n      isFocused = _useState4[0],\n      setFocus = _useState4[1];\n\n  var _projection = projection(coordinates),\n      _projection2 = _slicedToArray(_projection, 2),\n      x = _projection2[0],\n      y = _projection2[1];\n\n  function handleMouseEnter(evt) {\n    setFocus(true);\n    if (onMouseEnter) onMouseEnter(evt);\n  }\n\n  function handleMouseLeave(evt) {\n    setFocus(false);\n    if (isPressed) setPressed(false);\n    if (onMouseLeave) onMouseLeave(evt);\n  }\n\n  function handleFocus(evt) {\n    setFocus(true);\n    if (onFocus) onFocus(evt);\n  }\n\n  function handleBlur(evt) {\n    setFocus(false);\n    if (isPressed) setPressed(false);\n    if (onBlur) onBlur(evt);\n  }\n\n  function handleMouseDown(evt) {\n    setPressed(true);\n    if (onMouseDown) onMouseDown(evt);\n  }\n\n  function handleMouseUp(evt) {\n    setPressed(false);\n    if (onMouseUp) onMouseUp(evt);\n  }\n\n  return /*#__PURE__*/React.createElement(\"g\", _extends({\n    ref: ref,\n    transform: \"translate(\".concat(x, \", \").concat(y, \")\"),\n    className: \"rsm-marker \".concat(className),\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    onFocus: handleFocus,\n    onBlur: handleBlur,\n    onMouseDown: handleMouseDown,\n    onMouseUp: handleMouseUp,\n    style: style[isPressed || isFocused ? isPressed ? \"pressed\" : \"hover\" : \"default\"]\n  }, restProps), children);\n});\nMarker.displayName = \"Marker\";\nMarker.propTypes = {\n  coordinates: PropTypes.array,\n  children: PropTypes.oneOfType([PropTypes.node, PropTypes.arrayOf(PropTypes.node)]),\n  onMouseEnter: PropTypes.func,\n  onMouseLeave: PropTypes.func,\n  onMouseDown: PropTypes.func,\n  onMouseUp: PropTypes.func,\n  onFocus: PropTypes.func,\n  onBlur: PropTypes.func,\n  style: PropTypes.object,\n  className: PropTypes.string\n};\n\nvar _excluded$1 = [\"from\", \"to\", \"coordinates\", \"stroke\", \"strokeWidth\", \"fill\", \"className\"];\nvar Line = forwardRef(function (_ref, ref) {\n  var _ref$from = _ref.from,\n      from = _ref$from === void 0 ? [0, 0] : _ref$from,\n      _ref$to = _ref.to,\n      to = _ref$to === void 0 ? [0, 0] : _ref$to,\n      coordinates = _ref.coordinates,\n      _ref$stroke = _ref.stroke,\n      stroke = _ref$stroke === void 0 ? \"currentcolor\" : _ref$stroke,\n      _ref$strokeWidth = _ref.strokeWidth,\n      strokeWidth = _ref$strokeWidth === void 0 ? 3 : _ref$strokeWidth,\n      _ref$fill = _ref.fill,\n      fill = _ref$fill === void 0 ? \"transparent\" : _ref$fill,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded$1);\n\n  var _useContext = useContext(MapContext),\n      path = _useContext.path;\n\n  var lineData = {\n    type: \"LineString\",\n    coordinates: coordinates || [from, to]\n  };\n  return /*#__PURE__*/React.createElement(\"path\", _extends({\n    ref: ref,\n    d: path(lineData),\n    className: \"rsm-line \".concat(className),\n    stroke: stroke,\n    strokeWidth: strokeWidth,\n    fill: fill\n  }, restProps));\n});\nLine.displayName = \"Line\";\nLine.propTypes = {\n  from: PropTypes.array,\n  to: PropTypes.array,\n  coordinates: PropTypes.array,\n  stroke: PropTypes.string,\n  strokeWidth: PropTypes.number,\n  fill: PropTypes.string,\n  className: PropTypes.string\n};\n\nvar _excluded = [\"subject\", \"children\", \"connectorProps\", \"dx\", \"dy\", \"curve\", \"className\"];\nvar Annotation = forwardRef(function (_ref, ref) {\n  var subject = _ref.subject,\n      children = _ref.children,\n      connectorProps = _ref.connectorProps,\n      _ref$dx = _ref.dx,\n      dx = _ref$dx === void 0 ? 30 : _ref$dx,\n      _ref$dy = _ref.dy,\n      dy = _ref$dy === void 0 ? 30 : _ref$dy,\n      _ref$curve = _ref.curve,\n      curve = _ref$curve === void 0 ? 0 : _ref$curve,\n      _ref$className = _ref.className,\n      className = _ref$className === void 0 ? \"\" : _ref$className,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n\n  var _useContext = useContext(MapContext),\n      projection = _useContext.projection;\n\n  var _projection = projection(subject),\n      _projection2 = _slicedToArray(_projection, 2),\n      x = _projection2[0],\n      y = _projection2[1];\n\n  var connectorPath = createConnectorPath(dx, dy, curve);\n  return /*#__PURE__*/React.createElement(\"g\", _extends({\n    ref: ref,\n    transform: \"translate(\".concat(x + dx, \", \").concat(y + dy, \")\"),\n    className: \"rsm-annotation \".concat(className)\n  }, restProps), /*#__PURE__*/React.createElement(\"path\", _extends({\n    d: connectorPath,\n    fill: \"transparent\",\n    stroke: \"#000\"\n  }, connectorProps)), children);\n});\nAnnotation.displayName = \"Annotation\";\nAnnotation.propTypes = {\n  subject: PropTypes.array,\n  children: PropTypes.oneOfType([PropTypes.node, PropTypes.arrayOf(PropTypes.node)]),\n  dx: PropTypes.number,\n  dy: PropTypes.number,\n  curve: PropTypes.number,\n  connectorProps: PropTypes.object,\n  className: PropTypes.string\n};\n\nexport { Annotation, ComposableMap, Geographies, Geography$1 as Geography, Graticule$1 as Graticule, Line, MapContext, MapProvider, Marker, Sphere$1 as Sphere, ZoomPanContext, ZoomPanProvider, ZoomableGroup, useGeographies, useMapContext, useZoomPan, useZoomPanContext };\n", "export {default as geoArea} from \"./area.js\";\nexport {default as geoBounds} from \"./bounds.js\";\nexport {default as geoCentroid} from \"./centroid.js\";\nexport {default as geoCircle} from \"./circle.js\";\nexport {default as geoClipAntimeridian} from \"./clip/antimeridian.js\";\nexport {default as geoClipCircle} from \"./clip/circle.js\";\nexport {default as geoClipExtent} from \"./clip/extent.js\"; // DEPRECATED! Use d3.geoIdentity().clipExtent(…).\nexport {default as geoClipRectangle} from \"./clip/rectangle.js\";\nexport {default as geoContains} from \"./contains.js\";\nexport {default as geoDistance} from \"./distance.js\";\nexport {default as geoGraticule, graticule10 as geoGraticule10} from \"./graticule.js\";\nexport {default as geoInterpolate} from \"./interpolate.js\";\nexport {default as geoLength} from \"./length.js\";\nexport {default as geoPath} from \"./path/index.js\";\nexport {default as geoAlbers} from \"./projection/albers.js\";\nexport {default as geoAlbersUsa} from \"./projection/albersUsa.js\";\nexport {default as geoAzimuthalEqualArea, azimuthalEqualAreaRaw as geoAzimuthalEqualAreaRaw} from \"./projection/azimuthalEqualArea.js\";\nexport {default as geoAzimuthalEquidistant, azimuthalEquidistantRaw as geoAzimuthalEquidistantRaw} from \"./projection/azimuthalEquidistant.js\";\nexport {default as geoConicConformal, conicConformalRaw as geoConicConformalRaw} from \"./projection/conicConformal.js\";\nexport {default as geoConicEqualArea, conicEqualAreaRaw as geoConicEqualAreaRaw} from \"./projection/conicEqualArea.js\";\nexport {default as geoConicEquidistant, conicEquidistantRaw as geoConicEquidistantRaw} from \"./projection/conicEquidistant.js\";\nexport {default as geoEqualEarth, equalEarthRaw as geoEqualEarthRaw} from \"./projection/equalEarth.js\";\nexport {default as geoEquirectangular, equirectangularRaw as geoEquirectangularRaw} from \"./projection/equirectangular.js\";\nexport {default as geoGnomonic, gnomonicRaw as geoGnomonicRaw} from \"./projection/gnomonic.js\";\nexport {default as geoIdentity} from \"./projection/identity.js\";\nexport {default as geoProjection, projectionMutator as geoProjectionMutator} from \"./projection/index.js\";\nexport {default as geoMercator, mercatorRaw as geoMercatorRaw} from \"./projection/mercator.js\";\nexport {default as geoNaturalEarth1, naturalEarth1Raw as geoNaturalEarth1Raw} from \"./projection/naturalEarth1.js\";\nexport {default as geoOrthographic, orthographicRaw as geoOrthographicRaw} from \"./projection/orthographic.js\";\nexport {default as geoStereographic, stereographicRaw as geoStereographicRaw} from \"./projection/stereographic.js\";\nexport {default as geoTransverseMercator, transverseMercatorRaw as geoTransverseMercatorRaw} from \"./projection/transverseMercator.js\";\nexport {default as geoRotation} from \"./rotation.js\";\nexport {default as geoStream} from \"./stream.js\";\nexport {default as geoTransform} from \"./transform.js\";\n", "export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\n\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\n\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "export default function noop() {}\n", "function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\nexport default function(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n", "import {Adder} from \"d3-array\";\nimport {atan2, cos, quarterPi, radians, sin, tau} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nexport var areaRingSum = new Adder();\n\n// hello?\n\nvar areaSum = new Adder(),\n    lambda00,\n    phi00,\n    lambda0,\n    cosPhi0,\n    sinPhi0;\n\nexport var areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaRingSum = new Adder();\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    var areaRing = +areaRingSum;\n    areaSum.add(areaRing < 0 ? tau + areaRing : areaRing);\n    this.lineStart = this.lineEnd = this.point = noop;\n  },\n  sphere: function() {\n    areaSum.add(tau);\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaRingEnd() {\n  areaPoint(lambda00, phi00);\n}\n\nfunction areaPointFirst(lambda, phi) {\n  areaStream.point = areaPoint;\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, cosPhi0 = cos(phi = phi / 2 + quarterPi), sinPhi0 = sin(phi);\n}\n\nfunction areaPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  phi = phi / 2 + quarterPi; // half the angular distance from south pole\n\n  // Spherical excess E for a spherical triangle with vertices: south pole,\n  // previous point, current point.  Uses a formula derived from Cagnoli’s\n  // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n  var dLambda = lambda - lambda0,\n      sdLambda = dLambda >= 0 ? 1 : -1,\n      adLambda = sdLambda * dLambda,\n      cosPhi = cos(phi),\n      sinPhi = sin(phi),\n      k = sinPhi0 * sinPhi,\n      u = cosPhi0 * cosPhi + k * cos(adLambda),\n      v = k * sdLambda * sin(adLambda);\n  areaRingSum.add(atan2(v, u));\n\n  // Advance the previous points.\n  lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n\nexport default function(object) {\n  areaSum = new Adder();\n  stream(object, areaStream);\n  return areaSum * 2;\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  var lambda = spherical[0], phi = spherical[1], cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}\n", "import {Adder} from \"d3-array\";\nimport {areaStream, areaRingSum} from \"./area.js\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport {abs, degrees, epsilon, radians} from \"./math.js\";\nimport stream from \"./stream.js\";\n\nvar lambda0, phi0, lambda1, phi1, // bounds\n    lambda2, // previous lambda-coordinate\n    lambda00, phi00, // first point\n    p0, // previous 3D point\n    deltaSum,\n    ranges,\n    range;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: boundsLineStart,\n  lineEnd: boundsLineEnd,\n  polygonStart: function() {\n    boundsStream.point = boundsRingPoint;\n    boundsStream.lineStart = boundsRingStart;\n    boundsStream.lineEnd = boundsRingEnd;\n    deltaSum = new Adder();\n    areaStream.polygonStart();\n  },\n  polygonEnd: function() {\n    areaStream.polygonEnd();\n    boundsStream.point = boundsPoint;\n    boundsStream.lineStart = boundsLineStart;\n    boundsStream.lineEnd = boundsLineEnd;\n    if (areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    else if (deltaSum > epsilon) phi1 = 90;\n    else if (deltaSum < -epsilon) phi0 = -90;\n    range[0] = lambda0, range[1] = lambda1;\n  },\n  sphere: function() {\n    lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n  }\n};\n\nfunction boundsPoint(lambda, phi) {\n  ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n}\n\nfunction linePoint(lambda, phi) {\n  var p = cartesian([lambda * radians, phi * radians]);\n  if (p0) {\n    var normal = cartesianCross(p0, p),\n        equatorial = [normal[1], -normal[0], 0],\n        inflection = cartesianCross(equatorial, normal);\n    cartesianNormalizeInPlace(inflection);\n    inflection = spherical(inflection);\n    var delta = lambda - lambda2,\n        sign = delta > 0 ? 1 : -1,\n        lambdai = inflection[0] * degrees * sign,\n        phii,\n        antimeridian = abs(delta) > 180;\n    if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = inflection[1] * degrees;\n      if (phii > phi1) phi1 = phii;\n    } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = -inflection[1] * degrees;\n      if (phii < phi0) phi0 = phii;\n    } else {\n      if (phi < phi0) phi0 = phi;\n      if (phi > phi1) phi1 = phi;\n    }\n    if (antimeridian) {\n      if (lambda < lambda2) {\n        if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n      } else {\n        if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n      }\n    } else {\n      if (lambda1 >= lambda0) {\n        if (lambda < lambda0) lambda0 = lambda;\n        if (lambda > lambda1) lambda1 = lambda;\n      } else {\n        if (lambda > lambda2) {\n          if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n        } else {\n          if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n        }\n      }\n    }\n  } else {\n    ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  }\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n  p0 = p, lambda2 = lambda;\n}\n\nfunction boundsLineStart() {\n  boundsStream.point = linePoint;\n}\n\nfunction boundsLineEnd() {\n  range[0] = lambda0, range[1] = lambda1;\n  boundsStream.point = boundsPoint;\n  p0 = null;\n}\n\nfunction boundsRingPoint(lambda, phi) {\n  if (p0) {\n    var delta = lambda - lambda2;\n    deltaSum.add(abs(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n  } else {\n    lambda00 = lambda, phi00 = phi;\n  }\n  areaStream.point(lambda, phi);\n  linePoint(lambda, phi);\n}\n\nfunction boundsRingStart() {\n  areaStream.lineStart();\n}\n\nfunction boundsRingEnd() {\n  boundsRingPoint(lambda00, phi00);\n  areaStream.lineEnd();\n  if (abs(deltaSum) > epsilon) lambda0 = -(lambda1 = 180);\n  range[0] = lambda0, range[1] = lambda1;\n  p0 = null;\n}\n\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n  return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\n\nfunction rangeCompare(a, b) {\n  return a[0] - b[0];\n}\n\nfunction rangeContains(range, x) {\n  return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n\nexport default function(feature) {\n  var i, n, a, b, merged, deltaMax, delta;\n\n  phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n  ranges = [];\n  stream(feature, boundsStream);\n\n  // First, sort ranges by their minimum longitudes.\n  if (n = ranges.length) {\n    ranges.sort(rangeCompare);\n\n    // Then, merge any ranges that overlap.\n    for (i = 1, a = ranges[0], merged = [a]; i < n; ++i) {\n      b = ranges[i];\n      if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n        if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n        if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n      } else {\n        merged.push(a = b);\n      }\n    }\n\n    // Finally, find the largest gap between the merged ranges.\n    // The final bounding box will be the inverse of this gap.\n    for (deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i) {\n      b = merged[i];\n      if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n    }\n  }\n\n  ranges = range = null;\n\n  return lambda0 === Infinity || phi0 === Infinity\n      ? [[NaN, NaN], [NaN, NaN]]\n      : [[lambda0, phi0], [lambda1, phi1]];\n}\n", "import {Adder} from \"d3-array\";\nimport {asin, atan2, cos, degrees, epsilon, epsilon2, hypot, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar W0, W1,\n    X0, Y0, Z0,\n    X1, Y1, Z1,\n    X2, Y2, Z2,\n    lambda00, phi00, // first point\n    x0, y0, z0; // previous point\n\nvar centroidStream = {\n  sphere: noop,\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  }\n};\n\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  centroidPointCartesian(cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi));\n}\n\nfunction centroidPointCartesian(x, y, z) {\n  ++W0;\n  X0 += (x - X0) / W0;\n  Y0 += (y - Y0) / W0;\n  Z0 += (z - Z0) / W0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidLinePointFirst;\n}\n\nfunction centroidLinePointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidStream.point = centroidLinePoint;\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLinePoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      w = atan2(sqrt((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n  centroidStream.point = centroidRingPointFirst;\n}\n\nfunction centroidRingEnd() {\n  centroidRingPoint(lambda00, phi00);\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingPointFirst(lambda, phi) {\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  centroidStream.point = centroidRingPoint;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidRingPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      cx = y0 * z - z0 * y,\n      cy = z0 * x - x0 * z,\n      cz = x0 * y - y0 * x,\n      m = hypot(cx, cy, cz),\n      w = asin(m), // line weight = angle\n      v = m && -w / m; // area weight multiplier\n  X2.add(v * cx);\n  Y2.add(v * cy);\n  Z2.add(v * cz);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nexport default function(object) {\n  W0 = W1 =\n  X0 = Y0 = Z0 =\n  X1 = Y1 = Z1 = 0;\n  X2 = new Adder();\n  Y2 = new Adder();\n  Z2 = new Adder();\n  stream(object, centroidStream);\n\n  var x = +X2,\n      y = +Y2,\n      z = +Z2,\n      m = hypot(x, y, z);\n\n  // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n  if (m < epsilon2) {\n    x = X1, y = Y1, z = Z1;\n    // If the feature has zero length, fall back to arithmetic mean of point vectors.\n    if (W1 < epsilon) x = X0, y = Y0, z = Z0;\n    m = hypot(x, y, z);\n    // If the feature still has an undefined ccentroid, then return.\n    if (m < epsilon2) return [NaN, NaN];\n  }\n\n  return [atan2(y, x) * degrees, asin(z / m) * degrees];\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function(a, b) {\n\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n\n  if (a.invert && b.invert) compose.invert = function(x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n\n  return compose;\n}\n", "import compose from \"./compose.js\";\nimport {abs, asin, atan2, cos, degrees, pi, radians, sin, tau} from \"./math.js\";\n\nfunction rotationIdentity(lambda, phi) {\n  return [abs(lambda) > pi ? lambda + Math.round(-lambda / tau) * tau : lambda, phi];\n}\n\nrotationIdentity.invert = rotationIdentity;\n\nexport function rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n  return (deltaLambda %= tau) ? (deltaPhi || deltaGamma ? compose(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma))\n    : rotationLambda(deltaLambda))\n    : (deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma)\n    : rotationIdentity);\n}\n\nfunction forwardRotationLambda(deltaLambda) {\n  return function(lambda, phi) {\n    return lambda += deltaLambda, [lambda > pi ? lambda - tau : lambda < -pi ? lambda + tau : lambda, phi];\n  };\n}\n\nfunction rotationLambda(deltaLambda) {\n  var rotation = forwardRotationLambda(deltaLambda);\n  rotation.invert = forwardRotationLambda(-deltaLambda);\n  return rotation;\n}\n\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n  var cosDeltaPhi = cos(deltaPhi),\n      sinDeltaPhi = sin(deltaPhi),\n      cosDeltaGamma = cos(deltaGamma),\n      sinDeltaGamma = sin(deltaGamma);\n\n  function rotation(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaPhi + x * sinDeltaPhi;\n    return [\n      atan2(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n      asin(k * cosDeltaGamma + y * sinDeltaGamma)\n    ];\n  }\n\n  rotation.invert = function(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaGamma - y * sinDeltaGamma;\n    return [\n      atan2(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n      asin(k * cosDeltaPhi - x * sinDeltaPhi)\n    ];\n  };\n\n  return rotation;\n}\n\nexport default function(rotate) {\n  rotate = rotateRadians(rotate[0] * radians, rotate[1] * radians, rotate.length > 2 ? rotate[2] * radians : 0);\n\n  function forward(coordinates) {\n    coordinates = rotate(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  }\n\n  forward.invert = function(coordinates) {\n    coordinates = rotate.invert(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  };\n\n  return forward;\n}\n", "import {cartesian, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport constant from \"./constant.js\";\nimport {acos, cos, degrees, epsilon, radians, sin, tau} from \"./math.js\";\nimport {rotateRadians} from \"./rotation.js\";\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nexport function circleStream(stream, radius, delta, direction, t0, t1) {\n  if (!delta) return;\n  var cosRadius = cos(radius),\n      sinRadius = sin(radius),\n      step = direction * delta;\n  if (t0 == null) {\n    t0 = radius + direction * tau;\n    t1 = radius - step / 2;\n  } else {\n    t0 = circleRadius(cosRadius, t0);\n    t1 = circleRadius(cosRadius, t1);\n    if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * tau;\n  }\n  for (var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step) {\n    point = spherical([cosRadius, -sinRadius * cos(t), -sinRadius * sin(t)]);\n    stream.point(point[0], point[1]);\n  }\n}\n\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n  point = cartesian(point), point[0] -= cosRadius;\n  cartesianNormalizeInPlace(point);\n  var radius = acos(-point[1]);\n  return ((-point[2] < 0 ? -radius : radius) + tau - epsilon) % tau;\n}\n\nexport default function() {\n  var center = constant([0, 0]),\n      radius = constant(90),\n      precision = constant(6),\n      ring,\n      rotate,\n      stream = {point: point};\n\n  function point(x, y) {\n    ring.push(x = rotate(x, y));\n    x[0] *= degrees, x[1] *= degrees;\n  }\n\n  function circle() {\n    var c = center.apply(this, arguments),\n        r = radius.apply(this, arguments) * radians,\n        p = precision.apply(this, arguments) * radians;\n    ring = [];\n    rotate = rotateRadians(-c[0] * radians, -c[1] * radians, 0).invert;\n    circleStream(stream, r, p, 1);\n    c = {type: \"Polygon\", coordinates: [ring]};\n    ring = rotate = null;\n    return c;\n  }\n\n  circle.center = function(_) {\n    return arguments.length ? (center = typeof _ === \"function\" ? _ : constant([+_[0], +_[1]]), circle) : center;\n  };\n\n  circle.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), circle) : radius;\n  };\n\n  circle.precision = function(_) {\n    return arguments.length ? (precision = typeof _ === \"function\" ? _ : constant(+_), circle) : precision;\n  };\n\n  return circle;\n}\n", "import noop from \"../noop.js\";\n\nexport default function() {\n  var lines = [],\n      line;\n  return {\n    point: function(x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function() {\n      lines.push(line = []);\n    },\n    lineEnd: noop,\n    rejoin: function() {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function() {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}\n", "import {abs, epsilon} from \"./math.js\";\n\nexport default function(a, b) {\n  return abs(a[0] - b[0]) < epsilon && abs(a[1] - b[1]) < epsilon;\n}\n", "import pointEqual from \"../pointEqual.js\";\nimport {epsilon} from \"../math.js\";\n\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\nexport default function(segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n      clip = [],\n      i,\n      n;\n\n  segments.forEach(function(segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n, p0 = segment[0], p1 = segment[n], x;\n\n    if (pointEqual(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * epsilon;\n    }\n\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n\n  if (!subject.length) return;\n\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n\n  var start = subject[0],\n      points,\n      point;\n\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n        isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\n\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n      i = 0,\n      a = array[0],\n      b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}\n", "import {<PERSON>der} from \"d3-array\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace} from \"./cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, epsilon2, halfPi, pi, quarterPi, sign, sin, tau} from \"./math.js\";\n\nfunction longitude(point) {\n  if (abs(point[0]) <= pi)\n    return point[0];\n  else\n    return sign(point[0]) * ((abs(point[0]) + pi) % tau - pi);\n}\n\nexport default function(polygon, point) {\n  var lambda = longitude(point),\n      phi = point[1],\n      sinPhi = sin(phi),\n      normal = [sin(lambda), -cos(lambda), 0],\n      angle = 0,\n      winding = 0;\n\n  var sum = new Adder();\n\n  if (sinPhi === 1) phi = halfPi + epsilon;\n  else if (sinPhi === -1) phi = -halfPi - epsilon;\n\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n        m,\n        point0 = ring[m - 1],\n        lambda0 = longitude(point0),\n        phi0 = point0[1] / 2 + quarterPi,\n        sinPhi0 = sin(phi0),\n        cosPhi0 = cos(phi0);\n\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n          lambda1 = longitude(point1),\n          phi1 = point1[1] / 2 + quarterPi,\n          sinPhi1 = sin(phi1),\n          cosPhi1 = cos(phi1),\n          delta = lambda1 - lambda0,\n          sign = delta >= 0 ? 1 : -1,\n          absDelta = sign * delta,\n          antimeridian = absDelta > pi,\n          k = sinPhi0 * sinPhi1;\n\n      sum.add(atan2(k * sign * sin(absDelta), cosPhi0 * cosPhi1 + k * cos(absDelta)));\n      angle += antimeridian ? delta + sign * tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = cartesianCross(cartesian(point0), cartesian(point1));\n        cartesianNormalizeInPlace(arc);\n        var intersection = cartesianCross(normal, arc);\n        cartesianNormalizeInPlace(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -epsilon || angle < epsilon && sum < -epsilon2) ^ (winding & 1);\n}\n", "import clipBuffer from \"./buffer.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {epsilon, halfPi} from \"../math.js\";\nimport polygonContains from \"../polygonContains.js\";\nimport {merge} from \"d3-array\";\n\nexport default function(pointVisible, clipLine, interpolate, start) {\n  return function(sink) {\n    var line = clipLine(sink),\n        ringBuffer = clipBuffer(),\n        ringSink = clipLine(ringBuffer),\n        polygonStarted = false,\n        polygon,\n        segments,\n        ring;\n\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function() {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = merge(segments);\n        var startInside = polygonContains(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          clipRejoin(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function() {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n\n      var clean = ringSink.clean(),\n          ringSegments = ringBuffer.result(),\n          i, n = ringSegments.length, m,\n          segment,\n          point;\n\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n\n      segments.push(ringSegments.filter(validSegment));\n    }\n\n    return clip;\n  };\n}\n\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - halfPi - epsilon : halfPi - a[1])\n       - ((b = b.x)[0] < 0 ? b[1] - halfPi - epsilon : halfPi - b[1]);\n}\n", "import clip from \"./index.js\";\nimport {abs, atan, cos, epsilon, halfPi, pi, sin} from \"../math.js\";\n\nexport default clip(\n  function() { return true; },\n  clipAntimeridianLine,\n  clipAntimeridianInterpolate,\n  [-pi, -halfPi]\n);\n\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n  var lambda0 = NaN,\n      phi0 = NaN,\n      sign0 = NaN,\n      clean; // no intersections\n\n  return {\n    lineStart: function() {\n      stream.lineStart();\n      clean = 1;\n    },\n    point: function(lambda1, phi1) {\n      var sign1 = lambda1 > 0 ? pi : -pi,\n          delta = abs(lambda1 - lambda0);\n      if (abs(delta - pi) < epsilon) { // line crosses a pole\n        stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? halfPi : -halfPi);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        stream.point(lambda1, phi0);\n        clean = 0;\n      } else if (sign0 !== sign1 && delta >= pi) { // line crosses antimeridian\n        if (abs(lambda0 - sign0) < epsilon) lambda0 -= sign0 * epsilon; // handle degeneracies\n        if (abs(lambda1 - sign1) < epsilon) lambda1 -= sign1 * epsilon;\n        phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        clean = 0;\n      }\n      stream.point(lambda0 = lambda1, phi0 = phi1);\n      sign0 = sign1;\n    },\n    lineEnd: function() {\n      stream.lineEnd();\n      lambda0 = phi0 = NaN;\n    },\n    clean: function() {\n      return 2 - clean; // if intersections, rejoin first and last segments\n    }\n  };\n}\n\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n  var cosPhi0,\n      cosPhi1,\n      sinLambda0Lambda1 = sin(lambda0 - lambda1);\n  return abs(sinLambda0Lambda1) > epsilon\n      ? atan((sin(phi0) * (cosPhi1 = cos(phi1)) * sin(lambda1)\n          - sin(phi1) * (cosPhi0 = cos(phi0)) * sin(lambda0))\n          / (cosPhi0 * cosPhi1 * sinLambda0Lambda1))\n      : (phi0 + phi1) / 2;\n}\n\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n  var phi;\n  if (from == null) {\n    phi = direction * halfPi;\n    stream.point(-pi, phi);\n    stream.point(0, phi);\n    stream.point(pi, phi);\n    stream.point(pi, 0);\n    stream.point(pi, -phi);\n    stream.point(0, -phi);\n    stream.point(-pi, -phi);\n    stream.point(-pi, 0);\n    stream.point(-pi, phi);\n  } else if (abs(from[0] - to[0]) > epsilon) {\n    var lambda = from[0] < to[0] ? pi : -pi;\n    phi = direction * lambda / 2;\n    stream.point(-lambda, phi);\n    stream.point(0, phi);\n    stream.point(lambda, phi);\n  } else {\n    stream.point(to[0], to[1]);\n  }\n}\n", "import {cartesian, cartesianAddInPlace, cartesianCross, cartesianDot, cartesianScale, spherical} from \"../cartesian.js\";\nimport {circleStream} from \"../circle.js\";\nimport {abs, cos, epsilon, pi, radians, sqrt} from \"../math.js\";\nimport pointEqual from \"../pointEqual.js\";\nimport clip from \"./index.js\";\n\nexport default function(radius) {\n  var cr = cos(radius),\n      delta = 6 * radians,\n      smallRadius = cr > 0,\n      notHemisphere = abs(cr) > epsilon; // TODO optimise for this common case\n\n  function interpolate(from, to, direction, stream) {\n    circleStream(stream, radius, delta, direction, from, to);\n  }\n\n  function visible(lambda, phi) {\n    return cos(lambda) * cos(phi) > cr;\n  }\n\n  // Takes a line and cuts into visible segments. Return values used for polygon\n  // clipping: 0 - there were intersections or the line was empty; 1 - no\n  // intersections 2 - there were intersections, and the first and last segments\n  // should be rejoined.\n  function clipLine(stream) {\n    var point0, // previous point\n        c0, // code for previous point\n        v0, // visibility of previous point\n        v00, // visibility of first point\n        clean; // no intersections\n    return {\n      lineStart: function() {\n        v00 = v0 = false;\n        clean = 1;\n      },\n      point: function(lambda, phi) {\n        var point1 = [lambda, phi],\n            point2,\n            v = visible(lambda, phi),\n            c = smallRadius\n              ? v ? 0 : code(lambda, phi)\n              : v ? code(lambda + (lambda < 0 ? pi : -pi), phi) : 0;\n        if (!point0 && (v00 = v0 = v)) stream.lineStart();\n        if (v !== v0) {\n          point2 = intersect(point0, point1);\n          if (!point2 || pointEqual(point0, point2) || pointEqual(point1, point2))\n            point1[2] = 1;\n        }\n        if (v !== v0) {\n          clean = 0;\n          if (v) {\n            // outside going in\n            stream.lineStart();\n            point2 = intersect(point1, point0);\n            stream.point(point2[0], point2[1]);\n          } else {\n            // inside going out\n            point2 = intersect(point0, point1);\n            stream.point(point2[0], point2[1], 2);\n            stream.lineEnd();\n          }\n          point0 = point2;\n        } else if (notHemisphere && point0 && smallRadius ^ v) {\n          var t;\n          // If the codes for two points are different, or are both zero,\n          // and there this segment intersects with the small circle.\n          if (!(c & c0) && (t = intersect(point1, point0, true))) {\n            clean = 0;\n            if (smallRadius) {\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1]);\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n            } else {\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1], 3);\n            }\n          }\n        }\n        if (v && (!point0 || !pointEqual(point0, point1))) {\n          stream.point(point1[0], point1[1]);\n        }\n        point0 = point1, v0 = v, c0 = c;\n      },\n      lineEnd: function() {\n        if (v0) stream.lineEnd();\n        point0 = null;\n      },\n      // Rejoin first and last segments if there were intersections and the first\n      // and last points were visible.\n      clean: function() {\n        return clean | ((v00 && v0) << 1);\n      }\n    };\n  }\n\n  // Intersects the great circle between a and b with the clip circle.\n  function intersect(a, b, two) {\n    var pa = cartesian(a),\n        pb = cartesian(b);\n\n    // We have two planes, n1.p = d1 and n2.p = d2.\n    // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n    var n1 = [1, 0, 0], // normal\n        n2 = cartesianCross(pa, pb),\n        n2n2 = cartesianDot(n2, n2),\n        n1n2 = n2[0], // cartesianDot(n1, n2),\n        determinant = n2n2 - n1n2 * n1n2;\n\n    // Two polar points.\n    if (!determinant) return !two && a;\n\n    var c1 =  cr * n2n2 / determinant,\n        c2 = -cr * n1n2 / determinant,\n        n1xn2 = cartesianCross(n1, n2),\n        A = cartesianScale(n1, c1),\n        B = cartesianScale(n2, c2);\n    cartesianAddInPlace(A, B);\n\n    // Solve |p(t)|^2 = 1.\n    var u = n1xn2,\n        w = cartesianDot(A, u),\n        uu = cartesianDot(u, u),\n        t2 = w * w - uu * (cartesianDot(A, A) - 1);\n\n    if (t2 < 0) return;\n\n    var t = sqrt(t2),\n        q = cartesianScale(u, (-w - t) / uu);\n    cartesianAddInPlace(q, A);\n    q = spherical(q);\n\n    if (!two) return q;\n\n    // Two intersection points.\n    var lambda0 = a[0],\n        lambda1 = b[0],\n        phi0 = a[1],\n        phi1 = b[1],\n        z;\n\n    if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n\n    var delta = lambda1 - lambda0,\n        polar = abs(delta - pi) < epsilon,\n        meridian = polar || delta < epsilon;\n\n    if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n\n    // Check that the first point is between a and b.\n    if (meridian\n        ? polar\n          ? phi0 + phi1 > 0 ^ q[1] < (abs(q[0] - lambda0) < epsilon ? phi0 : phi1)\n          : phi0 <= q[1] && q[1] <= phi1\n        : delta > pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n      var q1 = cartesianScale(u, (-w + t) / uu);\n      cartesianAddInPlace(q1, A);\n      return [q, spherical(q1)];\n    }\n  }\n\n  // Generates a 4-bit vector representing the location of a point relative to\n  // the small circle's bounding box.\n  function code(lambda, phi) {\n    var r = smallRadius ? radius : pi - radius,\n        code = 0;\n    if (lambda < -r) code |= 1; // left\n    else if (lambda > r) code |= 2; // right\n    if (phi < -r) code |= 4; // below\n    else if (phi > r) code |= 8; // above\n    return code;\n  }\n\n  return clip(visible, clipLine, interpolate, smallRadius ? [0, -radius] : [-pi, radius - pi]);\n}\n", "export default function(a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n      ay = a[1],\n      bx = b[0],\n      by = b[1],\n      t0 = 0,\n      t1 = 1,\n      dx = bx - ax,\n      dy = by - ay,\n      r;\n\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}\n", "import {abs, epsilon} from \"../math.js\";\nimport clipBuffer from \"./buffer.js\";\nimport clipLine from \"./line.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {merge} from \"d3-array\";\n\nvar clipMax = 1e9, clipMin = -clipMax;\n\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\n\nexport default function clipRectangle(x0, y0, x1, y1) {\n\n  function visible(x, y) {\n    return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n  }\n\n  function interpolate(from, to, direction, stream) {\n    var a = 0, a1 = 0;\n    if (from == null\n        || (a = corner(from, direction)) !== (a1 = corner(to, direction))\n        || comparePoint(from, to) < 0 ^ direction > 0) {\n      do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n      while ((a = (a + direction + 4) % 4) !== a1);\n    } else {\n      stream.point(to[0], to[1]);\n    }\n  }\n\n  function corner(p, direction) {\n    return abs(p[0] - x0) < epsilon ? direction > 0 ? 0 : 3\n        : abs(p[0] - x1) < epsilon ? direction > 0 ? 2 : 1\n        : abs(p[1] - y0) < epsilon ? direction > 0 ? 1 : 0\n        : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n  }\n\n  function compareIntersection(a, b) {\n    return comparePoint(a.x, b.x);\n  }\n\n  function comparePoint(a, b) {\n    var ca = corner(a, 1),\n        cb = corner(b, 1);\n    return ca !== cb ? ca - cb\n        : ca === 0 ? b[1] - a[1]\n        : ca === 1 ? a[0] - b[0]\n        : ca === 2 ? a[1] - b[1]\n        : b[0] - a[0];\n  }\n\n  return function(stream) {\n    var activeStream = stream,\n        bufferStream = clipBuffer(),\n        segments,\n        polygon,\n        ring,\n        x__, y__, v__, // first point\n        x_, y_, v_, // previous point\n        first,\n        clean;\n\n    var clipStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: polygonStart,\n      polygonEnd: polygonEnd\n    };\n\n    function point(x, y) {\n      if (visible(x, y)) activeStream.point(x, y);\n    }\n\n    function polygonInside() {\n      var winding = 0;\n\n      for (var i = 0, n = polygon.length; i < n; ++i) {\n        for (var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j) {\n          a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n          if (a1 <= y1) { if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding; }\n          else { if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding; }\n        }\n      }\n\n      return winding;\n    }\n\n    // Buffer geometry within a polygon and then clip it en masse.\n    function polygonStart() {\n      activeStream = bufferStream, segments = [], polygon = [], clean = true;\n    }\n\n    function polygonEnd() {\n      var startInside = polygonInside(),\n          cleanInside = clean && startInside,\n          visible = (segments = merge(segments)).length;\n      if (cleanInside || visible) {\n        stream.polygonStart();\n        if (cleanInside) {\n          stream.lineStart();\n          interpolate(null, null, 1, stream);\n          stream.lineEnd();\n        }\n        if (visible) {\n          clipRejoin(segments, compareIntersection, startInside, interpolate, stream);\n        }\n        stream.polygonEnd();\n      }\n      activeStream = stream, segments = polygon = ring = null;\n    }\n\n    function lineStart() {\n      clipStream.point = linePoint;\n      if (polygon) polygon.push(ring = []);\n      first = true;\n      v_ = false;\n      x_ = y_ = NaN;\n    }\n\n    // TODO rather than special-case polygons, simply handle them separately.\n    // Ideally, coincident intersection points should be jittered to avoid\n    // clipping issues.\n    function lineEnd() {\n      if (segments) {\n        linePoint(x__, y__);\n        if (v__ && v_) bufferStream.rejoin();\n        segments.push(bufferStream.result());\n      }\n      clipStream.point = point;\n      if (v_) activeStream.lineEnd();\n    }\n\n    function linePoint(x, y) {\n      var v = visible(x, y);\n      if (polygon) ring.push([x, y]);\n      if (first) {\n        x__ = x, y__ = y, v__ = v;\n        first = false;\n        if (v) {\n          activeStream.lineStart();\n          activeStream.point(x, y);\n        }\n      } else {\n        if (v && v_) activeStream.point(x, y);\n        else {\n          var a = [x_ = Math.max(clipMin, Math.min(clipMax, x_)), y_ = Math.max(clipMin, Math.min(clipMax, y_))],\n              b = [x = Math.max(clipMin, Math.min(clipMax, x)), y = Math.max(clipMin, Math.min(clipMax, y))];\n          if (clipLine(a, b, x0, y0, x1, y1)) {\n            if (!v_) {\n              activeStream.lineStart();\n              activeStream.point(a[0], a[1]);\n            }\n            activeStream.point(b[0], b[1]);\n            if (!v) activeStream.lineEnd();\n            clean = false;\n          } else if (v) {\n            activeStream.lineStart();\n            activeStream.point(x, y);\n            clean = false;\n          }\n        }\n      }\n      x_ = x, y_ = y, v_ = v;\n    }\n\n    return clipStream;\n  };\n}\n", "import clipRectangle from \"./rectangle.js\";\n\nexport default function() {\n  var x0 = 0,\n      y0 = 0,\n      x1 = 960,\n      y1 = 500,\n      cache,\n      cacheStream,\n      clip;\n\n  return clip = {\n    stream: function(stream) {\n      return cache && cacheStream === stream ? cache : cache = clipRectangle(x0, y0, x1, y1)(cacheStream = stream);\n    },\n    extent: function(_) {\n      return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [[x0, y0], [x1, y1]];\n    }\n  };\n}\n", "import {Adder} from \"d3-array\";\nimport {abs, atan2, cos, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar lengthSum,\n    lambda0,\n    sinPhi0,\n    cosPhi0;\n\nvar lengthStream = {\n  sphere: noop,\n  point: noop,\n  lineStart: lengthLineStart,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop\n};\n\nfunction lengthLineStart() {\n  lengthStream.point = lengthPointFirst;\n  lengthStream.lineEnd = lengthLineEnd;\n}\n\nfunction lengthLineEnd() {\n  lengthStream.point = lengthStream.lineEnd = noop;\n}\n\nfunction lengthPointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, sinPhi0 = sin(phi), cosPhi0 = cos(phi);\n  lengthStream.point = lengthPoint;\n}\n\nfunction lengthPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var sinPhi = sin(phi),\n      cosPhi = cos(phi),\n      delta = abs(lambda - lambda0),\n      cosDelta = cos(delta),\n      sinDelta = sin(delta),\n      x = cosPhi * sinDelta,\n      y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta,\n      z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n  lengthSum.add(atan2(sqrt(x * x + y * y), z));\n  lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n\nexport default function(object) {\n  lengthSum = new Adder();\n  stream(object, lengthStream);\n  return +lengthSum;\n}\n", "import length from \"./length.js\";\n\nvar coordinates = [null, null],\n    object = {type: \"LineString\", coordinates: coordinates};\n\nexport default function(a, b) {\n  coordinates[0] = a;\n  coordinates[1] = b;\n  return length(object);\n}\n", "import {default as polygonContains} from \"./polygonContains.js\";\nimport {default as distance} from \"./distance.js\";\nimport {epsilon2, radians} from \"./math.js\";\n\nvar containsObjectType = {\n  Feature: function(object, point) {\n    return containsGeometry(object.geometry, point);\n  },\n  FeatureCollection: function(object, point) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) if (containsGeometry(features[i].geometry, point)) return true;\n    return false;\n  }\n};\n\nvar containsGeometryType = {\n  Sphere: function() {\n    return true;\n  },\n  Point: function(object, point) {\n    return containsPoint(object.coordinates, point);\n  },\n  MultiPoint: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPoint(coordinates[i], point)) return true;\n    return false;\n  },\n  LineString: function(object, point) {\n    return containsLine(object.coordinates, point);\n  },\n  MultiLineString: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsLine(coordinates[i], point)) return true;\n    return false;\n  },\n  Polygon: function(object, point) {\n    return containsPolygon(object.coordinates, point);\n  },\n  MultiPolygon: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPolygon(coordinates[i], point)) return true;\n    return false;\n  },\n  GeometryCollection: function(object, point) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) if (containsGeometry(geometries[i], point)) return true;\n    return false;\n  }\n};\n\nfunction containsGeometry(geometry, point) {\n  return geometry && containsGeometryType.hasOwnProperty(geometry.type)\n      ? containsGeometryType[geometry.type](geometry, point)\n      : false;\n}\n\nfunction containsPoint(coordinates, point) {\n  return distance(coordinates, point) === 0;\n}\n\nfunction containsLine(coordinates, point) {\n  var ao, bo, ab;\n  for (var i = 0, n = coordinates.length; i < n; i++) {\n    bo = distance(coordinates[i], point);\n    if (bo === 0) return true;\n    if (i > 0) {\n      ab = distance(coordinates[i], coordinates[i - 1]);\n      if (\n        ab > 0 &&\n        ao <= ab &&\n        bo <= ab &&\n        (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < epsilon2 * ab\n      )\n        return true;\n    }\n    ao = bo;\n  }\n  return false;\n}\n\nfunction containsPolygon(coordinates, point) {\n  return !!polygonContains(coordinates.map(ringRadians), pointRadians(point));\n}\n\nfunction ringRadians(ring) {\n  return ring = ring.map(pointRadians), ring.pop(), ring;\n}\n\nfunction pointRadians(point) {\n  return [point[0] * radians, point[1] * radians];\n}\n\nexport default function(object, point) {\n  return (object && containsObjectType.hasOwnProperty(object.type)\n      ? containsObjectType[object.type]\n      : containsGeometry)(object, point);\n}\n", "import {range} from \"d3-array\";\nimport {abs, ceil, epsilon} from \"./math.js\";\n\nfunction graticuleX(y0, y1, dy) {\n  var y = range(y0, y1 - epsilon, dy).concat(y1);\n  return function(x) { return y.map(function(y) { return [x, y]; }); };\n}\n\nfunction graticuleY(x0, x1, dx) {\n  var x = range(x0, x1 - epsilon, dx).concat(x1);\n  return function(y) { return x.map(function(x) { return [x, y]; }); };\n}\n\nexport default function graticule() {\n  var x1, x0, X1, X0,\n      y1, y0, Y1, Y0,\n      dx = 10, dy = dx, DX = 90, DY = 360,\n      x, y, X, Y,\n      precision = 2.5;\n\n  function graticule() {\n    return {type: \"MultiLineString\", coordinates: lines()};\n  }\n\n  function lines() {\n    return range(ceil(X0 / DX) * DX, X1, DX).map(X)\n        .concat(range(ceil(Y0 / DY) * DY, Y1, DY).map(Y))\n        .concat(range(ceil(x0 / dx) * dx, x1, dx).filter(function(x) { return abs(x % DX) > epsilon; }).map(x))\n        .concat(range(ceil(y0 / dy) * dy, y1, dy).filter(function(y) { return abs(y % DY) > epsilon; }).map(y));\n  }\n\n  graticule.lines = function() {\n    return lines().map(function(coordinates) { return {type: \"LineString\", coordinates: coordinates}; });\n  };\n\n  graticule.outline = function() {\n    return {\n      type: \"Polygon\",\n      coordinates: [\n        X(X0).concat(\n        Y(Y1).slice(1),\n        X(X1).reverse().slice(1),\n        Y(Y0).reverse().slice(1))\n      ]\n    };\n  };\n\n  graticule.extent = function(_) {\n    if (!arguments.length) return graticule.extentMinor();\n    return graticule.extentMajor(_).extentMinor(_);\n  };\n\n  graticule.extentMajor = function(_) {\n    if (!arguments.length) return [[X0, Y0], [X1, Y1]];\n    X0 = +_[0][0], X1 = +_[1][0];\n    Y0 = +_[0][1], Y1 = +_[1][1];\n    if (X0 > X1) _ = X0, X0 = X1, X1 = _;\n    if (Y0 > Y1) _ = Y0, Y0 = Y1, Y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.extentMinor = function(_) {\n    if (!arguments.length) return [[x0, y0], [x1, y1]];\n    x0 = +_[0][0], x1 = +_[1][0];\n    y0 = +_[0][1], y1 = +_[1][1];\n    if (x0 > x1) _ = x0, x0 = x1, x1 = _;\n    if (y0 > y1) _ = y0, y0 = y1, y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.step = function(_) {\n    if (!arguments.length) return graticule.stepMinor();\n    return graticule.stepMajor(_).stepMinor(_);\n  };\n\n  graticule.stepMajor = function(_) {\n    if (!arguments.length) return [DX, DY];\n    DX = +_[0], DY = +_[1];\n    return graticule;\n  };\n\n  graticule.stepMinor = function(_) {\n    if (!arguments.length) return [dx, dy];\n    dx = +_[0], dy = +_[1];\n    return graticule;\n  };\n\n  graticule.precision = function(_) {\n    if (!arguments.length) return precision;\n    precision = +_;\n    x = graticuleX(y0, y1, 90);\n    y = graticuleY(x0, x1, precision);\n    X = graticuleX(Y0, Y1, 90);\n    Y = graticuleY(X0, X1, precision);\n    return graticule;\n  };\n\n  return graticule\n      .extentMajor([[-180, -90 + epsilon], [180, 90 - epsilon]])\n      .extentMinor([[-180, -80 - epsilon], [180, 80 + epsilon]]);\n}\n\nexport function graticule10() {\n  return graticule()();\n}\n", "import {asin, atan2, cos, degrees, haversin, radians, sin, sqrt} from \"./math.js\";\n\nexport default function(a, b) {\n  var x0 = a[0] * radians,\n      y0 = a[1] * radians,\n      x1 = b[0] * radians,\n      y1 = b[1] * radians,\n      cy0 = cos(y0),\n      sy0 = sin(y0),\n      cy1 = cos(y1),\n      sy1 = sin(y1),\n      kx0 = cy0 * cos(x0),\n      ky0 = cy0 * sin(x0),\n      kx1 = cy1 * cos(x1),\n      ky1 = cy1 * sin(x1),\n      d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n      k = sin(d);\n\n  var interpolate = d ? function(t) {\n    var B = sin(t *= d) / k,\n        A = sin(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      atan2(y, x) * degrees,\n      atan2(z, sqrt(x * x + y * y)) * degrees\n    ];\n  } : function() {\n    return [x0 * degrees, y0 * degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n", "export default x => x;\n", "import {Adder} from \"d3-array\";\nimport {abs} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar areaSum = new Adder(),\n    areaRingSum = new Adder(),\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    areaStream.lineStart = areaStream.lineEnd = areaStream.point = noop;\n    areaSum.add(abs(areaRingSum));\n    areaRingSum = new Adder();\n  },\n  result: function() {\n    var area = areaSum / 2;\n    areaSum = new Adder();\n    return area;\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaPointFirst(x, y) {\n  areaStream.point = areaPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction areaPoint(x, y) {\n  areaRingSum.add(y0 * x - x0 * y);\n  x0 = x, y0 = y;\n}\n\nfunction areaRingEnd() {\n  areaPoint(x00, y00);\n}\n\nexport default areaStream;\n", "import noop from \"../noop.js\";\n\nvar x0 = Infinity,\n    y0 = x0,\n    x1 = -x0,\n    y1 = x1;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop,\n  result: function() {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\n\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\n\nexport default boundsStream;\n", "import {sqrt} from \"../math.js\";\n\n// TODO Enforce positive area for exterior, negative area for interior?\n\nvar X0 = 0,\n    Y0 = 0,\n    Z0 = 0,\n    X1 = 0,\n    Y1 = 0,\n    Z1 = 0,\n    X2 = 0,\n    Y2 = 0,\n    Z2 = 0,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar centroidStream = {\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.point = centroidPoint;\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  },\n  result: function() {\n    var centroid = Z2 ? [X2 / Z2, Y2 / Z2]\n        : Z1 ? [X1 / Z1, Y1 / Z1]\n        : Z0 ? [X0 / Z0, Y0 / Z0]\n        : [NaN, NaN];\n    X0 = Y0 = Z0 =\n    X1 = Y1 = Z1 =\n    X2 = Y2 = Z2 = 0;\n    return centroid;\n  }\n};\n\nfunction centroidPoint(x, y) {\n  X0 += x;\n  Y0 += y;\n  ++Z0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidPointFirstLine;\n}\n\nfunction centroidPointFirstLine(x, y) {\n  centroidStream.point = centroidPointLine;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidPointLine(x, y) {\n  var dx = x - x0, dy = y - y0, z = sqrt(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingStart() {\n  centroidStream.point = centroidPointFirstRing;\n}\n\nfunction centroidRingEnd() {\n  centroidPointRing(x00, y00);\n}\n\nfunction centroidPointFirstRing(x, y) {\n  centroidStream.point = centroidPointRing;\n  centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\n\nfunction centroidPointRing(x, y) {\n  var dx = x - x0,\n      dy = y - y0,\n      z = sqrt(dx * dx + dy * dy);\n\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n\n  z = y0 * x - x0 * y;\n  X2 += z * (x0 + x);\n  Y2 += z * (y0 + y);\n  Z2 += z * 3;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nexport default centroidStream;\n", "import {tau} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nexport default function PathContext(context) {\n  this._context = context;\n}\n\nPathContext.prototype = {\n  _radius: 4.5,\n  pointRadius: function(_) {\n    return this._radius = _, this;\n  },\n  polygonStart: function() {\n    this._line = 0;\n  },\n  polygonEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line === 0) this._context.closePath();\n    this._point = NaN;\n  },\n  point: function(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._context.moveTo(x, y);\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._context.lineTo(x, y);\n        break;\n      }\n      default: {\n        this._context.moveTo(x + this._radius, y);\n        this._context.arc(x, y, this._radius, 0, tau);\n        break;\n      }\n    }\n  },\n  result: noop\n};\n", "import {Adder} from \"d3-array\";\nimport {sqrt} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar lengthSum = new Adder(),\n    lengthRing,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar lengthStream = {\n  point: noop,\n  lineStart: function() {\n    lengthStream.point = lengthPointFirst;\n  },\n  lineEnd: function() {\n    if (lengthRing) lengthPoint(x00, y00);\n    lengthStream.point = noop;\n  },\n  polygonStart: function() {\n    lengthRing = true;\n  },\n  polygonEnd: function() {\n    lengthRing = null;\n  },\n  result: function() {\n    var length = +lengthSum;\n    lengthSum = new Adder();\n    return length;\n  }\n};\n\nfunction lengthPointFirst(x, y) {\n  lengthStream.point = lengthPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction lengthPoint(x, y) {\n  x0 -= x, y0 -= y;\n  lengthSum.add(sqrt(x0 * x0 + y0 * y0));\n  x0 = x, y0 = y;\n}\n\nexport default lengthStream;\n", "export default function PathString() {\n  this._string = [];\n}\n\nPathString.prototype = {\n  _radius: 4.5,\n  _circle: circle(4.5),\n  pointRadius: function(_) {\n    if ((_ = +_) !== this._radius) this._radius = _, this._circle = null;\n    return this;\n  },\n  polygonStart: function() {\n    this._line = 0;\n  },\n  polygonEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line === 0) this._string.push(\"Z\");\n    this._point = NaN;\n  },\n  point: function(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._string.push(\"M\", x, \",\", y);\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._string.push(\"L\", x, \",\", y);\n        break;\n      }\n      default: {\n        if (this._circle == null) this._circle = circle(this._radius);\n        this._string.push(\"M\", x, \",\", y, this._circle);\n        break;\n      }\n    }\n  },\n  result: function() {\n    if (this._string.length) {\n      var result = this._string.join(\"\");\n      this._string = [];\n      return result;\n    } else {\n      return null;\n    }\n  }\n};\n\nfunction circle(radius) {\n  return \"m0,\" + radius\n      + \"a\" + radius + \",\" + radius + \" 0 1,1 0,\" + -2 * radius\n      + \"a\" + radius + \",\" + radius + \" 0 1,1 0,\" + 2 * radius\n      + \"z\";\n}\n", "import identity from \"../identity.js\";\nimport stream from \"../stream.js\";\nimport pathArea from \"./area.js\";\nimport pathBounds from \"./bounds.js\";\nimport pathCentroid from \"./centroid.js\";\nimport PathContext from \"./context.js\";\nimport pathMeasure from \"./measure.js\";\nimport PathString from \"./string.js\";\n\nexport default function(projection, context) {\n  var pointRadius = 4.5,\n      projectionStream,\n      contextStream;\n\n  function path(object) {\n    if (object) {\n      if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n      stream(object, projectionStream(contextStream));\n    }\n    return contextStream.result();\n  }\n\n  path.area = function(object) {\n    stream(object, projectionStream(pathArea));\n    return pathArea.result();\n  };\n\n  path.measure = function(object) {\n    stream(object, projectionStream(pathMeasure));\n    return pathMeasure.result();\n  };\n\n  path.bounds = function(object) {\n    stream(object, projectionStream(pathBounds));\n    return pathBounds.result();\n  };\n\n  path.centroid = function(object) {\n    stream(object, projectionStream(pathCentroid));\n    return pathCentroid.result();\n  };\n\n  path.projection = function(_) {\n    return arguments.length ? (projectionStream = _ == null ? (projection = null, identity) : (projection = _).stream, path) : projection;\n  };\n\n  path.context = function(_) {\n    if (!arguments.length) return context;\n    contextStream = _ == null ? (context = null, new PathString) : new PathContext(context = _);\n    if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n    return path;\n  };\n\n  path.pointRadius = function(_) {\n    if (!arguments.length) return pointRadius;\n    pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n    return path;\n  };\n\n  return path.projection(projection).context(context);\n}\n", "export default function(methods) {\n  return {\n    stream: transformer(methods)\n  };\n}\n\nexport function transformer(methods) {\n  return function(stream) {\n    var s = new TransformStream;\n    for (var key in methods) s[key] = methods[key];\n    s.stream = stream;\n    return s;\n  };\n}\n\nfunction TransformStream() {}\n\nTransformStream.prototype = {\n  constructor: TransformStream,\n  point: function(x, y) { this.stream.point(x, y); },\n  sphere: function() { this.stream.sphere(); },\n  lineStart: function() { this.stream.lineStart(); },\n  lineEnd: function() { this.stream.lineEnd(); },\n  polygonStart: function() { this.stream.polygonStart(); },\n  polygonEnd: function() { this.stream.polygonEnd(); }\n};\n", "import {default as geoStream} from \"../stream.js\";\nimport boundsStream from \"../path/bounds.js\";\n\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  geoStream(object, projection.stream(boundsStream));\n  fitBounds(boundsStream.result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\n\nexport function fitExtent(projection, extent, object) {\n  return fit(projection, function(b) {\n    var w = extent[1][0] - extent[0][0],\n        h = extent[1][1] - extent[0][1],\n        k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n        x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n        y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\n\nexport function fitWidth(projection, width, object) {\n  return fit(projection, function(b) {\n    var w = +width,\n        k = w / (b[1][0] - b[0][0]),\n        x = (w - k * (b[1][0] + b[0][0])) / 2,\n        y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitHeight(projection, height, object) {\n  return fit(projection, function(b) {\n    var h = +height,\n        k = h / (b[1][1] - b[0][1]),\n        x = -k * b[0][0],\n        y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n", "import {cartesian} from \"../cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, radians, sqrt} from \"../math.js\";\nimport {transformer} from \"../transform.js\";\n\nvar maxDepth = 16, // maximum depth of subdivision\n    cosMinDistance = cos(30 * radians); // cos(minimum angular distance)\n\nexport default function(project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\n\nfunction resampleNone(project) {\n  return transformer({\n    point: function(x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\n\nfunction resample(project, delta2) {\n\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n        dy = y1 - y0,\n        d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n          b = b0 + b1,\n          c = c0 + c1,\n          m = sqrt(a * a + b * b + c * c),\n          phi2 = asin(c /= m),\n          lambda2 = abs(abs(c) - 1) < epsilon || abs(lambda0 - lambda1) < epsilon ? (lambda0 + lambda1) / 2 : atan2(b, a),\n          p = project(lambda2, phi2),\n          x2 = p[0],\n          y2 = p[1],\n          dx2 = x2 - x0,\n          dy2 = y2 - y0,\n          dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n          || abs((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n          || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) { // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function(stream) {\n    var lambda00, x00, y00, a00, b00, c00, // first point\n        lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() { stream.polygonStart(); resampleStream.lineStart = ringStart; },\n      polygonEnd: function() { stream.polygonEnd(); resampleStream.lineStart = lineStart; }\n    };\n\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n\n    function linePoint(lambda, phi) {\n      var c = cartesian([lambda, phi]), p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n\n    return resampleStream;\n  };\n}\n", "import clipAntimeridian from \"../clip/antimeridian.js\";\nimport clipCircle from \"../clip/circle.js\";\nimport clipRectangle from \"../clip/rectangle.js\";\nimport compose from \"../compose.js\";\nimport identity from \"../identity.js\";\nimport {cos, degrees, radians, sin, sqrt} from \"../math.js\";\nimport {rotateRadians} from \"../rotation.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport resample from \"./resample.js\";\n\nvar transformRadians = transformer({\n  point: function(x, y) {\n    this.stream.point(x * radians, y * radians);\n  }\n});\n\nfunction transformRotate(rotate) {\n  return transformer({\n    point: function(x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\n\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function(x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\n\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = cos(alpha),\n      sinAlpha = sin(alpha),\n      a = cosAlpha * k,\n      b = sinAlpha * k,\n      ai = cosAlpha / k,\n      bi = sinAlpha / k,\n      ci = (sinAlpha * dy - cosAlpha * dx) / k,\n      fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function(x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\n\nexport default function projection(project) {\n  return projectionMutator(function() { return project; })();\n}\n\nexport function projectionMutator(projectAt) {\n  var project,\n      k = 150, // scale\n      x = 480, y = 250, // translate\n      lambda = 0, phi = 0, // center\n      deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, // pre-rotate\n      alpha = 0, // post-rotate angle\n      sx = 1, // reflectX\n      sy = 1, // reflectX\n      theta = null, preclip = clipAntimeridian, // pre-clip angle\n      x0 = null, y0, x1, y1, postclip = identity, // post-clip extent\n      delta2 = 0.5, // precision\n      projectResample,\n      projectTransform,\n      projectRotateTransform,\n      cache,\n      cacheStream;\n\n  function projection(point) {\n    return projectRotateTransform(point[0] * radians, point[1] * radians);\n  }\n\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * degrees, point[1] * degrees];\n  }\n\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n\n  projection.preclip = function(_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n\n  projection.clipAngle = function(_) {\n    return arguments.length ? (preclip = +_ ? clipCircle(theta = _ * radians) : (theta = null, clipAntimeridian), reset()) : theta * degrees;\n  };\n\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n\n  projection.translate = function(_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n\n  projection.center = function(_) {\n    return arguments.length ? (lambda = _[0] % 360 * radians, phi = _[1] % 360 * radians, recenter()) : [lambda * degrees, phi * degrees];\n  };\n\n  projection.rotate = function(_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * radians, deltaPhi = _[1] % 360 * radians, deltaGamma = _.length > 2 ? _[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];\n  };\n\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, recenter()) : alpha * degrees;\n  };\n\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n\n  projection.precision = function(_) {\n    return arguments.length ? (projectResample = resample(projectTransform, delta2 = _ * _), reset()) : sqrt(delta2);\n  };\n\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n        transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = compose(project, transform);\n    projectRotateTransform = compose(rotate, projectTransform);\n    projectResample = resample(projectTransform, delta2);\n    return reset();\n  }\n\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  return function() {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}\n", "import {degrees, pi, radians} from \"../math.js\";\nimport {projectionMutator} from \"./index.js\";\n\nexport function conicProjection(projectAt) {\n  var phi0 = 0,\n      phi1 = pi / 3,\n      m = projectionMutator(projectAt),\n      p = m(phi0, phi1);\n\n  p.parallels = function(_) {\n    return arguments.length ? m(phi0 = _[0] * radians, phi1 = _[1] * radians) : [phi0 * degrees, phi1 * degrees];\n  };\n\n  return p;\n}\n", "import {asin, cos, sin} from \"../math.js\";\n\nexport function cylindricalEqualAreaRaw(phi0) {\n  var cosPhi0 = cos(phi0);\n\n  function forward(lambda, phi) {\n    return [lambda * cosPhi0, sin(phi) / cosPhi0];\n  }\n\n  forward.invert = function(x, y) {\n    return [x / cosPhi0, asin(y * cosPhi0)];\n  };\n\n  return forward;\n}\n", "import {abs, asin, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {cylindricalEqualAreaRaw} from \"./cylindricalEqualArea.js\";\n\nexport function conicEqualAreaRaw(y0, y1) {\n  var sy0 = sin(y0), n = (sy0 + sin(y1)) / 2;\n\n  // Are the parallels symmetrical around the Equator?\n  if (abs(n) < epsilon) return cylindricalEqualAreaRaw(y0);\n\n  var c = 1 + sy0 * (2 * n - sy0), r0 = sqrt(c) / n;\n\n  function project(x, y) {\n    var r = sqrt(c - 2 * n * sin(y)) / n;\n    return [r * sin(x *= n), r0 - r * cos(x)];\n  }\n\n  project.invert = function(x, y) {\n    var r0y = r0 - y,\n        l = atan2(x, abs(r0y)) * sign(r0y);\n    if (r0y * n < 0)\n      l -= pi * sign(x) * sign(r0y);\n    return [l / n, asin((c - (x * x + r0y * r0y) * n * n) / (2 * n))];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEqualAreaRaw)\n      .scale(155.424)\n      .center([0, 33.6442]);\n}\n", "import conicEqualArea from \"./conicEqualArea.js\";\n\nexport default function() {\n  return conicEqualArea()\n      .parallels([29.5, 45.5])\n      .scale(1070)\n      .translate([480, 250])\n      .rotate([96, 0])\n      .center([-0.6, 38.7]);\n}\n", "import {epsilon} from \"../math.js\";\nimport albers from \"./albers.js\";\nimport conicEqualArea from \"./conicEqualArea.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n  var n = streams.length;\n  return {\n    point: function(x, y) { var i = -1; while (++i < n) streams[i].point(x, y); },\n    sphere: function() { var i = -1; while (++i < n) streams[i].sphere(); },\n    lineStart: function() { var i = -1; while (++i < n) streams[i].lineStart(); },\n    lineEnd: function() { var i = -1; while (++i < n) streams[i].lineEnd(); },\n    polygonStart: function() { var i = -1; while (++i < n) streams[i].polygonStart(); },\n    polygonEnd: function() { var i = -1; while (++i < n) streams[i].polygonEnd(); }\n  };\n}\n\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\nexport default function() {\n  var cache,\n      cacheStream,\n      lower48 = albers(), lower48Point,\n      alaska = conicEqualArea().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]), alaskaPoint, // EPSG:3338\n      hawaii = conicEqualArea().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]), hawaiiPoint, // ESRI:102007\n      point, pointStream = {point: function(x, y) { point = [x, y]; }};\n\n  function albersUsa(coordinates) {\n    var x = coordinates[0], y = coordinates[1];\n    return point = null,\n        (lower48Point.point(x, y), point)\n        || (alaskaPoint.point(x, y), point)\n        || (hawaiiPoint.point(x, y), point);\n  }\n\n  albersUsa.invert = function(coordinates) {\n    var k = lower48.scale(),\n        t = lower48.translate(),\n        x = (coordinates[0] - t[0]) / k,\n        y = (coordinates[1] - t[1]) / k;\n    return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska\n        : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii\n        : lower48).invert(coordinates);\n  };\n\n  albersUsa.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);\n  };\n\n  albersUsa.precision = function(_) {\n    if (!arguments.length) return lower48.precision();\n    lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n    return reset();\n  };\n\n  albersUsa.scale = function(_) {\n    if (!arguments.length) return lower48.scale();\n    lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n    return albersUsa.translate(lower48.translate());\n  };\n\n  albersUsa.translate = function(_) {\n    if (!arguments.length) return lower48.translate();\n    var k = lower48.scale(), x = +_[0], y = +_[1];\n\n    lower48Point = lower48\n        .translate(_)\n        .clipExtent([[x - 0.455 * k, y - 0.238 * k], [x + 0.455 * k, y + 0.238 * k]])\n        .stream(pointStream);\n\n    alaskaPoint = alaska\n        .translate([x - 0.307 * k, y + 0.201 * k])\n        .clipExtent([[x - 0.425 * k + epsilon, y + 0.120 * k + epsilon], [x - 0.214 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    hawaiiPoint = hawaii\n        .translate([x - 0.205 * k, y + 0.212 * k])\n        .clipExtent([[x - 0.214 * k + epsilon, y + 0.166 * k + epsilon], [x - 0.115 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    return reset();\n  };\n\n  albersUsa.fitExtent = function(extent, object) {\n    return fitExtent(albersUsa, extent, object);\n  };\n\n  albersUsa.fitSize = function(size, object) {\n    return fitSize(albersUsa, size, object);\n  };\n\n  albersUsa.fitWidth = function(width, object) {\n    return fitWidth(albersUsa, width, object);\n  };\n\n  albersUsa.fitHeight = function(height, object) {\n    return fitHeight(albersUsa, height, object);\n  };\n\n  function reset() {\n    cache = cacheStream = null;\n    return albersUsa;\n  }\n\n  return albersUsa.scale(1070);\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"../math.js\";\n\nexport function azimuthalRaw(scale) {\n  return function(x, y) {\n    var cx = cos(x),\n        cy = cos(y),\n        k = scale(cx * cy);\n        if (k === Infinity) return [2, 0];\n    return [\n      k * cy * sin(x),\n      k * sin(y)\n    ];\n  }\n}\n\nexport function azimuthalInvert(angle) {\n  return function(x, y) {\n    var z = sqrt(x * x + y * y),\n        c = angle(z),\n        sc = sin(c),\n        cc = cos(c);\n    return [\n      atan2(x * sc, z * cc),\n      asin(z && y * sc / z)\n    ];\n  }\n}\n", "import {asin, sqrt} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEqualAreaRaw = azimuthalRaw(function(cxcy) {\n  return sqrt(2 / (1 + cxcy));\n});\n\nazimuthalEqualAreaRaw.invert = azimuthalInvert(function(z) {\n  return 2 * asin(z / 2);\n});\n\nexport default function() {\n  return projection(azimuthalEqualAreaRaw)\n      .scale(124.75)\n      .clipAngle(180 - 1e-3);\n}\n", "import {acos, sin} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEquidistantRaw = azimuthalRaw(function(c) {\n  return (c = acos(c)) && c / sin(c);\n});\n\nazimuthalEquidistantRaw.invert = azimuthalInvert(function(z) {\n  return z;\n});\n\nexport default function() {\n  return projection(azimuthalEquidistantRaw)\n      .scale(79.4188)\n      .clipAngle(180 - 1e-3);\n}\n", "import {atan, exp, halfPi, log, pi, tan, tau} from \"../math.js\";\nimport rotation from \"../rotation.js\";\nimport projection from \"./index.js\";\n\nexport function mercatorRaw(lambda, phi) {\n  return [lambda, log(tan((halfPi + phi) / 2))];\n}\n\nmercatorRaw.invert = function(x, y) {\n  return [x, 2 * atan(exp(y)) - halfPi];\n};\n\nexport default function() {\n  return mercatorProjection(mercatorRaw)\n      .scale(961 / tau);\n}\n\nexport function mercatorProjection(project) {\n  var m = projection(project),\n      center = m.center,\n      scale = m.scale,\n      translate = m.translate,\n      clipExtent = m.clipExtent,\n      x0 = null, y0, x1, y1; // clip extent\n\n  m.scale = function(_) {\n    return arguments.length ? (scale(_), reclip()) : scale();\n  };\n\n  m.translate = function(_) {\n    return arguments.length ? (translate(_), reclip()) : translate();\n  };\n\n  m.center = function(_) {\n    return arguments.length ? (center(_), reclip()) : center();\n  };\n\n  m.clipExtent = function(_) {\n    return arguments.length ? ((_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1])), reclip()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  function reclip() {\n    var k = pi * scale(),\n        t = m(rotation(m.rotate()).invert([0, 0]));\n    return clipExtent(x0 == null\n        ? [[t[0] - k, t[1] - k], [t[0] + k, t[1] + k]] : project === mercatorRaw\n        ? [[Math.max(t[0] - k, x0), y0], [Math.min(t[0] + k, x1), y1]]\n        : [[x0, Math.max(t[1] - k, y0)], [x1, Math.min(t[1] + k, y1)]]);\n  }\n\n  return reclip();\n}\n", "import {abs, atan, atan2, cos, epsilon, halfPi, log, pi, pow, sign, sin, sqrt, tan} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {mercatorRaw} from \"./mercator.js\";\n\nfunction tany(y) {\n  return tan((halfPi + y) / 2);\n}\n\nexport function conicConformalRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : log(cy0 / cos(y1)) / log(tany(y1) / tany(y0)),\n      f = cy0 * pow(tany(y0), n) / n;\n\n  if (!n) return mercatorRaw;\n\n  function project(x, y) {\n    if (f > 0) { if (y < -halfPi + epsilon) y = -halfPi + epsilon; }\n    else { if (y > halfPi - epsilon) y = halfPi - epsilon; }\n    var r = f / pow(tany(y), n);\n    return [r * sin(n * x), f - r * cos(n * x)];\n  }\n\n  project.invert = function(x, y) {\n    var fy = f - y, r = sign(n) * sqrt(x * x + fy * fy),\n      l = atan2(x, abs(fy)) * sign(fy);\n    if (fy * n < 0)\n      l -= pi * sign(x) * sign(fy);\n    return [l / n, 2 * atan(pow(f / r, 1 / n)) - halfPi];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicConformalRaw)\n      .scale(109.5)\n      .parallels([30, 30]);\n}\n", "import projection from \"./index.js\";\n\nexport function equirectangularRaw(lambda, phi) {\n  return [lambda, phi];\n}\n\nequirectangularRaw.invert = equirectangularRaw;\n\nexport default function() {\n  return projection(equirectangularRaw)\n      .scale(152.63);\n}\n", "import {abs, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {equirectangularRaw} from \"./equirectangular.js\";\n\nexport function conicEquidistantRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : (cy0 - cos(y1)) / (y1 - y0),\n      g = cy0 / n + y0;\n\n  if (abs(n) < epsilon) return equirectangularRaw;\n\n  function project(x, y) {\n    var gy = g - y, nx = n * x;\n    return [gy * sin(nx), g - gy * cos(nx)];\n  }\n\n  project.invert = function(x, y) {\n    var gy = g - y,\n        l = atan2(x, abs(gy)) * sign(gy);\n    if (gy * n < 0)\n      l -= pi * sign(x) * sign(gy);\n    return [l / n, g - sign(n) * sqrt(x * x + gy * gy)];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEquidistantRaw)\n      .scale(131.154)\n      .center([0, 13.9389]);\n}\n", "import projection from \"./index.js\";\nimport {abs, asin, cos, epsilon2, sin, sqrt} from \"../math.js\";\n\nvar A1 = 1.340264,\n    A2 = -0.081106,\n    A3 = 0.000893,\n    A4 = 0.003796,\n    M = sqrt(3) / 2,\n    iterations = 12;\n\nexport function equalEarthRaw(lambda, phi) {\n  var l = asin(M * sin(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n  return [\n    lambda * cos(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n    l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n  ];\n}\n\nequalEarthRaw.invert = function(x, y) {\n  var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n  for (var i = 0, delta, fy, fpy; i < iterations; ++i) {\n    fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n    fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n    l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n    if (abs(delta) < epsilon2) break;\n  }\n  return [\n    M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / cos(l),\n    asin(sin(l) / M)\n  ];\n};\n\nexport default function() {\n  return projection(equalEarthRaw)\n      .scale(177.158);\n}\n", "import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function gnomonicRaw(x, y) {\n  var cy = cos(y), k = cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\ngnomonicRaw.invert = azimuthalInvert(atan);\n\nexport default function() {\n  return projection(gnomonicRaw)\n      .scale(144.049)\n      .clipAngle(60);\n}\n", "import clipRectangle from \"../clip/rectangle.js\";\nimport identity from \"../identity.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport {cos, degrees, radians, sin} from \"../math.js\";\n\nexport default function() {\n  var k = 1, tx = 0, ty = 0, sx = 1, sy = 1, // scale, translate and reflect\n      alpha = 0, ca, sa, // angle\n      x0 = null, y0, x1, y1, // clip extent\n      kx = 1, ky = 1,\n      transform = transformer({\n        point: function(x, y) {\n          var p = projection([x, y])\n          this.stream.point(p[0], p[1]);\n        }\n      }),\n      postclip = identity,\n      cache,\n      cacheStream;\n\n  function reset() {\n    kx = k * sx;\n    ky = k * sy;\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  function projection (p) {\n    var x = p[0] * kx, y = p[1] * ky;\n    if (alpha) {\n      var t = y * ca - x * sa;\n      x = x * ca + y * sa;\n      y = t;\n    }    \n    return [x + tx, y + ty];\n  }\n  projection.invert = function(p) {\n    var x = p[0] - tx, y = p[1] - ty;\n    if (alpha) {\n      var t = y * ca + x * sa;\n      x = x * ca - y * sa;\n      y = t;\n    }\n    return [x / kx, y / ky];\n  };\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n  };\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, reset()) : k;\n  };\n  projection.translate = function(_) {\n    return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [tx, ty];\n  }\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, sa = sin(alpha), ca = cos(alpha), reset()) : alpha * degrees;\n  };\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n  };\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n  };\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  return projection;\n}\n", "import projection from \"./index.js\";\nimport {abs, epsilon} from \"../math.js\";\n\nexport function naturalEarth1Raw(lambda, phi) {\n  var phi2 = phi * phi, phi4 = phi2 * phi2;\n  return [\n    lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n    phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n  ];\n}\n\nnaturalEarth1Raw.invert = function(x, y) {\n  var phi = y, i = 25, delta;\n  do {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) /\n        (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n  } while (abs(delta) > epsilon && --i > 0);\n  return [\n    x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n    phi\n  ];\n};\n\nexport default function() {\n  return projection(naturalEarth1Raw)\n      .scale(175.295);\n}\n", "import {asin, cos, epsilon, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function orthographicRaw(x, y) {\n  return [cos(y) * sin(x), sin(y)];\n}\n\northographicRaw.invert = azimuthalInvert(asin);\n\nexport default function() {\n  return projection(orthographicRaw)\n      .scale(249.5)\n      .clipAngle(90 + epsilon);\n}\n", "import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function stereographicRaw(x, y) {\n  var cy = cos(y), k = 1 + cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\nstereographicRaw.invert = azimuthalInvert(function(z) {\n  return 2 * atan(z);\n});\n\nexport default function() {\n  return projection(stereographicRaw)\n      .scale(250)\n      .clipAngle(142);\n}\n", "import {atan, exp, halfPi, log, tan} from \"../math.js\";\nimport {mercatorProjection} from \"./mercator.js\";\n\nexport function transverseMercatorRaw(lambda, phi) {\n  return [log(tan((halfPi + phi) / 2)), -lambda];\n}\n\ntransverseMercatorRaw.invert = function(x, y) {\n  return [-y, 2 * atan(exp(x)) - halfPi];\n};\n\nexport default function() {\n  var m = mercatorProjection(transverseMercatorRaw),\n      center = m.center,\n      rotate = m.rotate;\n\n  m.center = function(_) {\n    return arguments.length ? center([-_[1], _[0]]) : (_ = center(), [_[1], -_[0]]);\n  };\n\n  m.rotate = function(_) {\n    return arguments.length ? rotate([_[0], _[1], _.length > 2 ? _[2] + 90 : 90]) : (_ = rotate(), [_[0], _[1], _[2] - 90]);\n  };\n\n  return rotate([0, 0, 90])\n      .scale(159.155);\n}\n", "export default function(x) {\n  return x;\n}\n", "import identity from \"./identity.js\";\n\nexport default function(transform) {\n  if (transform == null) return identity;\n  var x0,\n      y0,\n      kx = transform.scale[0],\n      ky = transform.scale[1],\n      dx = transform.translate[0],\n      dy = transform.translate[1];\n  return function(input, i) {\n    if (!i) x0 = y0 = 0;\n    var j = 2, n = input.length, output = new Array(n);\n    output[0] = (x0 += input[0]) * kx + dx;\n    output[1] = (y0 += input[1]) * ky + dy;\n    while (j < n) output[j] = input[j], ++j;\n    return output;\n  };\n}\n", "export default function(array, n) {\n  var t, j = array.length, i = j - n;\n  while (i < --j) t = array[i], array[i++] = array[j], array[j] = t;\n}\n", "import reverse from \"./reverse.js\";\nimport transform from \"./transform.js\";\n\nexport default function(topology, o) {\n  if (typeof o === \"string\") o = topology.objects[o];\n  return o.type === \"GeometryCollection\"\n      ? {type: \"FeatureCollection\", features: o.geometries.map(function(o) { return feature(topology, o); })}\n      : feature(topology, o);\n}\n\nfunction feature(topology, o) {\n  var id = o.id,\n      bbox = o.bbox,\n      properties = o.properties == null ? {} : o.properties,\n      geometry = object(topology, o);\n  return id == null && bbox == null ? {type: \"Feature\", properties: properties, geometry: geometry}\n      : bbox == null ? {type: \"Feature\", id: id, properties: properties, geometry: geometry}\n      : {type: \"Feature\", id: id, bbox: bbox, properties: properties, geometry: geometry};\n}\n\nexport function object(topology, o) {\n  var transformPoint = transform(topology.transform),\n      arcs = topology.arcs;\n\n  function arc(i, points) {\n    if (points.length) points.pop();\n    for (var a = arcs[i < 0 ? ~i : i], k = 0, n = a.length; k < n; ++k) {\n      points.push(transformPoint(a[k], k));\n    }\n    if (i < 0) reverse(points, n);\n  }\n\n  function point(p) {\n    return transformPoint(p);\n  }\n\n  function line(arcs) {\n    var points = [];\n    for (var i = 0, n = arcs.length; i < n; ++i) arc(arcs[i], points);\n    if (points.length < 2) points.push(points[0]); // This should never happen per the specification.\n    return points;\n  }\n\n  function ring(arcs) {\n    var points = line(arcs);\n    while (points.length < 4) points.push(points[0]); // This may happen if an arc has only two points.\n    return points;\n  }\n\n  function polygon(arcs) {\n    return arcs.map(ring);\n  }\n\n  function geometry(o) {\n    var type = o.type, coordinates;\n    switch (type) {\n      case \"GeometryCollection\": return {type: type, geometries: o.geometries.map(geometry)};\n      case \"Point\": coordinates = point(o.coordinates); break;\n      case \"MultiPoint\": coordinates = o.coordinates.map(point); break;\n      case \"LineString\": coordinates = line(o.arcs); break;\n      case \"MultiLineString\": coordinates = o.arcs.map(line); break;\n      case \"Polygon\": coordinates = polygon(o.arcs); break;\n      case \"MultiPolygon\": coordinates = o.arcs.map(polygon); break;\n      default: return null;\n    }\n    return {type: type, coordinates: coordinates};\n  }\n\n  return geometry(o);\n}\n", "export default function(topology, arcs) {\n  var stitchedArcs = {},\n      fragmentByStart = {},\n      fragmentByEnd = {},\n      fragments = [],\n      emptyIndex = -1;\n\n  // Stitch empty arcs first, since they may be subsumed by other arcs.\n  arcs.forEach(function(i, j) {\n    var arc = topology.arcs[i < 0 ? ~i : i], t;\n    if (arc.length < 3 && !arc[1][0] && !arc[1][1]) {\n      t = arcs[++emptyIndex], arcs[emptyIndex] = i, arcs[j] = t;\n    }\n  });\n\n  arcs.forEach(function(i) {\n    var e = ends(i),\n        start = e[0],\n        end = e[1],\n        f, g;\n\n    if (f = fragmentByEnd[start]) {\n      delete fragmentByEnd[f.end];\n      f.push(i);\n      f.end = end;\n      if (g = fragmentByStart[end]) {\n        delete fragmentByStart[g.start];\n        var fg = g === f ? f : f.concat(g);\n        fragmentByStart[fg.start = f.start] = fragmentByEnd[fg.end = g.end] = fg;\n      } else {\n        fragmentByStart[f.start] = fragmentByEnd[f.end] = f;\n      }\n    } else if (f = fragmentByStart[end]) {\n      delete fragmentByStart[f.start];\n      f.unshift(i);\n      f.start = start;\n      if (g = fragmentByEnd[start]) {\n        delete fragmentByEnd[g.end];\n        var gf = g === f ? f : g.concat(f);\n        fragmentByStart[gf.start = g.start] = fragmentByEnd[gf.end = f.end] = gf;\n      } else {\n        fragmentByStart[f.start] = fragmentByEnd[f.end] = f;\n      }\n    } else {\n      f = [i];\n      fragmentByStart[f.start = start] = fragmentByEnd[f.end = end] = f;\n    }\n  });\n\n  function ends(i) {\n    var arc = topology.arcs[i < 0 ? ~i : i], p0 = arc[0], p1;\n    if (topology.transform) p1 = [0, 0], arc.forEach(function(dp) { p1[0] += dp[0], p1[1] += dp[1]; });\n    else p1 = arc[arc.length - 1];\n    return i < 0 ? [p1, p0] : [p0, p1];\n  }\n\n  function flush(fragmentByEnd, fragmentByStart) {\n    for (var k in fragmentByEnd) {\n      var f = fragmentByEnd[k];\n      delete fragmentByStart[f.start];\n      delete f.start;\n      delete f.end;\n      f.forEach(function(i) { stitchedArcs[i < 0 ? ~i : i] = 1; });\n      fragments.push(f);\n    }\n  }\n\n  flush(fragmentByEnd, fragmentByStart);\n  flush(fragmentByStart, fragmentByEnd);\n  arcs.forEach(function(i) { if (!stitchedArcs[i < 0 ? ~i : i]) fragments.push([i]); });\n\n  return fragments;\n}\n", "import {object} from \"./feature.js\";\nimport stitch from \"./stitch.js\";\n\nexport default function(topology) {\n  return object(topology, meshArcs.apply(this, arguments));\n}\n\nexport function meshArcs(topology, object, filter) {\n  var arcs, i, n;\n  if (arguments.length > 1) arcs = extractArcs(topology, object, filter);\n  else for (i = 0, arcs = new Array(n = topology.arcs.length); i < n; ++i) arcs[i] = i;\n  return {type: \"MultiLineString\", arcs: stitch(topology, arcs)};\n}\n\nfunction extractArcs(topology, object, filter) {\n  var arcs = [],\n      geomsByArc = [],\n      geom;\n\n  function extract0(i) {\n    var j = i < 0 ? ~i : i;\n    (geomsByArc[j] || (geomsByArc[j] = [])).push({i: i, g: geom});\n  }\n\n  function extract1(arcs) {\n    arcs.forEach(extract0);\n  }\n\n  function extract2(arcs) {\n    arcs.forEach(extract1);\n  }\n\n  function extract3(arcs) {\n    arcs.forEach(extract2);\n  }\n\n  function geometry(o) {\n    switch (geom = o, o.type) {\n      case \"GeometryCollection\": o.geometries.forEach(geometry); break;\n      case \"LineString\": extract1(o.arcs); break;\n      case \"MultiLineString\": case \"Polygon\": extract2(o.arcs); break;\n      case \"MultiPolygon\": extract3(o.arcs); break;\n    }\n  }\n\n  geometry(object);\n\n  geomsByArc.forEach(filter == null\n      ? function(geoms) { arcs.push(geoms[0].i); }\n      : function(geoms) { if (filter(geoms[0].g, geoms[geoms.length - 1].g)) arcs.push(geoms[0].i); });\n\n  return arcs;\n}\n", "var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n", "export var xhtml = \"http://www.w3.org/1999/xhtml\";\n\nexport default {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml: xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\n", "import namespaces from \"./namespaces.js\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name; // eslint-disable-line no-prototype-builtins\n}\n", "import namespace from \"./namespace.js\";\nimport {xhtml} from \"./namespaces.js\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n", "function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport selector from \"../selector.js\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "export default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n", "function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\n\nfunction arrayAll(select) {\n  return function() {\n    var group = select.apply(this, arguments);\n    return group == null ? [] : array(group);\n  };\n}\n\nexport default function(select) {\n  if (typeof select === \"function\") select = arrayAll(select);\n  else select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n", "export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n\nexport function childMatcher(selector) {\n  return function(node) {\n    return node.matches(selector);\n  };\n}\n\n", "import {childMatcher} from \"../matcher.js\";\n\nvar find = Array.prototype.find;\n\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\n\nfunction childFirst() {\n  return this.firstElementChild;\n}\n\nexport default function(match) {\n  return this.select(match == null ? childFirst\n      : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {childMatcher} from \"../matcher.js\";\n\nvar filter = Array.prototype.filter;\n\nfunction children() {\n  return this.children;\n}\n\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\n\nexport default function(match) {\n  return this.selectAll(match == null ? children\n      : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {Selection} from \"./index.js\";\nimport matcher from \"../matcher.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "export default function(update) {\n  return new Array(update.length);\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport {EnterNode} from \"./enter.js\";\nimport array from \"../array.js\";\nimport constant from \"../constant.js\";\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = new Map,\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue.get(keyValues[i]) === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction datum(node) {\n  return node.__data__;\n}\n\nexport default function(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = array(value.call(parent, parent && parent.__data__, j, parents)),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n", "export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  enter = typeof onenter === \"function\" ? onenter(enter) : enter.append(onenter + \"\");\n  if (onupdate != null) update = onupdate(update);\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(selection) {\n  if (!(selection instanceof Selection)) throw new Error(\"invalid merge\");\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n", "export default function() {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}\n", "export default function() {\n  return !this.node();\n}\n", "export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n", "import namespace from \"../namespace.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n", "export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n", "import defaultView from \"../window.js\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n", "function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n", "function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n", "function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n", "function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n", "function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n", "function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n", "import creator from \"../creator.js\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n", "import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n", "function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n", "function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n", "export default function(value) {\n  return arguments.length\n      ? this.property(\"__data__\", value)\n      : this.node().__data__;\n}\n", "function contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, options: options};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\n", "import defaultView from \"../window.js\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n", "export default function*() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\n", "import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nfunction selection_selection() {\n  return this;\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\n\nexport default selection;\n", "import {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n", "var nextId = 0;\n\nexport default function local() {\n  return new Local;\n}\n\nfunction Local() {\n  this._ = \"@\" + (++nextId).toString(36);\n}\n\nLocal.prototype = local.prototype = {\n  constructor: Local,\n  get: function(node) {\n    var id = this._;\n    while (!(id in node)) if (!(node = node.parentNode)) return;\n    return node[id];\n  },\n  set: function(node, value) {\n    return node[this._] = value;\n  },\n  remove: function(node) {\n    return this._ in node && delete node[this._];\n  },\n  toString: function() {\n    return this._;\n  }\n};\n", "export default function(event) {\n  let sourceEvent;\n  while (sourceEvent = event.sourceEvent) event = sourceEvent;\n  return event;\n}\n", "import sourceEvent from \"./sourceEvent.js\";\n\nexport default function(event, node) {\n  event = sourceEvent(event);\n  if (node === undefined) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\n", "export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {select} from \"d3-selection\";\nimport noevent from \"./noevent.js\";\n\nexport default function(view) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", noevent, true);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, true);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\n\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, true);\n    setTimeout(function() { selection.on(\"click.drag\", null); }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}\n", "export default function DragEvent(type, {\n  sourceEvent,\n  subject,\n  target,\n  identifier,\n  active,\n  x, y, dx, dy,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    subject: {value: subject, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    identifier: {value: identifier, enumerable: true, configurable: true},\n    active: {value: active, enumerable: true, configurable: true},\n    x: {value: x, enumerable: true, configurable: true},\n    y: {value: y, enumerable: true, configurable: true},\n    dx: {value: dx, enumerable: true, configurable: true},\n    dy: {value: dy, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\n", "var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(null, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n", "import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer, timeout} from \"d3-timer\";\n\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\n\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\n\nexport default function(node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index, // For context during callback.\n    group: group, // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\n\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\n\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\n\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\n\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n      tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n        i = -1,\n        n = tween.length;\n\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}\n", "import {STARTING, ENDING, ENDED} from \"./transition/schedule.js\";\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      active,\n      empty = true,\n      i;\n\n  if (!schedules) return;\n\n  name = name == null ? null : name + \"\";\n\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) { empty = false; continue; }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n\n  if (empty) delete node.__transition;\n}\n", "import interrupt from \"../interrupt.js\";\n\nexport default function(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {name: name, value: value}, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nexport default function(name, value) {\n  var id = this._id;\n\n  name += \"\";\n\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\n\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n\n  transition.each(function() {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n\n  return function(node) {\n    return get(node, id).value[name];\n  };\n}\n", "import {color} from \"d3-color\";\nimport {interpolateNumber, interpolateRgb, interpolateString} from \"d3-interpolate\";\n\nexport default function(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber\n      : b instanceof color ? interpolateRgb\n      : (c = color(b)) ? (b = c, interpolateRgb)\n      : interpolateString)(a, b);\n}\n", "import {interpolateTransformSvg as interpolateTransform} from \"d3-interpolate\";\nimport {namespace} from \"d3-selection\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value))\n      : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname)\n      : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\n", "import {namespace} from \"d3-selection\";\n\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\n\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\n\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\n", "import {get, init} from \"./schedule.js\";\n\nfunction delayFunction(id, value) {\n  return function() {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\n\nfunction delayConstant(id, value) {\n  return value = +value, function() {\n    init(this, id).delay = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? delayFunction\n          : delayConstant)(id, value))\n      : get(this.node(), id).delay;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction durationFunction(id, value) {\n  return function() {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\n\nfunction durationConstant(id, value) {\n  return value = +value, function() {\n    set(this, id).duration = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? durationFunction\n          : durationConstant)(id, value))\n      : get(this.node(), id).duration;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    set(this, id).ease = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each(easeConstant(id, value))\n      : get(this.node(), id).ease;\n}\n", "import {set} from \"./schedule.js\";\n\nfunction easeVarying(id, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error;\n    set(this, id).ease = v;\n  };\n}\n\nexport default function(value) {\n  if (typeof value !== \"function\") throw new Error;\n  return this.each(easeVarying(this._id, value));\n}\n", "import {matcher} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\n", "import {Transition} from \"./index.js\";\n\nexport default function(transition) {\n  if (transition._id !== this._id) throw new Error;\n\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Transition(merges, this._parents, this._name, this._id);\n}\n", "import {get, set, init} from \"./schedule.js\";\n\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\n\nfunction onFunction(id, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule = sit(this, id),\n        on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, listener) {\n  var id = this._id;\n\n  return arguments.length < 2\n      ? get(this.node(), id).on.on(name)\n      : this.each(onFunction(id, name, listener));\n}\n", "function removeFunction(id) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\n\nexport default function() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\n", "import {selector} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, name, id);\n}\n", "import {selectorAll} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\n\nvar Selection = selection.prototype.constructor;\n\nexport default function() {\n  return new Selection(this._groups, this._parents);\n}\n", "import {interpolateTransformCss as interpolateTransform} from \"d3-interpolate\";\nimport {style} from \"d3-selection\";\nimport {set} from \"./schedule.js\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction styleNull(name, interpolate) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = style(this, name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction styleFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        value1 = value(this),\n        string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction styleMaybeRemove(id, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove;\n  return function() {\n    var schedule = set(this, id),\n        on = schedule.on,\n        listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this\n      .styleTween(name, styleNull(name, i))\n      .on(\"end.style.\" + name, styleRemove(name))\n    : typeof value === \"function\" ? this\n      .styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value)))\n      .each(styleMaybeRemove(this._id, name))\n    : this\n      .styleTween(name, styleConstant(name, i, value), priority)\n      .on(\"end.style.\" + name, null);\n}\n", "function styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\n\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\n", "import {tweenValue} from \"./tween.js\";\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\n\nexport default function(value) {\n  return this.tween(\"text\", typeof value === \"function\"\n      ? textFunction(tweenValue(this, \"text\", value))\n      : textConstant(value == null ? \"\" : value + \"\"));\n}\n", "function textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\n\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, textTween(value));\n}\n", "import {Transition, newId} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function() {\n  var name = this._name,\n      id0 = this._id,\n      id1 = newId();\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id1);\n}\n", "import {set} from \"./schedule.js\";\n\nexport default function() {\n  var on0, on1, that = this, id = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = {value: reject},\n        end = {value: function() { if (--size === 0) resolve(); }};\n\n    that.each(function() {\n      var schedule = set(this, id),\n          on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}\n", "import {selection} from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\n\nvar id = 0;\n\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\n\nexport default function transition(name) {\n  return selection().transition(name);\n}\n\nexport function newId() {\n  return ++id;\n}\n\nvar selection_prototype = selection.prototype;\n\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\n", "export function cubicIn(t) {\n  return t * t * t;\n}\n\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\n\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\n", "var exponent = 3;\n\nexport var polyIn = (function custom(e) {\n  e = +e;\n\n  function polyIn(t) {\n    return Math.pow(t, e);\n  }\n\n  polyIn.exponent = custom;\n\n  return polyIn;\n})(exponent);\n\nexport var polyOut = (function custom(e) {\n  e = +e;\n\n  function polyOut(t) {\n    return 1 - Math.pow(1 - t, e);\n  }\n\n  polyOut.exponent = custom;\n\n  return polyOut;\n})(exponent);\n\nexport var polyInOut = (function custom(e) {\n  e = +e;\n\n  function polyInOut(t) {\n    return ((t *= 2) <= 1 ? Math.pow(t, e) : 2 - Math.pow(2 - t, e)) / 2;\n  }\n\n  polyInOut.exponent = custom;\n\n  return polyInOut;\n})(exponent);\n", "var pi = Math.PI,\n    halfPi = pi / 2;\n\nexport function sinIn(t) {\n  return (+t === 1) ? 1 : 1 - Math.cos(t * halfPi);\n}\n\nexport function sinOut(t) {\n  return Math.sin(t * halfPi);\n}\n\nexport function sinInOut(t) {\n  return (1 - Math.cos(pi * t)) / 2;\n}\n", "// tpmt is two power minus ten times t scaled to [0,1]\nexport function tpmt(x) {\n  return (Math.pow(2, -10 * x) - 0.0009765625) * 1.0009775171065494;\n}\n", "var b1 = 4 / 11,\n    b2 = 6 / 11,\n    b3 = 8 / 11,\n    b4 = 3 / 4,\n    b5 = 9 / 11,\n    b6 = 10 / 11,\n    b7 = 15 / 16,\n    b8 = 21 / 22,\n    b9 = 63 / 64,\n    b0 = 1 / b1 / b1;\n\nexport function bounceIn(t) {\n  return 1 - bounceOut(1 - t);\n}\n\nexport function bounceOut(t) {\n  return (t = +t) < b1 ? b0 * t * t : t < b3 ? b0 * (t -= b2) * t + b4 : t < b6 ? b0 * (t -= b5) * t + b7 : b0 * (t -= b8) * t + b9;\n}\n\nexport function bounceInOut(t) {\n  return ((t *= 2) <= 1 ? 1 - bounceOut(1 - t) : bounceOut(t - 1) + 1) / 2;\n}\n", "var overshoot = 1.70158;\n\nexport var backIn = (function custom(s) {\n  s = +s;\n\n  function backIn(t) {\n    return (t = +t) * t * (s * (t - 1) + t);\n  }\n\n  backIn.overshoot = custom;\n\n  return backIn;\n})(overshoot);\n\nexport var backOut = (function custom(s) {\n  s = +s;\n\n  function backOut(t) {\n    return --t * t * ((t + 1) * s + t) + 1;\n  }\n\n  backOut.overshoot = custom;\n\n  return backOut;\n})(overshoot);\n\nexport var backInOut = (function custom(s) {\n  s = +s;\n\n  function backInOut(t) {\n    return ((t *= 2) < 1 ? t * t * ((s + 1) * t - s) : (t -= 2) * t * ((s + 1) * t + s) + 2) / 2;\n  }\n\n  backInOut.overshoot = custom;\n\n  return backInOut;\n})(overshoot);\n", "import {tpmt} from \"./math.js\";\n\nvar tau = 2 * Math.PI,\n    amplitude = 1,\n    period = 0.3;\n\nexport var elasticIn = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticIn(t) {\n    return a * tpmt(-(--t)) * Math.sin((s - t) / p);\n  }\n\n  elasticIn.amplitude = function(a) { return custom(a, p * tau); };\n  elasticIn.period = function(p) { return custom(a, p); };\n\n  return elasticIn;\n})(amplitude, period);\n\nexport var elasticOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticOut(t) {\n    return 1 - a * tpmt(t = +t) * Math.sin((t + s) / p);\n  }\n\n  elasticOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticOut.period = function(p) { return custom(a, p); };\n\n  return elasticOut;\n})(amplitude, period);\n\nexport var elasticInOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticInOut(t) {\n    return ((t = t * 2 - 1) < 0\n        ? a * tpmt(-t) * Math.sin((s - t) / p)\n        : 2 - a * tpmt(t) * Math.sin((s + t) / p)) / 2;\n  }\n\n  elasticInOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticInOut.period = function(p) { return custom(a, p); };\n\n  return elasticInOut;\n})(amplitude, period);\n", "import {Transition, newId} from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport {easeCubicInOut} from \"d3-ease\";\nimport {now} from \"d3-timer\";\n\nvar defaultTiming = {\n  time: null, // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\n\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\n\nexport default function(name) {\n  var id,\n      timing;\n\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\n\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\n", "export default x => () => x;\n", "export default function ZoomEvent(type, {\n  sourceEvent,\n  target,\n  transform,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    transform: {value: transform, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n", "export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\n\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\n\nexport var identity = new Transform(1, 0, 0);\n\ntransform.prototype = Transform.prototype;\n\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}\n", "export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolateZoom} from \"d3-interpolate\";\nimport {select, pointer} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport ZoomEvent from \"./event.js\";\nimport {Transform, identity} from \"./transform.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\n// Ignore right-click, since that should open the context menu.\n// except for pinch-to-zoom, which is sent as a wheel+ctrlKey event\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === 'wheel') && !event.button;\n}\n\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\n\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\n\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * (event.ctrlKey ? 10 : 1);\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0],\n      dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0],\n      dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1],\n      dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(\n    dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1),\n    dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1)\n  );\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      extent = defaultExtent,\n      constrain = defaultConstrain,\n      wheelDelta = defaultWheelDelta,\n      touchable = defaultTouchable,\n      scaleExtent = [0, Infinity],\n      translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]],\n      duration = 250,\n      interpolate = interpolateZoom,\n      listeners = dispatch(\"start\", \"zoom\", \"end\"),\n      touchstarting,\n      touchfirst,\n      touchending,\n      touchDelay = 500,\n      wheelDelay = 150,\n      clickDistance2 = 0,\n      tapDistance = 10;\n\n  function zoom(selection) {\n    selection\n        .property(\"__zoom\", defaultTransform)\n        .on(\"wheel.zoom\", wheeled)\n        .on(\"mousedown.zoom\", mousedowned)\n        .on(\"dblclick.zoom\", dblclicked)\n      .filter(touchable)\n        .on(\"touchstart.zoom\", touchstarted)\n        .on(\"touchmove.zoom\", touchmoved)\n        .on(\"touchend.zoom touchcancel.zoom\", touchended)\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  zoom.transform = function(collection, transform, point, event) {\n    var selection = collection.selection ? collection.selection() : collection;\n    selection.property(\"__zoom\", defaultTransform);\n    if (collection !== selection) {\n      schedule(collection, transform, point, event);\n    } else {\n      selection.interrupt().each(function() {\n        gesture(this, arguments)\n          .event(event)\n          .start()\n          .zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform)\n          .end();\n      });\n    }\n  };\n\n  zoom.scaleBy = function(selection, k, p, event) {\n    zoom.scaleTo(selection, function() {\n      var k0 = this.__zoom.k,\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n\n  zoom.scaleTo = function(selection, k, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t0 = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p,\n          p1 = t0.invert(p0),\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n\n  zoom.translateBy = function(selection, x, y, event) {\n    zoom.transform(selection, function() {\n      return constrain(this.__zoom.translate(\n        typeof x === \"function\" ? x.apply(this, arguments) : x,\n        typeof y === \"function\" ? y.apply(this, arguments) : y\n      ), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n\n  zoom.translateTo = function(selection, x, y, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(\n        typeof x === \"function\" ? -x.apply(this, arguments) : -x,\n        typeof y === \"function\" ? -y.apply(this, arguments) : -y\n      ), e, translateExtent);\n    }, p, event);\n  };\n\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k, y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n\n  function centroid(extent) {\n    return [(+extent[0][0] + +extent[1][0]) / 2, (+extent[0][1] + +extent[1][1]) / 2];\n  }\n\n  function schedule(transition, transform, point, event) {\n    transition\n        .on(\"start.zoom\", function() { gesture(this, arguments).event(event).start(); })\n        .on(\"interrupt.zoom end.zoom\", function() { gesture(this, arguments).event(event).end(); })\n        .tween(\"zoom\", function() {\n          var that = this,\n              args = arguments,\n              g = gesture(that, args).event(event),\n              e = extent.apply(that, args),\n              p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point,\n              w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]),\n              a = that.__zoom,\n              b = typeof transform === \"function\" ? transform.apply(that, args) : transform,\n              i = interpolate(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n          return function(t) {\n            if (t === 1) t = b; // Avoid rounding error on end.\n            else { var l = i(t), k = w / l[2]; t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k); }\n            g.zoom(null, t);\n          };\n        });\n  }\n\n  function gesture(that, args, clean) {\n    return (!clean && that.__zooming) || new Gesture(that, args);\n  }\n\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n\n  Gesture.prototype = {\n    event: function(event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function() {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function(key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function(type) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new ZoomEvent(type, {\n          sourceEvent: this.sourceEvent,\n          target: zoom,\n          type,\n          transform: this.that.__zoom,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function wheeled(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event),\n        t = this.__zoom,\n        k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta.apply(this, arguments)))),\n        p = pointer(event);\n\n    // If the mouse is in the same location as before, reuse it.\n    // If there were recent wheel events, reset the wheel idle timeout.\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    }\n\n    // If this wheel event won’t trigger a transform change, ignore it.\n    else if (t.k === k) return;\n\n    // Otherwise, capture the mouse point and location at the start.\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n\n  function mousedowned(event, ...args) {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var g = gesture(this, args, true).event(event),\n        v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true),\n        p = pointer(event, currentTarget),\n        currentTarget = event.currentTarget,\n        x0 = event.clientX,\n        y0 = event.clientY;\n\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n\n    function mousemoved(event) {\n      noevent(event);\n      if (!g.moved) {\n        var dx = event.clientX - x0, dy = event.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event)\n       .zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n\n    function mouseupped(event) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      dragEnable(event.view, g.moved);\n      noevent(event);\n      g.event(event).end();\n    }\n  }\n\n  function dblclicked(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var t0 = this.__zoom,\n        p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this),\n        p1 = t0.invert(p0),\n        k1 = t0.k * (event.shiftKey ? 0.5 : 2),\n        t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule, t1, p0, event);\n    else select(this).call(zoom.transform, t1, p0, event);\n  }\n\n  function touchstarted(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.touches,\n        n = touches.length,\n        g = gesture(this, args, event.changedTouches.length === n).event(event),\n        started, i, t, p;\n\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;\n      else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function() { touchstarting = null; }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t, p, l;\n\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;\n      else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0], l0 = g.touch0[1],\n          p1 = g.touch1[0], l1 = g.touch1[1],\n          dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp,\n          dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    }\n    else if (g.touch0) p = g.touch0[0], l = g.touch0[1];\n    else return;\n\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t;\n\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;\n      else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);\n    else {\n      g.end();\n      // If this was a dbltap, reroute to the (optional) dblclick.zoom handler.\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n\n  zoom.wheelDelta = function(_) {\n    return arguments.length ? (wheelDelta = typeof _ === \"function\" ? _ : constant(+_), zoom) : wheelDelta;\n  };\n\n  zoom.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), zoom) : filter;\n  };\n\n  zoom.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom) : touchable;\n  };\n\n  zoom.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom) : extent;\n  };\n\n  zoom.scaleExtent = function(_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom) : [scaleExtent[0], scaleExtent[1]];\n  };\n\n  zoom.translateExtent = function(_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n\n  zoom.constrain = function(_) {\n    return arguments.length ? (constrain = _, zoom) : constrain;\n  };\n\n  zoom.duration = function(_) {\n    return arguments.length ? (duration = +_, zoom) : duration;\n  };\n\n  zoom.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, zoom) : interpolate;\n  };\n\n  zoom.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom : value;\n  };\n\n  zoom.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom) : Math.sqrt(clickDistance2);\n  };\n\n  zoom.tapDistance = function(_) {\n    return arguments.length ? (tapDistance = +_, zoom) : tapDistance;\n  };\n\n  return zoom;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAgI;AAChI,wBAAsB;;;ACDtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,KAAK,KAAK;AACd,IAAI,SAAS,KAAK;AAClB,IAAI,YAAY,KAAK;AACrB,IAAI,MAAM,KAAK;AAEf,IAAI,UAAU,MAAM;AACpB,IAAI,UAAU,KAAK;AAEnB,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK;AAChB,IAAI,QAAQ,KAAK;AACjB,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK;AAEf,IAAI,QAAQ,KAAK;AACjB,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK,QAAQ,SAAS,GAAG;AAAE,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAAG;AACzE,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK;AAEf,SAAS,KAAK,GAAG;AACtB,SAAO,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC;AAC9C;AAEO,SAAS,KAAK,GAAG;AACtB,SAAO,IAAI,IAAI,SAAS,IAAI,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC;AACxD;AAEO,SAAS,SAAS,GAAG;AAC1B,UAAQ,IAAI,IAAI,IAAI,CAAC,KAAK;AAC5B;;;ACnCe,SAAR,OAAwB;AAAC;;;ACAhC,SAAS,eAAe,UAAU,QAAQ;AACxC,MAAI,YAAY,mBAAmB,eAAe,SAAS,IAAI,GAAG;AAChE,uBAAmB,SAAS,IAAI,EAAE,UAAU,MAAM;AAAA,EACpD;AACF;AAEA,IAAI,mBAAmB;AAAA,EACrB,SAAS,SAASC,SAAQ,QAAQ;AAChC,mBAAeA,QAAO,UAAU,MAAM;AAAA,EACxC;AAAA,EACA,mBAAmB,SAASA,SAAQ,QAAQ;AAC1C,QAAI,WAAWA,QAAO,UAAU,IAAI,IAAI,IAAI,SAAS;AACrD,WAAO,EAAE,IAAI;AAAG,qBAAe,SAAS,CAAC,EAAE,UAAU,MAAM;AAAA,EAC7D;AACF;AAEA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,SAASA,SAAQ,QAAQ;AAC/B,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,OAAO,SAASA,SAAQ,QAAQ;AAC9B,IAAAA,UAASA,QAAO;AAChB,WAAO,MAAMA,QAAO,CAAC,GAAGA,QAAO,CAAC,GAAGA,QAAO,CAAC,CAAC;AAAA,EAC9C;AAAA,EACA,YAAY,SAASA,SAAQ,QAAQ;AACnC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI;AAAG,MAAAD,UAASC,aAAY,CAAC,GAAG,OAAO,MAAMD,QAAO,CAAC,GAAGA,QAAO,CAAC,GAAGA,QAAO,CAAC,CAAC;AAAA,EACvF;AAAA,EACA,YAAY,SAASA,SAAQ,QAAQ;AACnC,eAAWA,QAAO,aAAa,QAAQ,CAAC;AAAA,EAC1C;AAAA,EACA,iBAAiB,SAASA,SAAQ,QAAQ;AACxC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI;AAAG,iBAAWA,aAAY,CAAC,GAAG,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,SAAS,SAASD,SAAQ,QAAQ;AAChC,kBAAcA,QAAO,aAAa,MAAM;AAAA,EAC1C;AAAA,EACA,cAAc,SAASA,SAAQ,QAAQ;AACrC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI;AAAG,oBAAcA,aAAY,CAAC,GAAG,MAAM;AAAA,EACtD;AAAA,EACA,oBAAoB,SAASD,SAAQ,QAAQ;AAC3C,QAAI,aAAaA,QAAO,YAAY,IAAI,IAAI,IAAI,WAAW;AAC3D,WAAO,EAAE,IAAI;AAAG,qBAAe,WAAW,CAAC,GAAG,MAAM;AAAA,EACtD;AACF;AAEA,SAAS,WAAWC,cAAa,QAAQ,QAAQ;AAC/C,MAAI,IAAI,IAAI,IAAIA,aAAY,SAAS,QAAQ;AAC7C,SAAO,UAAU;AACjB,SAAO,EAAE,IAAI;AAAG,iBAAaA,aAAY,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AACrG,SAAO,QAAQ;AACjB;AAEA,SAAS,cAAcA,cAAa,QAAQ;AAC1C,MAAI,IAAI,IAAI,IAAIA,aAAY;AAC5B,SAAO,aAAa;AACpB,SAAO,EAAE,IAAI;AAAG,eAAWA,aAAY,CAAC,GAAG,QAAQ,CAAC;AACpD,SAAO,WAAW;AACpB;AAEe,SAAR,eAAiBD,SAAQ,QAAQ;AACtC,MAAIA,WAAU,iBAAiB,eAAeA,QAAO,IAAI,GAAG;AAC1D,qBAAiBA,QAAO,IAAI,EAAEA,SAAQ,MAAM;AAAA,EAC9C,OAAO;AACL,mBAAeA,SAAQ,MAAM;AAAA,EAC/B;AACF;;;AC/DO,IAAI,cAAc,IAAI,MAAM;AAInC,IAAI,UAAU,IAAI,MAAM;AAAxB,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AAEG,IAAI,aAAa;AAAA,EACtB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,kBAAc,IAAI,MAAM;AACxB,eAAW,YAAY;AACvB,eAAW,UAAU;AAAA,EACvB;AAAA,EACA,YAAY,WAAW;AACrB,QAAI,WAAW,CAAC;AAChB,YAAQ,IAAI,WAAW,IAAI,MAAM,WAAW,QAAQ;AACpD,SAAK,YAAY,KAAK,UAAU,KAAK,QAAQ;AAAA,EAC/C;AAAA,EACA,QAAQ,WAAW;AACjB,YAAQ,IAAI,GAAG;AAAA,EACjB;AACF;AAEA,SAAS,gBAAgB;AACvB,aAAW,QAAQ;AACrB;AAEA,SAAS,cAAc;AACrB,YAAU,UAAU,KAAK;AAC3B;AAEA,SAAS,eAAe,QAAQ,KAAK;AACnC,aAAW,QAAQ;AACnB,aAAW,QAAQ,QAAQ;AAC3B,YAAU,SAAS,OAAO;AAC1B,YAAU,QAAQ,UAAU,IAAI,MAAM,MAAM,IAAI,SAAS,GAAG,UAAU,IAAI,GAAG;AAC/E;AAEA,SAAS,UAAU,QAAQ,KAAK;AAC9B,YAAU,SAAS,OAAO;AAC1B,QAAM,MAAM,IAAI;AAKhB,MAAI,UAAU,SAAS,SACnB,WAAW,WAAW,IAAI,IAAI,IAC9B,WAAW,WAAW,SACtB,SAAS,IAAI,GAAG,GAChB,SAAS,IAAI,GAAG,GAChB,IAAI,UAAU,QACd,IAAI,UAAU,SAAS,IAAI,IAAI,QAAQ,GACvC,IAAI,IAAI,WAAW,IAAI,QAAQ;AACnC,cAAY,IAAI,MAAM,GAAG,CAAC,CAAC;AAG3B,YAAU,QAAQ,UAAU,QAAQ,UAAU;AAChD;AAEe,SAAR,aAAiBE,SAAQ;AAC9B,YAAU,IAAI,MAAM;AACpB,iBAAOA,SAAQ,UAAU;AACzB,SAAO,UAAU;AACnB;;;ACzEO,SAAS,UAAUC,YAAW;AACnC,SAAO,CAAC,MAAMA,WAAU,CAAC,GAAGA,WAAU,CAAC,CAAC,GAAG,KAAKA,WAAU,CAAC,CAAC,CAAC;AAC/D;AAEO,SAAS,UAAUC,YAAW;AACnC,MAAI,SAASA,WAAU,CAAC,GAAG,MAAMA,WAAU,CAAC,GAAG,SAAS,IAAI,GAAG;AAC/D,SAAO,CAAC,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;AAC9D;AAEO,SAAS,aAAa,GAAG,GAAG;AACjC,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/C;AAEO,SAAS,eAAe,GAAG,GAAG;AACnC,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACzF;AAGO,SAAS,oBAAoB,GAAG,GAAG;AACxC,IAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACzC;AAEO,SAAS,eAAe,QAAQ,GAAG;AACxC,SAAO,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACrD;AAGO,SAAS,0BAA0B,GAAG;AAC3C,MAAI,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACpD,IAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK;AAChC;;;AC1BA,IAAIC;AAAJ,IAAa;AAAb,IAAmB;AAAnB,IAA4B;AAA5B,IACI;AADJ,IAEIC;AAFJ,IAEcC;AAFd,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMI;AAEJ,IAAI,eAAe;AAAA,EACjB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,iBAAa,QAAQ;AACrB,iBAAa,YAAY;AACzB,iBAAa,UAAU;AACvB,eAAW,IAAI,MAAM;AACrB,eAAW,aAAa;AAAA,EAC1B;AAAA,EACA,YAAY,WAAW;AACrB,eAAW,WAAW;AACtB,iBAAa,QAAQ;AACrB,iBAAa,YAAY;AACzB,iBAAa,UAAU;AACvB,QAAI,cAAc;AAAG,MAAAF,WAAU,EAAE,UAAU,MAAM,OAAO,EAAE,OAAO;AAAA,aACxD,WAAW;AAAS,aAAO;AAAA,aAC3B,WAAW,CAAC;AAAS,aAAO;AACrC,UAAM,CAAC,IAAIA,UAAS,MAAM,CAAC,IAAI;AAAA,EACjC;AAAA,EACA,QAAQ,WAAW;AACjB,IAAAA,WAAU,EAAE,UAAU,MAAM,OAAO,EAAE,OAAO;AAAA,EAC9C;AACF;AAEA,SAAS,YAAY,QAAQ,KAAK;AAChC,SAAO,KAAK,QAAQ,CAACA,WAAU,QAAQ,UAAU,MAAM,CAAC;AACxD,MAAI,MAAM;AAAM,WAAO;AACvB,MAAI,MAAM;AAAM,WAAO;AACzB;AAEA,SAAS,UAAU,QAAQ,KAAK;AAC9B,MAAI,IAAI,UAAU,CAAC,SAAS,SAAS,MAAM,OAAO,CAAC;AACnD,MAAI,IAAI;AACN,QAAI,SAAS,eAAe,IAAI,CAAC,GAC7B,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GACtC,aAAa,eAAe,YAAY,MAAM;AAClD,8BAA0B,UAAU;AACpC,iBAAa,UAAU,UAAU;AACjC,QAAI,QAAQ,SAAS,SACjBG,QAAO,QAAQ,IAAI,IAAI,IACvB,UAAU,WAAW,CAAC,IAAI,UAAUA,OACpC,MACA,eAAe,IAAI,KAAK,IAAI;AAChC,QAAI,gBAAgBA,QAAO,UAAU,WAAW,UAAUA,QAAO,SAAS;AACxE,aAAO,WAAW,CAAC,IAAI;AACvB,UAAI,OAAO;AAAM,eAAO;AAAA,IAC1B,WAAW,WAAW,UAAU,OAAO,MAAM,KAAK,gBAAgBA,QAAO,UAAU,WAAW,UAAUA,QAAO,SAAS;AACtH,aAAO,CAAC,WAAW,CAAC,IAAI;AACxB,UAAI,OAAO;AAAM,eAAO;AAAA,IAC1B,OAAO;AACL,UAAI,MAAM;AAAM,eAAO;AACvB,UAAI,MAAM;AAAM,eAAO;AAAA,IACzB;AACA,QAAI,cAAc;AAChB,UAAI,SAAS,SAAS;AACpB,YAAI,MAAMH,UAAS,MAAM,IAAI,MAAMA,UAAS,OAAO;AAAG,oBAAU;AAAA,MAClE,OAAO;AACL,YAAI,MAAM,QAAQ,OAAO,IAAI,MAAMA,UAAS,OAAO;AAAG,UAAAA,WAAU;AAAA,MAClE;AAAA,IACF,OAAO;AACL,UAAI,WAAWA,UAAS;AACtB,YAAI,SAASA;AAAS,UAAAA,WAAU;AAChC,YAAI,SAAS;AAAS,oBAAU;AAAA,MAClC,OAAO;AACL,YAAI,SAAS,SAAS;AACpB,cAAI,MAAMA,UAAS,MAAM,IAAI,MAAMA,UAAS,OAAO;AAAG,sBAAU;AAAA,QAClE,OAAO;AACL,cAAI,MAAM,QAAQ,OAAO,IAAI,MAAMA,UAAS,OAAO;AAAG,YAAAA,WAAU;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,KAAK,QAAQ,CAACA,WAAU,QAAQ,UAAU,MAAM,CAAC;AAAA,EAC1D;AACA,MAAI,MAAM;AAAM,WAAO;AACvB,MAAI,MAAM;AAAM,WAAO;AACvB,OAAK,GAAG,UAAU;AACpB;AAEA,SAAS,kBAAkB;AACzB,eAAa,QAAQ;AACvB;AAEA,SAAS,gBAAgB;AACvB,QAAM,CAAC,IAAIA,UAAS,MAAM,CAAC,IAAI;AAC/B,eAAa,QAAQ;AACrB,OAAK;AACP;AAEA,SAAS,gBAAgB,QAAQ,KAAK;AACpC,MAAI,IAAI;AACN,QAAI,QAAQ,SAAS;AACrB,aAAS,IAAI,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,IAAI,MAAM,QAAQ,KAAK;AAAA,EAC1E,OAAO;AACL,IAAAC,YAAW,QAAQC,SAAQ;AAAA,EAC7B;AACA,aAAW,MAAM,QAAQ,GAAG;AAC5B,YAAU,QAAQ,GAAG;AACvB;AAEA,SAAS,kBAAkB;AACzB,aAAW,UAAU;AACvB;AAEA,SAAS,gBAAgB;AACvB,kBAAgBD,WAAUC,MAAK;AAC/B,aAAW,QAAQ;AACnB,MAAI,IAAI,QAAQ,IAAI;AAAS,IAAAF,WAAU,EAAE,UAAU;AACnD,QAAM,CAAC,IAAIA,UAAS,MAAM,CAAC,IAAI;AAC/B,OAAK;AACP;AAKA,SAAS,MAAMA,UAASI,UAAS;AAC/B,UAAQA,YAAWJ,YAAW,IAAII,WAAU,MAAMA;AACpD;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB;AAEA,SAAS,cAAcC,QAAO,GAAG;AAC/B,SAAOA,OAAM,CAAC,KAAKA,OAAM,CAAC,IAAIA,OAAM,CAAC,KAAK,KAAK,KAAKA,OAAM,CAAC,IAAI,IAAIA,OAAM,CAAC,KAAKA,OAAM,CAAC,IAAI;AAC5F;AAEe,SAAR,eAAiBC,UAAS;AAC/B,MAAI,GAAG,GAAG,GAAG,GAAG,QAAQ,UAAU;AAElC,SAAO,UAAU,EAAEN,WAAU,OAAO;AACpC,WAAS,CAAC;AACV,iBAAOM,UAAS,YAAY;AAG5B,MAAI,IAAI,OAAO,QAAQ;AACrB,WAAO,KAAK,YAAY;AAGxB,SAAK,IAAI,GAAG,IAAI,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACnD,UAAI,OAAO,CAAC;AACZ,UAAI,cAAc,GAAG,EAAE,CAAC,CAAC,KAAK,cAAc,GAAG,EAAE,CAAC,CAAC,GAAG;AACpD,YAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,YAAE,CAAC,IAAI,EAAE,CAAC;AACrD,YAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACvD,OAAO;AACL,eAAO,KAAK,IAAI,CAAC;AAAA,MACnB;AAAA,IACF;AAIA,SAAK,WAAW,WAAW,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1F,UAAI,OAAO,CAAC;AACZ,WAAK,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK;AAAU,mBAAW,OAAON,WAAU,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC;AAAA,IAC7F;AAAA,EACF;AAEA,WAAS,QAAQ;AAEjB,SAAOA,aAAY,YAAY,SAAS,WAClC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,IACvB,CAAC,CAACA,UAAS,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC;AACzC;;;AC7KA,IAAI;AAAJ,IAAQ;AAAR,IACI;AADJ,IACQ;AADR,IACY;AADZ,IAEI;AAFJ,IAEQ;AAFR,IAEY;AAFZ,IAGI;AAHJ,IAGQ;AAHR,IAGY;AAHZ,IAIIO;AAJJ,IAIcC;AAJd,IAKI;AALJ,IAKQ;AALR,IAKY;AAEZ,IAAI,iBAAiB;AAAA,EACnB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,mBAAe,YAAY;AAC3B,mBAAe,UAAU;AAAA,EAC3B;AAAA,EACA,YAAY,WAAW;AACrB,mBAAe,YAAY;AAC3B,mBAAe,UAAU;AAAA,EAC3B;AACF;AAGA,SAAS,cAAc,QAAQ,KAAK;AAClC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG;AACpB,yBAAuB,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;AAC7E;AAEA,SAAS,uBAAuB,GAAG,GAAG,GAAG;AACvC,IAAE;AACF,SAAO,IAAI,MAAM;AACjB,SAAO,IAAI,MAAM;AACjB,SAAO,IAAI,MAAM;AACnB;AAEA,SAAS,oBAAoB;AAC3B,iBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,QAAQ,KAAK;AAC3C,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG;AACpB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,IAAI,GAAG;AACZ,iBAAe,QAAQ;AACvB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB,QAAQ,KAAK;AACtC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,IAAI,GAAG,GACX,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAC/H,QAAM;AACN,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB;AACzB,iBAAe,QAAQ;AACzB;AAIA,SAAS,oBAAoB;AAC3B,iBAAe,QAAQ;AACzB;AAEA,SAAS,kBAAkB;AACzB,oBAAkBD,WAAUC,MAAK;AACjC,iBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,QAAQ,KAAK;AAC3C,EAAAD,YAAW,QAAQC,SAAQ;AAC3B,YAAU,SAAS,OAAO;AAC1B,iBAAe,QAAQ;AACvB,MAAI,SAAS,IAAI,GAAG;AACpB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,IAAI,GAAG;AACZ,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB,QAAQ,KAAK;AACtC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,IAAI,GAAG,GACX,KAAK,KAAK,IAAI,KAAK,GACnB,KAAK,KAAK,IAAI,KAAK,GACnB,KAAK,KAAK,IAAI,KAAK,GACnB,IAAI,MAAM,IAAI,IAAI,EAAE,GACpB,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,CAAC,IAAI;AAClB,KAAG,IAAI,IAAI,EAAE;AACb,KAAG,IAAI,IAAI,EAAE;AACb,KAAG,IAAI,IAAI,EAAE;AACb,QAAM;AACN,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEe,SAAR,iBAAiBC,SAAQ;AAC9B,OAAK,KACL,KAAK,KAAK,KACV,KAAK,KAAK,KAAK;AACf,OAAK,IAAI,MAAM;AACf,OAAK,IAAI,MAAM;AACf,OAAK,IAAI,MAAM;AACf,iBAAOA,SAAQ,cAAc;AAE7B,MAAI,IAAI,CAAC,IACL,IAAI,CAAC,IACL,IAAI,CAAC,IACL,IAAI,MAAM,GAAG,GAAG,CAAC;AAGrB,MAAI,IAAI,UAAU;AAChB,QAAI,IAAI,IAAI,IAAI,IAAI;AAEpB,QAAI,KAAK;AAAS,UAAI,IAAI,IAAI,IAAI,IAAI;AACtC,QAAI,MAAM,GAAG,GAAG,CAAC;AAEjB,QAAI,IAAI;AAAU,aAAO,CAAC,KAAK,GAAG;AAAA,EACpC;AAEA,SAAO,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,OAAO;AACtD;;;AC9Ie,SAAR,iBAAiB,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACJe,SAAR,gBAAiB,GAAG,GAAG;AAE5B,WAAS,QAAQ,GAAG,GAAG;AACrB,WAAO,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EAClC;AAEA,MAAI,EAAE,UAAU,EAAE;AAAQ,YAAQ,SAAS,SAAS,GAAG,GAAG;AACxD,aAAO,IAAI,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACrD;AAEA,SAAO;AACT;;;ACRA,SAAS,iBAAiB,QAAQ,KAAK;AACrC,SAAO,CAAC,IAAI,MAAM,IAAI,KAAK,SAAS,KAAK,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,QAAQ,GAAG;AACnF;AAEA,iBAAiB,SAAS;AAEnB,SAAS,cAAc,aAAa,UAAU,YAAY;AAC/D,UAAQ,eAAe,OAAQ,YAAY,aAAa,gBAAQ,eAAe,WAAW,GAAG,iBAAiB,UAAU,UAAU,CAAC,IAC/H,eAAe,WAAW,IACzB,YAAY,aAAa,iBAAiB,UAAU,UAAU,IAC/D;AACN;AAEA,SAAS,sBAAsB,aAAa;AAC1C,SAAO,SAAS,QAAQ,KAAK;AAC3B,WAAO,UAAU,aAAa,CAAC,SAAS,KAAK,SAAS,MAAM,SAAS,CAAC,KAAK,SAAS,MAAM,QAAQ,GAAG;AAAA,EACvG;AACF;AAEA,SAAS,eAAe,aAAa;AACnC,MAAI,WAAW,sBAAsB,WAAW;AAChD,WAAS,SAAS,sBAAsB,CAAC,WAAW;AACpD,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,YAAY;AAC9C,MAAI,cAAc,IAAI,QAAQ,GAC1B,cAAc,IAAI,QAAQ,GAC1B,gBAAgB,IAAI,UAAU,GAC9B,gBAAgB,IAAI,UAAU;AAElC,WAAS,SAAS,QAAQ,KAAK;AAC7B,QAAI,SAAS,IAAI,GAAG,GAChB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,GAAG,GACX,IAAI,IAAI,cAAc,IAAI;AAC9B,WAAO;AAAA,MACL,MAAM,IAAI,gBAAgB,IAAI,eAAe,IAAI,cAAc,IAAI,WAAW;AAAA,MAC9E,KAAK,IAAI,gBAAgB,IAAI,aAAa;AAAA,IAC5C;AAAA,EACF;AAEA,WAAS,SAAS,SAAS,QAAQ,KAAK;AACtC,QAAI,SAAS,IAAI,GAAG,GAChB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,GAAG,GACX,IAAI,IAAI,gBAAgB,IAAI;AAChC,WAAO;AAAA,MACL,MAAM,IAAI,gBAAgB,IAAI,eAAe,IAAI,cAAc,IAAI,WAAW;AAAA,MAC9E,KAAK,IAAI,cAAc,IAAI,WAAW;AAAA,IACxC;AAAA,EACF;AAEA,SAAO;AACT;AAEe,SAAR,iBAAiB,QAAQ;AAC9B,WAAS,cAAc,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,SAAS,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC;AAE5G,WAAS,QAAQC,cAAa;AAC5B,IAAAA,eAAc,OAAOA,aAAY,CAAC,IAAI,SAASA,aAAY,CAAC,IAAI,OAAO;AACvE,WAAOA,aAAY,CAAC,KAAK,SAASA,aAAY,CAAC,KAAK,SAASA;AAAA,EAC/D;AAEA,UAAQ,SAAS,SAASA,cAAa;AACrC,IAAAA,eAAc,OAAO,OAAOA,aAAY,CAAC,IAAI,SAASA,aAAY,CAAC,IAAI,OAAO;AAC9E,WAAOA,aAAY,CAAC,KAAK,SAASA,aAAY,CAAC,KAAK,SAASA;AAAA,EAC/D;AAEA,SAAO;AACT;;;ACrEO,SAAS,aAAa,QAAQ,QAAQ,OAAO,WAAW,IAAI,IAAI;AACrE,MAAI,CAAC;AAAO;AACZ,MAAI,YAAY,IAAI,MAAM,GACtB,YAAY,IAAI,MAAM,GACtB,OAAO,YAAY;AACvB,MAAI,MAAM,MAAM;AACd,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,OAAO;AAAA,EACvB,OAAO;AACL,SAAK,aAAa,WAAW,EAAE;AAC/B,SAAK,aAAa,WAAW,EAAE;AAC/B,QAAI,YAAY,IAAI,KAAK,KAAK,KAAK;AAAI,YAAM,YAAY;AAAA,EAC3D;AACA,WAAS,OAAO,IAAI,IAAI,YAAY,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAClE,YAAQ,UAAU,CAAC,WAAW,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;AACvE,WAAO,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EACjC;AACF;AAGA,SAAS,aAAa,WAAW,OAAO;AACtC,UAAQ,UAAU,KAAK,GAAG,MAAM,CAAC,KAAK;AACtC,4BAA0B,KAAK;AAC/B,MAAI,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3B,WAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,UAAU,MAAM,WAAW;AAChE;AAEe,SAAR,iBAAmB;AACxB,MAAI,SAAS,iBAAS,CAAC,GAAG,CAAC,CAAC,GACxB,SAAS,iBAAS,EAAE,GACpB,YAAY,iBAAS,CAAC,GACtB,MACA,QACA,SAAS,EAAC,MAAY;AAE1B,WAAS,MAAM,GAAG,GAAG;AACnB,SAAK,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC;AAC1B,MAAE,CAAC,KAAK,SAAS,EAAE,CAAC,KAAK;AAAA,EAC3B;AAEA,WAASC,UAAS;AAChB,QAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,IAAI,OAAO,MAAM,MAAM,SAAS,IAAI,SACpC,IAAI,UAAU,MAAM,MAAM,SAAS,IAAI;AAC3C,WAAO,CAAC;AACR,aAAS,cAAc,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,EAAE;AAC5D,iBAAa,QAAQ,GAAG,GAAG,CAAC;AAC5B,QAAI,EAAC,MAAM,WAAW,aAAa,CAAC,IAAI,EAAC;AACzC,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,EAAAA,QAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGA,WAAU;AAAA,EACxG;AAEA,EAAAA,QAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAGA,WAAU;AAAA,EAC5F;AAEA,EAAAA,QAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAGA,WAAU;AAAA,EAC/F;AAEA,SAAOA;AACT;;;ACrEe,SAAR,iBAAmB;AACxB,MAAI,QAAQ,CAAC,GACT;AACJ,SAAO;AAAA,IACL,OAAO,SAAS,GAAG,GAAG,GAAG;AACvB,WAAK,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IACrB;AAAA,IACA,WAAW,WAAW;AACpB,YAAM,KAAK,OAAO,CAAC,CAAC;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,IACT,QAAQ,WAAW;AACjB,UAAI,MAAM,SAAS;AAAG,cAAM,KAAK,MAAM,IAAI,EAAE,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,IACpE;AAAA,IACA,QAAQ,WAAW;AACjB,UAAI,SAAS;AACb,cAAQ,CAAC;AACT,aAAO;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACrBe,SAAR,mBAAiB,GAAG,GAAG;AAC5B,SAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,WAAW,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;AAC1D;;;ACDA,SAAS,aAAa,OAAO,QAAQ,OAAO,OAAO;AACjD,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI,KAAK,IAAI;AACpB;AAKe,SAAR,eAAiB,UAAUC,sBAAqB,aAAa,aAAa,QAAQ;AACvF,MAAI,UAAU,CAAC,GACX,OAAO,CAAC,GACR,GACA;AAEJ,WAAS,QAAQ,SAAS,SAAS;AACjC,SAAKC,KAAI,QAAQ,SAAS,MAAM;AAAG;AACnC,QAAIA,IAAGC,MAAK,QAAQ,CAAC,GAAG,KAAK,QAAQD,EAAC,GAAG;AAEzC,QAAI,mBAAWC,KAAI,EAAE,GAAG;AACtB,UAAI,CAACA,IAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,eAAO,UAAU;AACjB,aAAK,IAAI,GAAG,IAAID,IAAG,EAAE;AAAG,iBAAO,OAAOC,MAAK,QAAQ,CAAC,GAAG,CAAC,GAAGA,IAAG,CAAC,CAAC;AAChE,eAAO,QAAQ;AACf;AAAA,MACF;AAEA,SAAG,CAAC,KAAK,IAAI;AAAA,IACf;AAEA,YAAQ,KAAK,IAAI,IAAI,aAAaA,KAAI,SAAS,MAAM,IAAI,CAAC;AAC1D,SAAK,KAAK,EAAE,IAAI,IAAI,aAAaA,KAAI,MAAM,GAAG,KAAK,CAAC;AACpD,YAAQ,KAAK,IAAI,IAAI,aAAa,IAAI,SAAS,MAAM,KAAK,CAAC;AAC3D,SAAK,KAAK,EAAE,IAAI,IAAI,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;AAAA,EACrD,CAAC;AAED,MAAI,CAAC,QAAQ;AAAQ;AAErB,OAAK,KAAKF,oBAAmB;AAC7B,OAAK,OAAO;AACZ,OAAK,IAAI;AAET,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AACvC,SAAK,CAAC,EAAE,IAAI,cAAc,CAAC;AAAA,EAC7B;AAEA,MAAIG,SAAQ,QAAQ,CAAC,GACjB,QACA;AAEJ,SAAO,GAAG;AAER,QAAI,UAAUA,QACV,YAAY;AAChB,WAAO,QAAQ;AAAG,WAAK,UAAU,QAAQ,OAAOA;AAAO;AACvD,aAAS,QAAQ;AACjB,WAAO,UAAU;AACjB,OAAG;AACD,cAAQ,IAAI,QAAQ,EAAE,IAAI;AAC1B,UAAI,QAAQ,GAAG;AACb,YAAI,WAAW;AACb,eAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE;AAAG,mBAAO,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,QAC1F,OAAO;AACL,sBAAY,QAAQ,GAAG,QAAQ,EAAE,GAAG,GAAG,MAAM;AAAA,QAC/C;AACA,kBAAU,QAAQ;AAAA,MACpB,OAAO;AACL,YAAI,WAAW;AACb,mBAAS,QAAQ,EAAE;AACnB,eAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,EAAE;AAAG,mBAAO,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,QACxF,OAAO;AACL,sBAAY,QAAQ,GAAG,QAAQ,EAAE,GAAG,IAAI,MAAM;AAAA,QAChD;AACA,kBAAU,QAAQ;AAAA,MACpB;AACA,gBAAU,QAAQ;AAClB,eAAS,QAAQ;AACjB,kBAAY,CAAC;AAAA,IACf,SAAS,CAAC,QAAQ;AAClB,WAAO,QAAQ;AAAA,EACjB;AACF;AAEA,SAAS,KAAK,OAAO;AACnB,MAAI,EAAE,IAAI,MAAM;AAAS;AACzB,MAAI,GACA,IAAI,GACJ,IAAI,MAAM,CAAC,GACX;AACJ,SAAO,EAAE,IAAI,GAAG;AACd,MAAE,IAAI,IAAI,MAAM,CAAC;AACjB,MAAE,IAAI;AACN,QAAI;AAAA,EACN;AACA,IAAE,IAAI,IAAI,MAAM,CAAC;AACjB,IAAE,IAAI;AACR;;;AClGA,SAAS,UAAU,OAAO;AACxB,MAAI,IAAI,MAAM,CAAC,CAAC,KAAK;AACnB,WAAO,MAAM,CAAC;AAAA;AAEd,WAAO,KAAK,MAAM,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,MAAM;AAC1D;AAEe,SAAR,wBAAiB,SAAS,OAAO;AACtC,MAAI,SAAS,UAAU,KAAK,GACxB,MAAM,MAAM,CAAC,GACb,SAAS,IAAI,GAAG,GAChB,SAAS,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,GACtCC,SAAQ,GACR,UAAU;AAEd,MAAI,MAAM,IAAI,MAAM;AAEpB,MAAI,WAAW;AAAG,UAAM,SAAS;AAAA,WACxB,WAAW;AAAI,UAAM,CAAC,SAAS;AAExC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,QAAI,EAAE,KAAK,OAAO,QAAQ,CAAC,GAAG;AAAS;AACvC,QAAI,MACA,GACA,SAAS,KAAK,IAAI,CAAC,GACnBC,WAAU,UAAU,MAAM,GAC1BC,QAAO,OAAO,CAAC,IAAI,IAAI,WACvBC,WAAU,IAAID,KAAI,GAClBE,WAAU,IAAIF,KAAI;AAEtB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGD,WAAUI,UAASF,WAAU,SAASC,WAAU,SAAS,SAAS,QAAQ;AACpG,UAAI,SAAS,KAAK,CAAC,GACfC,WAAU,UAAU,MAAM,GAC1BC,QAAO,OAAO,CAAC,IAAI,IAAI,WACvB,UAAU,IAAIA,KAAI,GAClB,UAAU,IAAIA,KAAI,GAClB,QAAQD,WAAUJ,UAClBM,QAAO,SAAS,IAAI,IAAI,IACxB,WAAWA,QAAO,OAClB,eAAe,WAAW,IAC1B,IAAIJ,WAAU;AAElB,UAAI,IAAI,MAAM,IAAII,QAAO,IAAI,QAAQ,GAAGH,WAAU,UAAU,IAAI,IAAI,QAAQ,CAAC,CAAC;AAC9E,MAAAJ,UAAS,eAAe,QAAQO,QAAO,MAAM;AAI7C,UAAI,eAAeN,YAAW,SAASI,YAAW,QAAQ;AACxD,YAAI,MAAM,eAAe,UAAU,MAAM,GAAG,UAAU,MAAM,CAAC;AAC7D,kCAA0B,GAAG;AAC7B,YAAI,eAAe,eAAe,QAAQ,GAAG;AAC7C,kCAA0B,YAAY;AACtC,YAAI,UAAU,eAAe,SAAS,IAAI,KAAK,KAAK,KAAK,aAAa,CAAC,CAAC;AACxE,YAAI,MAAM,UAAU,QAAQ,WAAW,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI;AACxD,qBAAW,eAAe,SAAS,IAAI,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAaA,UAAQL,SAAQ,CAAC,WAAWA,SAAQ,WAAW,MAAM,CAAC,YAAa,UAAU;AAC/E;;;ACtEe,SAAR,aAAiB,cAAc,UAAU,aAAaQ,QAAO;AAClE,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO,SAAS,IAAI,GACpB,aAAa,eAAW,GACxB,WAAW,SAAS,UAAU,GAC9B,iBAAiB,OACjB,SACA,UACA;AAEJ,QAAI,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,WAAW;AACvB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,mBAAW,CAAC;AACZ,kBAAU,CAAC;AAAA,MACb;AAAA,MACA,YAAY,WAAW;AACrB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,mBAAW,MAAM,QAAQ;AACzB,YAAI,cAAc,wBAAgB,SAASA,MAAK;AAChD,YAAI,SAAS,QAAQ;AACnB,cAAI,CAAC;AAAgB,iBAAK,aAAa,GAAG,iBAAiB;AAC3D,yBAAW,UAAU,qBAAqB,aAAa,aAAa,IAAI;AAAA,QAC1E,WAAW,aAAa;AACtB,cAAI,CAAC;AAAgB,iBAAK,aAAa,GAAG,iBAAiB;AAC3D,eAAK,UAAU;AACf,sBAAY,MAAM,MAAM,GAAG,IAAI;AAC/B,eAAK,QAAQ;AAAA,QACf;AACA,YAAI;AAAgB,eAAK,WAAW,GAAG,iBAAiB;AACxD,mBAAW,UAAU;AAAA,MACvB;AAAA,MACA,QAAQ,WAAW;AACjB,aAAK,aAAa;AAClB,aAAK,UAAU;AACf,oBAAY,MAAM,MAAM,GAAG,IAAI;AAC/B,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,MAAM,QAAQ,KAAK;AAC1B,UAAI,aAAa,QAAQ,GAAG;AAAG,aAAK,MAAM,QAAQ,GAAG;AAAA,IACvD;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,WAAK,MAAM,QAAQ,GAAG;AAAA,IACxB;AAEA,aAAS,YAAY;AACnB,WAAK,QAAQ;AACb,WAAK,UAAU;AAAA,IACjB;AAEA,aAAS,UAAU;AACjB,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACf;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,WAAK,KAAK,CAAC,QAAQ,GAAG,CAAC;AACvB,eAAS,MAAM,QAAQ,GAAG;AAAA,IAC5B;AAEA,aAAS,YAAY;AACnB,eAAS,UAAU;AACnB,aAAO,CAAC;AAAA,IACV;AAEA,aAAS,UAAU;AACjB,gBAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;AAChC,eAAS,QAAQ;AAEjB,UAAI,QAAQ,SAAS,MAAM,GACvB,eAAe,WAAW,OAAO,GACjC,GAAG,IAAI,aAAa,QAAQ,GAC5B,SACAC;AAEJ,WAAK,IAAI;AACT,cAAQ,KAAK,IAAI;AACjB,aAAO;AAEP,UAAI,CAAC;AAAG;AAGR,UAAI,QAAQ,GAAG;AACb,kBAAU,aAAa,CAAC;AACxB,aAAK,IAAI,QAAQ,SAAS,KAAK,GAAG;AAChC,cAAI,CAAC;AAAgB,iBAAK,aAAa,GAAG,iBAAiB;AAC3D,eAAK,UAAU;AACf,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE;AAAG,iBAAK,OAAOA,SAAQ,QAAQ,CAAC,GAAG,CAAC,GAAGA,OAAM,CAAC,CAAC;AACpE,eAAK,QAAQ;AAAA,QACf;AACA;AAAA,MACF;AAIA,UAAI,IAAI,KAAK,QAAQ;AAAG,qBAAa,KAAK,aAAa,IAAI,EAAE,OAAO,aAAa,MAAM,CAAC,CAAC;AAEzF,eAAS,KAAK,aAAa,OAAO,YAAY,CAAC;AAAA,IACjD;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,SAAS;AAC7B,SAAO,QAAQ,SAAS;AAC1B;AAIA,SAAS,oBAAoB,GAAG,GAAG;AACjC,WAAS,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS,UAAU,SAAS,EAAE,CAAC,OACxD,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS,UAAU,SAAS,EAAE,CAAC;AACnE;;;AC/HA,IAAO,uBAAQ;AAAA,EACb,WAAW;AAAE,WAAO;AAAA,EAAM;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,CAAC,CAAC,IAAI,CAAC,MAAM;AACf;AAKA,SAAS,qBAAqB,QAAQ;AACpC,MAAIC,WAAU,KACVC,QAAO,KACP,QAAQ,KACR;AAEJ,SAAO;AAAA,IACL,WAAW,WAAW;AACpB,aAAO,UAAU;AACjB,cAAQ;AAAA,IACV;AAAA,IACA,OAAO,SAASC,UAASC,OAAM;AAC7B,UAAI,QAAQD,WAAU,IAAI,KAAK,CAAC,IAC5B,QAAQ,IAAIA,WAAUF,QAAO;AACjC,UAAI,IAAI,QAAQ,EAAE,IAAI,SAAS;AAC7B,eAAO,MAAMA,UAASC,SAAQA,QAAOE,SAAQ,IAAI,IAAI,SAAS,CAAC,MAAM;AACrE,eAAO,MAAM,OAAOF,KAAI;AACxB,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,eAAO,MAAM,OAAOA,KAAI;AACxB,eAAO,MAAMC,UAASD,KAAI;AAC1B,gBAAQ;AAAA,MACV,WAAW,UAAU,SAAS,SAAS,IAAI;AACzC,YAAI,IAAID,WAAU,KAAK,IAAI;AAAS,UAAAA,YAAW,QAAQ;AACvD,YAAI,IAAIE,WAAU,KAAK,IAAI;AAAS,UAAAA,YAAW,QAAQ;AACvD,QAAAD,QAAO,0BAA0BD,UAASC,OAAMC,UAASC,KAAI;AAC7D,eAAO,MAAM,OAAOF,KAAI;AACxB,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,eAAO,MAAM,OAAOA,KAAI;AACxB,gBAAQ;AAAA,MACV;AACA,aAAO,MAAMD,WAAUE,UAASD,QAAOE,KAAI;AAC3C,cAAQ;AAAA,IACV;AAAA,IACA,SAAS,WAAW;AAClB,aAAO,QAAQ;AACf,MAAAH,WAAUC,QAAO;AAAA,IACnB;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,0BAA0BD,UAASC,OAAMC,UAASC,OAAM;AAC/D,MAAIC,UACA,SACA,oBAAoB,IAAIJ,WAAUE,QAAO;AAC7C,SAAO,IAAI,iBAAiB,IAAI,UAC1B,MAAM,IAAID,KAAI,KAAK,UAAU,IAAIE,KAAI,KAAK,IAAID,QAAO,IACjD,IAAIC,KAAI,KAAKC,WAAU,IAAIH,KAAI,KAAK,IAAID,QAAO,MAC9CI,WAAU,UAAU,kBAAkB,KAC1CH,QAAOE,SAAQ;AACxB;AAEA,SAAS,4BAA4B,MAAM,IAAI,WAAW,QAAQ;AAChE,MAAI;AACJ,MAAI,QAAQ,MAAM;AAChB,UAAM,YAAY;AAClB,WAAO,MAAM,CAAC,IAAI,GAAG;AACrB,WAAO,MAAM,GAAG,GAAG;AACnB,WAAO,MAAM,IAAI,GAAG;AACpB,WAAO,MAAM,IAAI,CAAC;AAClB,WAAO,MAAM,IAAI,CAAC,GAAG;AACrB,WAAO,MAAM,GAAG,CAAC,GAAG;AACpB,WAAO,MAAM,CAAC,IAAI,CAAC,GAAG;AACtB,WAAO,MAAM,CAAC,IAAI,CAAC;AACnB,WAAO,MAAM,CAAC,IAAI,GAAG;AAAA,EACvB,WAAW,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,SAAS;AACzC,QAAI,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC;AACrC,UAAM,YAAY,SAAS;AAC3B,WAAO,MAAM,CAAC,QAAQ,GAAG;AACzB,WAAO,MAAM,GAAG,GAAG;AACnB,WAAO,MAAM,QAAQ,GAAG;AAAA,EAC1B,OAAO;AACL,WAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAC3B;AACF;;;ACrFe,SAARE,gBAAiB,QAAQ;AAC9B,MAAI,KAAK,IAAI,MAAM,GACf,QAAQ,IAAI,SACZ,cAAc,KAAK,GACnB,gBAAgB,IAAI,EAAE,IAAI;AAE9B,WAAS,YAAY,MAAM,IAAI,WAAW,QAAQ;AAChD,iBAAa,QAAQ,QAAQ,OAAO,WAAW,MAAM,EAAE;AAAA,EACzD;AAEA,WAAS,QAAQ,QAAQ,KAAK;AAC5B,WAAO,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI;AAAA,EAClC;AAMA,WAAS,SAAS,QAAQ;AACxB,QAAI,QACA,IACA,IACA,KACA;AACJ,WAAO;AAAA,MACL,WAAW,WAAW;AACpB,cAAM,KAAK;AACX,gBAAQ;AAAA,MACV;AAAA,MACA,OAAO,SAAS,QAAQ,KAAK;AAC3B,YAAI,SAAS,CAAC,QAAQ,GAAG,GACrB,QACA,IAAI,QAAQ,QAAQ,GAAG,GACvB,IAAI,cACA,IAAI,IAAI,KAAK,QAAQ,GAAG,IACxB,IAAI,KAAK,UAAU,SAAS,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI;AAC1D,YAAI,CAAC,WAAW,MAAM,KAAK;AAAI,iBAAO,UAAU;AAChD,YAAI,MAAM,IAAI;AACZ,mBAAS,UAAU,QAAQ,MAAM;AACjC,cAAI,CAAC,UAAU,mBAAW,QAAQ,MAAM,KAAK,mBAAW,QAAQ,MAAM;AACpE,mBAAO,CAAC,IAAI;AAAA,QAChB;AACA,YAAI,MAAM,IAAI;AACZ,kBAAQ;AACR,cAAI,GAAG;AAEL,mBAAO,UAAU;AACjB,qBAAS,UAAU,QAAQ,MAAM;AACjC,mBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,UACnC,OAAO;AAEL,qBAAS,UAAU,QAAQ,MAAM;AACjC,mBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AACpC,mBAAO,QAAQ;AAAA,UACjB;AACA,mBAAS;AAAA,QACX,WAAW,iBAAiB,UAAU,cAAc,GAAG;AACrD,cAAI;AAGJ,cAAI,EAAE,IAAI,QAAQ,IAAI,UAAU,QAAQ,QAAQ,IAAI,IAAI;AACtD,oBAAQ;AACR,gBAAI,aAAa;AACf,qBAAO,UAAU;AACjB,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,QAAQ;AAAA,YACjB,OAAO;AACL,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,QAAQ;AACf,qBAAO,UAAU;AACjB,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM,CAAC,UAAU,CAAC,mBAAW,QAAQ,MAAM,IAAI;AACjD,iBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QACnC;AACA,iBAAS,QAAQ,KAAK,GAAG,KAAK;AAAA,MAChC;AAAA,MACA,SAAS,WAAW;AAClB,YAAI;AAAI,iBAAO,QAAQ;AACvB,iBAAS;AAAA,MACX;AAAA;AAAA;AAAA,MAGA,OAAO,WAAW;AAChB,eAAO,SAAU,OAAO,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAGA,WAAS,UAAU,GAAG,GAAG,KAAK;AAC5B,QAAI,KAAK,UAAU,CAAC,GAChB,KAAK,UAAU,CAAC;AAIpB,QAAI,KAAK,CAAC,GAAG,GAAG,CAAC,GACb,KAAK,eAAe,IAAI,EAAE,GAC1B,OAAO,aAAa,IAAI,EAAE,GAC1B,OAAO,GAAG,CAAC,GACX,cAAc,OAAO,OAAO;AAGhC,QAAI,CAAC;AAAa,aAAO,CAAC,OAAO;AAEjC,QAAI,KAAM,KAAK,OAAO,aAClB,KAAK,CAAC,KAAK,OAAO,aAClB,QAAQ,eAAe,IAAI,EAAE,GAC7B,IAAI,eAAe,IAAI,EAAE,GACzB,IAAI,eAAe,IAAI,EAAE;AAC7B,wBAAoB,GAAG,CAAC;AAGxB,QAAI,IAAI,OACJ,IAAI,aAAa,GAAG,CAAC,GACrB,KAAK,aAAa,GAAG,CAAC,GACtB,KAAK,IAAI,IAAI,MAAM,aAAa,GAAG,CAAC,IAAI;AAE5C,QAAI,KAAK;AAAG;AAEZ,QAAI,IAAI,KAAK,EAAE,GACX,IAAI,eAAe,IAAI,CAAC,IAAI,KAAK,EAAE;AACvC,wBAAoB,GAAG,CAAC;AACxB,QAAI,UAAU,CAAC;AAEf,QAAI,CAAC;AAAK,aAAO;AAGjB,QAAIC,WAAU,EAAE,CAAC,GACbC,WAAU,EAAE,CAAC,GACbC,QAAO,EAAE,CAAC,GACVC,QAAO,EAAE,CAAC,GACV;AAEJ,QAAIF,WAAUD;AAAS,UAAIA,UAASA,WAAUC,UAASA,WAAU;AAEjE,QAAIG,SAAQH,WAAUD,UAClB,QAAQ,IAAII,SAAQ,EAAE,IAAI,SAC1B,WAAW,SAASA,SAAQ;AAEhC,QAAI,CAAC,SAASD,QAAOD;AAAM,UAAIA,OAAMA,QAAOC,OAAMA,QAAO;AAGzD,QAAI,WACE,QACED,QAAOC,QAAO,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAIH,QAAO,IAAI,UAAUE,QAAOC,SACjED,SAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAKC,QAC1BC,SAAQ,MAAMJ,YAAW,EAAE,CAAC,KAAK,EAAE,CAAC,KAAKC,WAAU;AACvD,UAAI,KAAK,eAAe,IAAI,CAAC,IAAI,KAAK,EAAE;AACxC,0BAAoB,IAAI,CAAC;AACzB,aAAO,CAAC,GAAG,UAAU,EAAE,CAAC;AAAA,IAC1B;AAAA,EACF;AAIA,WAAS,KAAK,QAAQ,KAAK;AACzB,QAAI,IAAI,cAAc,SAAS,KAAK,QAChCI,QAAO;AACX,QAAI,SAAS,CAAC;AAAG,MAAAA,SAAQ;AAAA,aAChB,SAAS;AAAG,MAAAA,SAAQ;AAC7B,QAAI,MAAM,CAAC;AAAG,MAAAA,SAAQ;AAAA,aACb,MAAM;AAAG,MAAAA,SAAQ;AAC1B,WAAOA;AAAA,EACT;AAEA,SAAO,aAAK,SAAS,UAAU,aAAa,cAAc,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC;AAC7F;;;AChLe,SAAR,aAAiB,GAAG,GAAGC,KAAIC,KAAIC,KAAIC,KAAI;AAC5C,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,GACL,KAAK,GACL,KAAK,KAAK,IACV,KAAK,KAAK,IACV;AAEJ,MAAIH,MAAK;AACT,MAAI,CAAC,MAAM,IAAI;AAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB;AAEA,MAAIE,MAAK;AACT,MAAI,CAAC,MAAM,IAAI;AAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB;AAEA,MAAID,MAAK;AACT,MAAI,CAAC,MAAM,IAAI;AAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB;AAEA,MAAIE,MAAK;AACT,MAAI,CAAC,MAAM,IAAI;AAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI;AAAI;AACZ,QAAI,IAAI;AAAI,WAAK;AAAA,EACnB;AAEA,MAAI,KAAK;AAAG,MAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AAClD,MAAI,KAAK;AAAG,MAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AAClD,SAAO;AACT;;;ACpDA,IAAI,UAAU;AAAd,IAAmB,UAAU,CAAC;AAKf,SAAR,cAA+BC,KAAIC,KAAIC,KAAIC,KAAI;AAEpD,WAAS,QAAQ,GAAG,GAAG;AACrB,WAAOH,OAAM,KAAK,KAAKE,OAAMD,OAAM,KAAK,KAAKE;AAAA,EAC/C;AAEA,WAAS,YAAY,MAAM,IAAI,WAAW,QAAQ;AAChD,QAAI,IAAI,GAAG,KAAK;AAChB,QAAI,QAAQ,SACJ,IAAI,OAAO,MAAM,SAAS,QAAQ,KAAK,OAAO,IAAI,SAAS,MAC5D,aAAa,MAAM,EAAE,IAAI,IAAI,YAAY,GAAG;AACjD;AAAG,eAAO,MAAM,MAAM,KAAK,MAAM,IAAIH,MAAKE,KAAI,IAAI,IAAIC,MAAKF,GAAE;AAAA,cACrD,KAAK,IAAI,YAAY,KAAK,OAAO;AAAA,IAC3C,OAAO;AACL,aAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAEA,WAAS,OAAO,GAAG,WAAW;AAC5B,WAAO,IAAI,EAAE,CAAC,IAAID,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAChD,IAAI,EAAE,CAAC,IAAIE,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAC/C,IAAI,EAAE,CAAC,IAAID,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAC/C,YAAY,IAAI,IAAI;AAAA,EAC5B;AAEA,WAASG,qBAAoB,GAAG,GAAG;AACjC,WAAO,aAAa,EAAE,GAAG,EAAE,CAAC;AAAA,EAC9B;AAEA,WAAS,aAAa,GAAG,GAAG;AAC1B,QAAI,KAAK,OAAO,GAAG,CAAC,GAChB,KAAK,OAAO,GAAG,CAAC;AACpB,WAAO,OAAO,KAAK,KAAK,KAClB,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrB,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrB,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrB,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EAClB;AAEA,SAAO,SAAS,QAAQ;AACtB,QAAI,eAAe,QACf,eAAe,eAAW,GAC1B,UACA,SACA,MACA,KAAK,KAAK,KACV,IAAI,IAAI,IACR,OACA;AAEJ,QAAI,aAAa;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,UAAI,QAAQ,GAAG,CAAC;AAAG,qBAAa,MAAM,GAAG,CAAC;AAAA,IAC5C;AAEA,aAAS,gBAAgB;AACvB,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,iBAASC,QAAO,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAIA,MAAK,QAAQC,SAAQD,MAAK,CAAC,GAAG,IAAI,IAAIE,MAAKD,OAAM,CAAC,GAAGE,MAAKF,OAAM,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACrH,eAAKC,KAAI,KAAKC,KAAIF,SAAQD,MAAK,CAAC,GAAGE,MAAKD,OAAM,CAAC,GAAGE,MAAKF,OAAM,CAAC;AAC9D,cAAI,MAAMH,KAAI;AAAE,gBAAIK,MAAKL,QAAOI,MAAK,OAAOJ,MAAK,OAAOK,MAAK,OAAOR,MAAK;AAAK,gBAAE;AAAA,UAAS,OACpF;AAAE,gBAAIQ,OAAML,QAAOI,MAAK,OAAOJ,MAAK,OAAOK,MAAK,OAAOR,MAAK;AAAK,gBAAE;AAAA,UAAS;AAAA,QACnF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAGA,aAAS,eAAe;AACtB,qBAAe,cAAc,WAAW,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ;AAAA,IACpE;AAEA,aAAS,aAAa;AACpB,UAAI,cAAc,cAAc,GAC5B,cAAc,SAAS,aACvBS,YAAW,WAAW,MAAM,QAAQ,GAAG;AAC3C,UAAI,eAAeA,UAAS;AAC1B,eAAO,aAAa;AACpB,YAAI,aAAa;AACf,iBAAO,UAAU;AACjB,sBAAY,MAAM,MAAM,GAAG,MAAM;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAIA,UAAS;AACX,yBAAW,UAAUL,sBAAqB,aAAa,aAAa,MAAM;AAAA,QAC5E;AACA,eAAO,WAAW;AAAA,MACpB;AACA,qBAAe,QAAQ,WAAW,UAAU,OAAO;AAAA,IACrD;AAEA,aAAS,YAAY;AACnB,iBAAW,QAAQM;AACnB,UAAI;AAAS,gBAAQ,KAAK,OAAO,CAAC,CAAC;AACnC,cAAQ;AACR,WAAK;AACL,WAAK,KAAK;AAAA,IACZ;AAKA,aAAS,UAAU;AACjB,UAAI,UAAU;AACZ,QAAAA,WAAU,KAAK,GAAG;AAClB,YAAI,OAAO;AAAI,uBAAa,OAAO;AACnC,iBAAS,KAAK,aAAa,OAAO,CAAC;AAAA,MACrC;AACA,iBAAW,QAAQ;AACnB,UAAI;AAAI,qBAAa,QAAQ;AAAA,IAC/B;AAEA,aAASA,WAAU,GAAG,GAAG;AACvB,UAAI,IAAI,QAAQ,GAAG,CAAC;AACpB,UAAI;AAAS,aAAK,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7B,UAAI,OAAO;AACT,cAAM,GAAG,MAAM,GAAG,MAAM;AACxB,gBAAQ;AACR,YAAI,GAAG;AACL,uBAAa,UAAU;AACvB,uBAAa,MAAM,GAAG,CAAC;AAAA,QACzB;AAAA,MACF,OAAO;AACL,YAAI,KAAK;AAAI,uBAAa,MAAM,GAAG,CAAC;AAAA,aAC/B;AACH,cAAI,IAAI,CAAC,KAAK,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,EAAE,CAAC,CAAC,GACjG,IAAI,CAAC,IAAI,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC;AACjG,cAAI,aAAS,GAAG,GAAGV,KAAIC,KAAIC,KAAIC,GAAE,GAAG;AAClC,gBAAI,CAAC,IAAI;AACP,2BAAa,UAAU;AACvB,2BAAa,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,YAC/B;AACA,yBAAa,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7B,gBAAI,CAAC;AAAG,2BAAa,QAAQ;AAC7B,oBAAQ;AAAA,UACV,WAAW,GAAG;AACZ,yBAAa,UAAU;AACvB,yBAAa,MAAM,GAAG,CAAC;AACvB,oBAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,WAAK,GAAG,KAAK,GAAG,KAAK;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AACF;;;ACrKe,SAAR,iBAAmB;AACxB,MAAIQ,MAAK,GACLC,MAAK,GACLC,MAAK,KACLC,MAAK,KACL,OACA,aACA;AAEJ,SAAO,OAAO;AAAA,IACZ,QAAQ,SAAS,QAAQ;AACvB,aAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,cAAcH,KAAIC,KAAIC,KAAIC,GAAE,EAAE,cAAc,MAAM;AAAA,IAC7G;AAAA,IACA,QAAQ,SAAS,GAAG;AAClB,aAAO,UAAU,UAAUH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,QAAQ,cAAc,MAAM,QAAQ,CAAC,CAACH,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,IAChJ;AAAA,EACF;AACF;;;ACdA,IAAI;AAAJ,IACIC;AADJ,IAEIC;AAFJ,IAGIC;AAEJ,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AACd;AAEA,SAAS,kBAAkB;AACzB,eAAa,QAAQ;AACrB,eAAa,UAAU;AACzB;AAEA,SAAS,gBAAgB;AACvB,eAAa,QAAQ,aAAa,UAAU;AAC9C;AAEA,SAAS,iBAAiB,QAAQ,KAAK;AACrC,YAAU,SAAS,OAAO;AAC1B,EAAAF,WAAU,QAAQC,WAAU,IAAI,GAAG,GAAGC,WAAU,IAAI,GAAG;AACvD,eAAa,QAAQ;AACvB;AAEA,SAAS,YAAY,QAAQ,KAAK;AAChC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,SAAS,IAAI,GAAG,GAChB,QAAQ,IAAI,SAASF,QAAO,GAC5B,WAAW,IAAI,KAAK,GACpB,WAAW,IAAI,KAAK,GACpB,IAAI,SAAS,UACb,IAAIE,WAAU,SAASD,WAAU,SAAS,UAC1C,IAAIA,WAAU,SAASC,WAAU,SAAS;AAC9C,YAAU,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,EAAAF,WAAU,QAAQC,WAAU,QAAQC,WAAU;AAChD;AAEe,SAAR,eAAiBC,SAAQ;AAC9B,cAAY,IAAI,MAAM;AACtB,iBAAOA,SAAQ,YAAY;AAC3B,SAAO,CAAC;AACV;;;AClDA,IAAI,cAAc,CAAC,MAAM,IAAI;AAA7B,IACI,SAAS,EAAC,MAAM,cAAc,YAAwB;AAE3C,SAAR,iBAAiB,GAAG,GAAG;AAC5B,cAAY,CAAC,IAAI;AACjB,cAAY,CAAC,IAAI;AACjB,SAAO,eAAO,MAAM;AACtB;;;ACLA,IAAI,qBAAqB;AAAA,EACvB,SAAS,SAASC,SAAQ,OAAO;AAC/B,WAAO,iBAAiBA,QAAO,UAAU,KAAK;AAAA,EAChD;AAAA,EACA,mBAAmB,SAASA,SAAQ,OAAO;AACzC,QAAI,WAAWA,QAAO,UAAU,IAAI,IAAI,IAAI,SAAS;AACrD,WAAO,EAAE,IAAI;AAAG,UAAI,iBAAiB,SAAS,CAAC,EAAE,UAAU,KAAK;AAAG,eAAO;AAC1E,WAAO;AAAA,EACT;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,WAAW;AACjB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAASA,SAAQ,OAAO;AAC7B,WAAO,cAAcA,QAAO,aAAa,KAAK;AAAA,EAChD;AAAA,EACA,YAAY,SAASA,SAAQ,OAAO;AAClC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI;AAAG,UAAI,cAAcA,aAAY,CAAC,GAAG,KAAK;AAAG,eAAO;AACjE,WAAO;AAAA,EACT;AAAA,EACA,YAAY,SAASD,SAAQ,OAAO;AAClC,WAAO,aAAaA,QAAO,aAAa,KAAK;AAAA,EAC/C;AAAA,EACA,iBAAiB,SAASA,SAAQ,OAAO;AACvC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI;AAAG,UAAI,aAAaA,aAAY,CAAC,GAAG,KAAK;AAAG,eAAO;AAChE,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAASD,SAAQ,OAAO;AAC/B,WAAO,gBAAgBA,QAAO,aAAa,KAAK;AAAA,EAClD;AAAA,EACA,cAAc,SAASA,SAAQ,OAAO;AACpC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI;AAAG,UAAI,gBAAgBA,aAAY,CAAC,GAAG,KAAK;AAAG,eAAO;AACnE,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,SAASD,SAAQ,OAAO;AAC1C,QAAI,aAAaA,QAAO,YAAY,IAAI,IAAI,IAAI,WAAW;AAC3D,WAAO,EAAE,IAAI;AAAG,UAAI,iBAAiB,WAAW,CAAC,GAAG,KAAK;AAAG,eAAO;AACnE,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,UAAU,OAAO;AACzC,SAAO,YAAY,qBAAqB,eAAe,SAAS,IAAI,IAC9D,qBAAqB,SAAS,IAAI,EAAE,UAAU,KAAK,IACnD;AACR;AAEA,SAAS,cAAcC,cAAa,OAAO;AACzC,SAAO,iBAASA,cAAa,KAAK,MAAM;AAC1C;AAEA,SAAS,aAAaA,cAAa,OAAO;AACxC,MAAI,IAAI,IAAI;AACZ,WAAS,IAAI,GAAG,IAAIA,aAAY,QAAQ,IAAI,GAAG,KAAK;AAClD,SAAK,iBAASA,aAAY,CAAC,GAAG,KAAK;AACnC,QAAI,OAAO;AAAG,aAAO;AACrB,QAAI,IAAI,GAAG;AACT,WAAK,iBAASA,aAAY,CAAC,GAAGA,aAAY,IAAI,CAAC,CAAC;AAChD,UACE,KAAK,KACL,MAAM,MACN,MAAM,OACL,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK,WAAW;AAEhE,eAAO;AAAA,IACX;AACA,SAAK;AAAA,EACP;AACA,SAAO;AACT;AAEA,SAAS,gBAAgBA,cAAa,OAAO;AAC3C,SAAO,CAAC,CAAC,wBAAgBA,aAAY,IAAI,WAAW,GAAG,aAAa,KAAK,CAAC;AAC5E;AAEA,SAAS,YAAY,MAAM;AACzB,SAAO,OAAO,KAAK,IAAI,YAAY,GAAG,KAAK,IAAI,GAAG;AACpD;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,CAAC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAChD;AAEe,SAAR,iBAAiBD,SAAQ,OAAO;AACrC,UAAQA,WAAU,mBAAmB,eAAeA,QAAO,IAAI,IACzD,mBAAmBA,QAAO,IAAI,IAC9B,kBAAkBA,SAAQ,KAAK;AACvC;;;AC7FA,SAAS,WAAWE,KAAIC,KAAI,IAAI;AAC9B,MAAI,IAAI,cAAMD,KAAIC,MAAK,SAAS,EAAE,EAAE,OAAOA,GAAE;AAC7C,SAAO,SAAS,GAAG;AAAE,WAAO,EAAE,IAAI,SAASC,IAAG;AAAE,aAAO,CAAC,GAAGA,EAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AACrE;AAEA,SAAS,WAAWC,KAAIC,KAAI,IAAI;AAC9B,MAAI,IAAI,cAAMD,KAAIC,MAAK,SAAS,EAAE,EAAE,OAAOA,GAAE;AAC7C,SAAO,SAAS,GAAG;AAAE,WAAO,EAAE,IAAI,SAASC,IAAG;AAAE,aAAO,CAACA,IAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AACrE;AAEe,SAAR,YAA6B;AAClC,MAAID,KAAID,KAAIG,KAAIC,KACZN,KAAID,KAAIQ,KAAIC,KACZ,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAChC,GAAG,GAAG,GAAG,GACT,YAAY;AAEhB,WAASC,aAAY;AACnB,WAAO,EAAC,MAAM,mBAAmB,aAAa,MAAM,EAAC;AAAA,EACvD;AAEA,WAAS,QAAQ;AACf,WAAO,cAAM,KAAKH,MAAK,EAAE,IAAI,IAAID,KAAI,EAAE,EAAE,IAAI,CAAC,EACzC,OAAO,cAAM,KAAKG,MAAK,EAAE,IAAI,IAAID,KAAI,EAAE,EAAE,IAAI,CAAC,CAAC,EAC/C,OAAO,cAAM,KAAKL,MAAK,EAAE,IAAI,IAAIC,KAAI,EAAE,EAAE,OAAO,SAASC,IAAG;AAAE,aAAO,IAAIA,KAAI,EAAE,IAAI;AAAA,IAAS,CAAC,EAAE,IAAI,CAAC,CAAC,EACrG,OAAO,cAAM,KAAKL,MAAK,EAAE,IAAI,IAAIC,KAAI,EAAE,EAAE,OAAO,SAASC,IAAG;AAAE,aAAO,IAAIA,KAAI,EAAE,IAAI;AAAA,IAAS,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,EAC5G;AAEA,EAAAQ,WAAU,QAAQ,WAAW;AAC3B,WAAO,MAAM,EAAE,IAAI,SAASC,cAAa;AAAE,aAAO,EAAC,MAAM,cAAc,aAAaA,aAAW;AAAA,IAAG,CAAC;AAAA,EACrG;AAEA,EAAAD,WAAU,UAAU,WAAW;AAC7B,WAAO;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,QACX,EAAEH,GAAE,EAAE;AAAA,UACN,EAAEC,GAAE,EAAE,MAAM,CAAC;AAAA,UACb,EAAEF,GAAE,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,UACvB,EAAEG,GAAE,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,QAAC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAEA,EAAAC,WAAU,SAAS,SAAS,GAAG;AAC7B,QAAI,CAAC,UAAU;AAAQ,aAAOA,WAAU,YAAY;AACpD,WAAOA,WAAU,YAAY,CAAC,EAAE,YAAY,CAAC;AAAA,EAC/C;AAEA,EAAAA,WAAU,cAAc,SAAS,GAAG;AAClC,QAAI,CAAC,UAAU;AAAQ,aAAO,CAAC,CAACH,KAAIE,GAAE,GAAG,CAACH,KAAIE,GAAE,CAAC;AACjD,IAAAD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAAG,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,QAAID,MAAKD;AAAI,UAAIC,KAAIA,MAAKD,KAAIA,MAAK;AACnC,QAAIG,MAAKD;AAAI,UAAIC,KAAIA,MAAKD,KAAIA,MAAK;AACnC,WAAOE,WAAU,UAAU,SAAS;AAAA,EACtC;AAEA,EAAAA,WAAU,cAAc,SAAS,GAAG;AAClC,QAAI,CAAC,UAAU;AAAQ,aAAO,CAAC,CAACP,KAAIH,GAAE,GAAG,CAACI,KAAIH,GAAE,CAAC;AACjD,IAAAE,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAAJ,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,QAAIE,MAAKC;AAAI,UAAID,KAAIA,MAAKC,KAAIA,MAAK;AACnC,QAAIJ,MAAKC;AAAI,UAAID,KAAIA,MAAKC,KAAIA,MAAK;AACnC,WAAOS,WAAU,UAAU,SAAS;AAAA,EACtC;AAEA,EAAAA,WAAU,OAAO,SAAS,GAAG;AAC3B,QAAI,CAAC,UAAU;AAAQ,aAAOA,WAAU,UAAU;AAClD,WAAOA,WAAU,UAAU,CAAC,EAAE,UAAU,CAAC;AAAA,EAC3C;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU;AAAQ,aAAO,CAAC,IAAI,EAAE;AACrC,SAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACrB,WAAOA;AAAA,EACT;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU;AAAQ,aAAO,CAAC,IAAI,EAAE;AACrC,SAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACrB,WAAOA;AAAA,EACT;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU;AAAQ,aAAO;AAC9B,gBAAY,CAAC;AACb,QAAI,WAAWV,KAAIC,KAAI,EAAE;AACzB,QAAI,WAAWE,KAAIC,KAAI,SAAS;AAChC,QAAI,WAAWK,KAAID,KAAI,EAAE;AACzB,QAAI,WAAWD,KAAID,KAAI,SAAS;AAChC,WAAOI;AAAA,EACT;AAEA,SAAOA,WACF,YAAY,CAAC,CAAC,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,EACxD,YAAY,CAAC,CAAC,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC;AAC/D;AAEO,SAAS,cAAc;AAC5B,SAAO,UAAU,EAAE;AACrB;;;ACtGe,SAAR,oBAAiB,GAAG,GAAG;AAC5B,MAAIE,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZ,MAAM,IAAIF,GAAE,GACZ,MAAM,IAAIA,GAAE,GACZ,MAAM,IAAIE,GAAE,GACZ,MAAM,IAAIA,GAAE,GACZ,MAAM,MAAM,IAAIH,GAAE,GAClB,MAAM,MAAM,IAAIA,GAAE,GAClB,MAAM,MAAM,IAAIE,GAAE,GAClB,MAAM,MAAM,IAAIA,GAAE,GAClB,IAAI,IAAI,KAAK,KAAK,SAASC,MAAKF,GAAE,IAAI,MAAM,MAAM,SAASC,MAAKF,GAAE,CAAC,CAAC,GACpE,IAAI,IAAI,CAAC;AAEb,MAAI,cAAc,IAAI,SAAS,GAAG;AAChC,QAAI,IAAI,IAAI,KAAK,CAAC,IAAI,GAClB,IAAI,IAAI,IAAI,CAAC,IAAI,GACjB,IAAI,IAAI,MAAM,IAAI,KAClB,IAAI,IAAI,MAAM,IAAI,KAClB,IAAI,IAAI,MAAM,IAAI;AACtB,WAAO;AAAA,MACL,MAAM,GAAG,CAAC,IAAI;AAAA,MACd,MAAM,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;AAAA,IAClC;AAAA,EACF,IAAI,WAAW;AACb,WAAO,CAACA,MAAK,SAASC,MAAK,OAAO;AAAA,EACpC;AAEA,cAAY,WAAW;AAEvB,SAAO;AACT;;;ACnCA,IAAO,mBAAQ,OAAK;;;ACIpB,IAAIG,WAAU,IAAI,MAAM;AAAxB,IACIC,eAAc,IAAI,MAAM;AAD5B,IAEI;AAFJ,IAGI;AAHJ,IAIIC;AAJJ,IAKIC;AAEJ,IAAIC,cAAa;AAAA,EACf,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,IAAAA,YAAW,YAAYC;AACvB,IAAAD,YAAW,UAAUE;AAAA,EACvB;AAAA,EACA,YAAY,WAAW;AACrB,IAAAF,YAAW,YAAYA,YAAW,UAAUA,YAAW,QAAQ;AAC/D,IAAAJ,SAAQ,IAAI,IAAIC,YAAW,CAAC;AAC5B,IAAAA,eAAc,IAAI,MAAM;AAAA,EAC1B;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,OAAOD,WAAU;AACrB,IAAAA,WAAU,IAAI,MAAM;AACpB,WAAO;AAAA,EACT;AACF;AAEA,SAASK,iBAAgB;AACvB,EAAAD,YAAW,QAAQG;AACrB;AAEA,SAASA,gBAAe,GAAG,GAAG;AAC5B,EAAAH,YAAW,QAAQI;AACnB,QAAMN,MAAK,GAAG,MAAMC,MAAK;AAC3B;AAEA,SAASK,WAAU,GAAG,GAAG;AACvB,EAAAP,aAAY,IAAIE,MAAK,IAAID,MAAK,CAAC;AAC/B,EAAAA,MAAK,GAAGC,MAAK;AACf;AAEA,SAASG,eAAc;AACrB,EAAAE,WAAU,KAAK,GAAG;AACpB;AAEA,IAAOC,gBAAQL;;;AC/Cf,IAAIM,MAAK;AAAT,IACIC,MAAKD;AADT,IAEI,KAAK,CAACA;AAFV,IAGI,KAAK;AAET,IAAIE,gBAAe;AAAA,EACjB,OAAOC;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,QAAQ,WAAW;AACjB,QAAI,SAAS,CAAC,CAACH,KAAIC,GAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAChC,SAAK,KAAK,EAAEA,MAAKD,MAAK;AACtB,WAAO;AAAA,EACT;AACF;AAEA,SAASG,aAAY,GAAG,GAAG;AACzB,MAAI,IAAIH;AAAI,IAAAA,MAAK;AACjB,MAAI,IAAI;AAAI,SAAK;AACjB,MAAI,IAAIC;AAAI,IAAAA,MAAK;AACjB,MAAI,IAAI;AAAI,SAAK;AACnB;AAEA,IAAOG,kBAAQF;;;ACvBf,IAAIG,MAAK;AAAT,IACIC,MAAK;AADT,IAEIC,MAAK;AAFT,IAGIC,MAAK;AAHT,IAIIC,MAAK;AAJT,IAKIC,MAAK;AALT,IAMIC,MAAK;AANT,IAOIC,MAAK;AAPT,IAQIC,MAAK;AART,IASIC;AATJ,IAUIC;AAVJ,IAWIC;AAXJ,IAYIC;AAEJ,IAAIC,kBAAiB;AAAA,EACnB,OAAOC;AAAA,EACP,WAAWC;AAAA,EACX,SAASC;AAAA,EACT,cAAc,WAAW;AACvB,IAAAH,gBAAe,YAAYI;AAC3B,IAAAJ,gBAAe,UAAUK;AAAA,EAC3B;AAAA,EACA,YAAY,WAAW;AACrB,IAAAL,gBAAe,QAAQC;AACvB,IAAAD,gBAAe,YAAYE;AAC3B,IAAAF,gBAAe,UAAUG;AAAA,EAC3B;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,WAAWR,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IAC/BH,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IACtBH,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IACtB,CAAC,KAAK,GAAG;AACf,IAAAF,MAAKC,MAAKC,MACVC,MAAKC,MAAKC,MACVC,MAAKC,MAAKC,MAAK;AACf,WAAO;AAAA,EACT;AACF;AAEA,SAASM,eAAc,GAAG,GAAG;AAC3B,EAAAd,OAAM;AACN,EAAAC,OAAM;AACN,IAAEC;AACJ;AAEA,SAASa,qBAAoB;AAC3B,EAAAF,gBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,EAAAA,gBAAe,QAAQ;AACvB,EAAAC,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,KAAK,IAAID,KAAI,KAAK,IAAIC,KAAI,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE;AACxD,EAAAT,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM;AACN,EAAAS,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,SAASI,mBAAkB;AACzB,EAAAH,gBAAe,QAAQC;AACzB;AAEA,SAASG,qBAAoB;AAC3B,EAAAJ,gBAAe,QAAQ;AACzB;AAEA,SAASK,mBAAkB;AACzB,oBAAkBT,MAAKC,IAAG;AAC5B;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,EAAAG,gBAAe,QAAQ;AACvB,EAAAC,eAAcL,OAAME,MAAK,GAAGD,OAAME,MAAK,CAAC;AAC1C;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,KAAK,IAAID,KACT,KAAK,IAAIC,KACT,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE;AAE9B,EAAAT,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM;AAEN,MAAIO,MAAK,IAAID,MAAK;AAClB,EAAAL,OAAM,KAAKK,MAAK;AAChB,EAAAJ,OAAM,KAAKK,MAAK;AAChB,EAAAJ,OAAM,IAAI;AACV,EAAAM,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,IAAOO,oBAAQN;;;AChGA,SAAR,YAA6B,SAAS;AAC3C,OAAK,WAAW;AAClB;AAEA,YAAY,YAAY;AAAA,EACtB,SAAS;AAAA,EACT,aAAa,SAAS,GAAG;AACvB,WAAO,KAAK,UAAU,GAAG;AAAA,EAC3B;AAAA,EACA,cAAc,WAAW;AACvB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY,WAAW;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,UAAU;AAAG,WAAK,SAAS,UAAU;AAC9C,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,SAAS,GAAG,GAAG;AACpB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB,aAAK,SAAS;AACd;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB;AAAA,MACF;AAAA,MACA,SAAS;AACP,aAAK,SAAS,OAAO,IAAI,KAAK,SAAS,CAAC;AACxC,aAAK,SAAS,IAAI,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG;AAC5C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV;;;ACxCA,IAAIO,aAAY,IAAI,MAAM;AAA1B,IACI;AADJ,IAEIC;AAFJ,IAGIC;AAHJ,IAIIC;AAJJ,IAKIC;AAEJ,IAAIC,gBAAe;AAAA,EACjB,OAAO;AAAA,EACP,WAAW,WAAW;AACpB,IAAAA,cAAa,QAAQC;AAAA,EACvB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI;AAAY,MAAAC,aAAYN,MAAKC,IAAG;AACpC,IAAAG,cAAa,QAAQ;AAAA,EACvB;AAAA,EACA,cAAc,WAAW;AACvB,iBAAa;AAAA,EACf;AAAA,EACA,YAAY,WAAW;AACrB,iBAAa;AAAA,EACf;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,SAAS,CAACL;AACd,IAAAA,aAAY,IAAI,MAAM;AACtB,WAAO;AAAA,EACT;AACF;AAEA,SAASM,kBAAiB,GAAG,GAAG;AAC9B,EAAAD,cAAa,QAAQE;AACrB,EAAAN,OAAME,MAAK,GAAGD,OAAME,MAAK;AAC3B;AAEA,SAASG,aAAY,GAAG,GAAG;AACzB,EAAAJ,OAAM,GAAGC,OAAM;AACf,EAAAJ,WAAU,IAAI,KAAKG,MAAKA,MAAKC,MAAKA,GAAE,CAAC;AACrC,EAAAD,MAAK,GAAGC,MAAK;AACf;AAEA,IAAO,kBAAQC;;;AC5CA,SAAR,aAA8B;AACnC,OAAK,UAAU,CAAC;AAClB;AAEA,WAAW,YAAY;AAAA,EACrB,SAAS;AAAA,EACT,SAAS,OAAO,GAAG;AAAA,EACnB,aAAa,SAAS,GAAG;AACvB,SAAK,IAAI,CAAC,OAAO,KAAK;AAAS,WAAK,UAAU,GAAG,KAAK,UAAU;AAChE,WAAO;AAAA,EACT;AAAA,EACA,cAAc,WAAW;AACvB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY,WAAW;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,UAAU;AAAG,WAAK,QAAQ,KAAK,GAAG;AAC3C,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,SAAS,GAAG,GAAG;AACpB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,QAAQ,KAAK,KAAK,GAAG,KAAK,CAAC;AAChC,aAAK,SAAS;AACd;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,QAAQ,KAAK,KAAK,GAAG,KAAK,CAAC;AAChC;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,KAAK,WAAW;AAAM,eAAK,UAAU,OAAO,KAAK,OAAO;AAC5D,aAAK,QAAQ,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,KAAK,QAAQ,QAAQ;AACvB,UAAI,SAAS,KAAK,QAAQ,KAAK,EAAE;AACjC,WAAK,UAAU,CAAC;AAChB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,OAAO,QAAQ;AACtB,SAAO,QAAQ,SACT,MAAM,SAAS,MAAM,SAAS,cAAc,KAAK,SACjD,MAAM,SAAS,MAAM,SAAS,cAAc,IAAI,SAChD;AACR;;;ACjDe,SAAR,aAAiBG,aAAY,SAAS;AAC3C,MAAI,cAAc,KACd,kBACA;AAEJ,WAAS,KAAKC,SAAQ;AACpB,QAAIA,SAAQ;AACV,UAAI,OAAO,gBAAgB;AAAY,sBAAc,YAAY,CAAC,YAAY,MAAM,MAAM,SAAS,CAAC;AACpG,qBAAOA,SAAQ,iBAAiB,aAAa,CAAC;AAAA,IAChD;AACA,WAAO,cAAc,OAAO;AAAA,EAC9B;AAEA,OAAK,OAAO,SAASA,SAAQ;AAC3B,mBAAOA,SAAQ,iBAAiBC,aAAQ,CAAC;AACzC,WAAOA,cAAS,OAAO;AAAA,EACzB;AAEA,OAAK,UAAU,SAASD,SAAQ;AAC9B,mBAAOA,SAAQ,iBAAiB,eAAW,CAAC;AAC5C,WAAO,gBAAY,OAAO;AAAA,EAC5B;AAEA,OAAK,SAAS,SAASA,SAAQ;AAC7B,mBAAOA,SAAQ,iBAAiBE,eAAU,CAAC;AAC3C,WAAOA,gBAAW,OAAO;AAAA,EAC3B;AAEA,OAAK,WAAW,SAASF,SAAQ;AAC/B,mBAAOA,SAAQ,iBAAiBG,iBAAY,CAAC;AAC7C,WAAOA,kBAAa,OAAO;AAAA,EAC7B;AAEA,OAAK,aAAa,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,mBAAmB,KAAK,QAAQJ,cAAa,MAAM,qBAAaA,cAAa,GAAG,QAAQ,QAAQA;AAAA,EAC7H;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU;AAAQ,aAAO;AAC9B,oBAAgB,KAAK,QAAQ,UAAU,MAAM,IAAI,gBAAc,IAAI,YAAY,UAAU,CAAC;AAC1F,QAAI,OAAO,gBAAgB;AAAY,oBAAc,YAAY,WAAW;AAC5E,WAAO;AAAA,EACT;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,QAAI,CAAC,UAAU;AAAQ,aAAO;AAC9B,kBAAc,OAAO,MAAM,aAAa,KAAK,cAAc,YAAY,CAAC,CAAC,GAAG,CAAC;AAC7E,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,WAAWA,WAAU,EAAE,QAAQ,OAAO;AACpD;;;AC5De,SAAR,kBAAiB,SAAS;AAC/B,SAAO;AAAA,IACL,QAAQ,YAAY,OAAO;AAAA,EAC7B;AACF;AAEO,SAAS,YAAY,SAAS;AACnC,SAAO,SAAS,QAAQ;AACtB,QAAI,IAAI,IAAI;AACZ,aAAS,OAAO;AAAS,QAAE,GAAG,IAAI,QAAQ,GAAG;AAC7C,MAAE,SAAS;AACX,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB;AAAC;AAE5B,gBAAgB,YAAY;AAAA,EAC1B,aAAa;AAAA,EACb,OAAO,SAAS,GAAG,GAAG;AAAE,SAAK,OAAO,MAAM,GAAG,CAAC;AAAA,EAAG;AAAA,EACjD,QAAQ,WAAW;AAAE,SAAK,OAAO,OAAO;AAAA,EAAG;AAAA,EAC3C,WAAW,WAAW;AAAE,SAAK,OAAO,UAAU;AAAA,EAAG;AAAA,EACjD,SAAS,WAAW;AAAE,SAAK,OAAO,QAAQ;AAAA,EAAG;AAAA,EAC7C,cAAc,WAAW;AAAE,SAAK,OAAO,aAAa;AAAA,EAAG;AAAA,EACvD,YAAY,WAAW;AAAE,SAAK,OAAO,WAAW;AAAA,EAAG;AACrD;;;ACtBA,SAAS,IAAIK,aAAY,WAAWC,SAAQ;AAC1C,MAAI,OAAOD,YAAW,cAAcA,YAAW,WAAW;AAC1D,EAAAA,YAAW,MAAM,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,MAAI,QAAQ;AAAM,IAAAA,YAAW,WAAW,IAAI;AAC5C,iBAAUC,SAAQD,YAAW,OAAOE,eAAY,CAAC;AACjD,YAAUA,gBAAa,OAAO,CAAC;AAC/B,MAAI,QAAQ;AAAM,IAAAF,YAAW,WAAW,IAAI;AAC5C,SAAOA;AACT;AAEO,SAAS,UAAUA,aAAY,QAAQC,SAAQ;AACpD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,GAC9B,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,GAC9B,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAC7D,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,GACpD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;AACxD,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;AAEO,SAAS,QAAQD,aAAY,MAAMC,SAAQ;AAChD,SAAO,UAAUD,aAAY,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGC,OAAM;AACrD;AAEO,SAAS,SAASD,aAAY,OAAOC,SAAQ;AAClD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,CAAC,OACL,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IACzB,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,GACpC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACnB,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;AAEO,SAAS,UAAUD,aAAY,QAAQC,SAAQ;AACpD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,CAAC,QACL,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IACzB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GACf,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;AACxC,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;;;AC1CA,IAAI,WAAW;AAAf,IACI,iBAAiB,IAAI,KAAK,OAAO;AAEtB,SAAR,iBAAiB,SAAS,QAAQ;AACvC,SAAO,CAAC,SAAS,SAAS,SAAS,MAAM,IAAI,aAAa,OAAO;AACnE;AAEA,SAAS,aAAa,SAAS;AAC7B,SAAO,YAAY;AAAA,IACjB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,QAAQ,GAAG,CAAC;AAChB,WAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC;AACH;AAEA,SAAS,SAAS,SAAS,QAAQ;AAEjC,WAAS,eAAeE,KAAIC,KAAIC,UAAS,IAAIC,KAAI,IAAIC,KAAIC,KAAIC,UAAS,IAAIC,KAAI,IAAI,OAAO,QAAQ;AAC/F,QAAI,KAAKH,MAAKJ,KACV,KAAKK,MAAKJ,KACV,KAAK,KAAK,KAAK,KAAK;AACxB,QAAI,KAAK,IAAI,UAAU,SAAS;AAC9B,UAAI,IAAI,KAAK,IACT,IAAIE,MAAKI,KACT,IAAI,KAAK,IACT,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAC9B,OAAO,KAAK,KAAK,CAAC,GAClBC,WAAU,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,IAAIN,WAAUI,QAAO,IAAI,WAAWJ,WAAUI,YAAW,IAAI,MAAM,GAAG,CAAC,GAC9G,IAAI,QAAQE,UAAS,IAAI,GACzB,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,KAAKR,KACX,MAAM,KAAKC,KACX,KAAK,KAAK,MAAM,KAAK;AACzB,UAAI,KAAK,KAAK,KAAK,UACZ,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG,IAAI,OACxC,KAAK,KAAKE,MAAKI,MAAK,KAAK,KAAK,gBAAgB;AACnD,uBAAeP,KAAIC,KAAIC,UAAS,IAAIC,KAAI,IAAI,IAAI,IAAIK,UAAS,KAAK,GAAG,KAAK,GAAG,GAAG,OAAO,MAAM;AAC7F,eAAO,MAAM,IAAI,EAAE;AACnB,uBAAe,IAAI,IAAIA,UAAS,GAAG,GAAG,GAAGJ,KAAIC,KAAIC,UAAS,IAAIC,KAAI,IAAI,OAAO,MAAM;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS,QAAQ;AACtB,QAAIE,WAAUC,MAAKC,MAAK,KAAK,KAAK,KAC9BT,UAASF,KAAIC,KAAI,IAAIE,KAAI;AAE7B,QAAI,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,WAAW;AAAE,eAAO,aAAa;AAAG,uBAAe,YAAY;AAAA,MAAW;AAAA,MACxF,YAAY,WAAW;AAAE,eAAO,WAAW;AAAG,uBAAe,YAAY;AAAA,MAAW;AAAA,IACtF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,UAAI,QAAQ,GAAG,CAAC;AAChB,aAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACzB;AAEA,aAAS,YAAY;AACnB,MAAAH,MAAK;AACL,qBAAe,QAAQY;AACvB,aAAO,UAAU;AAAA,IACnB;AAEA,aAASA,WAAU,QAAQ,KAAK;AAC9B,UAAI,IAAI,UAAU,CAAC,QAAQ,GAAG,CAAC,GAAG,IAAI,QAAQ,QAAQ,GAAG;AACzD,qBAAeZ,KAAIC,KAAIC,UAAS,IAAIC,KAAI,IAAIH,MAAK,EAAE,CAAC,GAAGC,MAAK,EAAE,CAAC,GAAGC,WAAU,QAAQ,KAAK,EAAE,CAAC,GAAGC,MAAK,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,UAAU,MAAM;AACrI,aAAO,MAAMH,KAAIC,GAAE;AAAA,IACrB;AAEA,aAAS,UAAU;AACjB,qBAAe,QAAQ;AACvB,aAAO,QAAQ;AAAA,IACjB;AAEA,aAAS,YAAY;AACnB,gBAAU;AACV,qBAAe,QAAQ;AACvB,qBAAe,UAAU;AAAA,IAC3B;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,MAAAW,WAAUH,YAAW,QAAQ,GAAG,GAAGC,OAAMV,KAAIW,OAAMV,KAAI,MAAM,IAAI,MAAME,KAAI,MAAM;AACjF,qBAAe,QAAQS;AAAA,IACzB;AAEA,aAAS,UAAU;AACjB,qBAAeZ,KAAIC,KAAIC,UAAS,IAAIC,KAAI,IAAIO,MAAKC,MAAKF,WAAU,KAAK,KAAK,KAAK,UAAU,MAAM;AAC/F,qBAAe,UAAU;AACzB,cAAQ;AAAA,IACV;AAEA,WAAO;AAAA,EACT;AACF;;;AC1FA,IAAI,mBAAmB,YAAY;AAAA,EACjC,OAAO,SAAS,GAAG,GAAG;AACpB,SAAK,OAAO,MAAM,IAAI,SAAS,IAAI,OAAO;AAAA,EAC5C;AACF,CAAC;AAED,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,YAAY;AAAA,IACjB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,IAAI,OAAO,GAAG,CAAC;AACnB,aAAO,KAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe,GAAG,IAAI,IAAI,IAAI,IAAI;AACzC,WAASI,WAAU,GAAG,GAAG;AACvB,SAAK;AAAI,SAAK;AACd,WAAO,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,EAChC;AACA,EAAAA,WAAU,SAAS,SAAS,GAAG,GAAG;AAChC,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE;AAAA,EAC9C;AACA,SAAOA;AACT;AAEA,SAAS,qBAAqB,GAAG,IAAI,IAAI,IAAI,IAAI,OAAO;AACtD,MAAI,CAAC;AAAO,WAAO,eAAe,GAAG,IAAI,IAAI,IAAI,EAAE;AACnD,MAAI,WAAW,IAAI,KAAK,GACpB,WAAW,IAAI,KAAK,GACpB,IAAI,WAAW,GACf,IAAI,WAAW,GACf,KAAK,WAAW,GAChB,KAAK,WAAW,GAChB,MAAM,WAAW,KAAK,WAAW,MAAM,GACvC,MAAM,WAAW,KAAK,WAAW,MAAM;AAC3C,WAASA,WAAU,GAAG,GAAG;AACvB,SAAK;AAAI,SAAK;AACd,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,EAChD;AACA,EAAAA,WAAU,SAAS,SAAS,GAAG,GAAG;AAChC,WAAO,CAAC,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,EAAE;AAAA,EAClE;AACA,SAAOA;AACT;AAEe,SAAR,WAA4B,SAAS;AAC1C,SAAO,kBAAkB,WAAW;AAAE,WAAO;AAAA,EAAS,CAAC,EAAE;AAC3D;AAEO,SAAS,kBAAkB,WAAW;AAC3C,MAAI,SACA,IAAI,KACJ,IAAI,KAAK,IAAI,KACb,SAAS,GAAG,MAAM,GAClB,cAAc,GAAG,WAAW,GAAG,aAAa,GAAG,QAC/C,QAAQ,GACR,KAAK,GACL,KAAK,GACL,QAAQ,MAAM,UAAU,sBACxBC,MAAK,MAAMC,KAAIC,KAAIC,KAAI,WAAW,kBAClC,SAAS,KACT,iBACA,kBACA,wBACA,OACA;AAEJ,WAASC,YAAW,OAAO;AACzB,WAAO,uBAAuB,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAAA,EACtE;AAEA,WAAS,OAAO,OAAO;AACrB,YAAQ,uBAAuB,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACxD,WAAO,SAAS,CAAC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAAA,EACzD;AAEA,EAAAA,YAAW,SAAS,SAAS,QAAQ;AACnC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,iBAAiB,gBAAgB,MAAM,EAAE,QAAQ,gBAAgB,SAAS,cAAc,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EAC7J;AAEA,EAAAA,YAAW,UAAU,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,UAAU,GAAG,QAAQ,QAAW,MAAM,KAAK;AAAA,EACxE;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,WAAW,GAAGJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,MAAM,KAAK;AAAA,EAChF;AAEA,EAAAC,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,UAAU,CAAC,IAAIC,gBAAW,QAAQ,IAAI,OAAO,KAAK,QAAQ,MAAM,uBAAmB,MAAM,KAAK,QAAQ;AAAA,EACnI;AAEA,EAAAD,YAAW,aAAa,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,WAAW,KAAK,QAAQJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,oBAAY,cAAcH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACxN;AAEA,EAAAC,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,IAAI,CAAC,GAAG,SAAS,KAAK;AAAA,EACnD;AAEA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,CAAC;AAAA,EACtE;AAEA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,SAAS,EAAE,CAAC,IAAI,MAAM,SAAS,MAAM,EAAE,CAAC,IAAI,MAAM,SAAS,SAAS,KAAK,CAAC,SAAS,SAAS,MAAM,OAAO;AAAA,EACtI;AAEA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,cAAc,EAAE,CAAC,IAAI,MAAM,SAAS,WAAW,EAAE,CAAC,IAAI,MAAM,SAAS,aAAa,EAAE,SAAS,IAAI,EAAE,CAAC,IAAI,MAAM,UAAU,GAAG,SAAS,KAAK,CAAC,cAAc,SAAS,WAAW,SAAS,aAAa,OAAO;AAAA,EACtO;AAEA,EAAAA,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,IAAI,MAAM,SAAS,SAAS,KAAK,QAAQ;AAAA,EAC9E;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,EACjE;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,EACjE;AAEA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,kBAAkB,iBAAS,kBAAkB,SAAS,IAAI,CAAC,GAAG,MAAM,KAAK,KAAK,MAAM;AAAA,EACjH;AAEA,EAAAA,YAAW,YAAY,SAAS,QAAQE,SAAQ;AAC9C,WAAO,UAAUF,aAAY,QAAQE,OAAM;AAAA,EAC7C;AAEA,EAAAF,YAAW,UAAU,SAAS,MAAME,SAAQ;AAC1C,WAAO,QAAQF,aAAY,MAAME,OAAM;AAAA,EACzC;AAEA,EAAAF,YAAW,WAAW,SAAS,OAAOE,SAAQ;AAC5C,WAAO,SAASF,aAAY,OAAOE,OAAM;AAAA,EAC3C;AAEA,EAAAF,YAAW,YAAY,SAAS,QAAQE,SAAQ;AAC9C,WAAO,UAAUF,aAAY,QAAQE,OAAM;AAAA,EAC7C;AAEA,WAAS,WAAW;AAClB,QAAI,SAAS,qBAAqB,GAAG,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,MAAM,MAAM,QAAQ,QAAQ,GAAG,CAAC,GACtFP,aAAY,qBAAqB,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK;AACnF,aAAS,cAAc,aAAa,UAAU,UAAU;AACxD,uBAAmB,gBAAQ,SAASA,UAAS;AAC7C,6BAAyB,gBAAQ,QAAQ,gBAAgB;AACzD,sBAAkB,iBAAS,kBAAkB,MAAM;AACnD,WAAO,MAAM;AAAA,EACf;AAEA,WAAS,QAAQ;AACf,YAAQ,cAAc;AACtB,WAAOK;AAAA,EACT;AAEA,SAAO,WAAW;AAChB,cAAU,UAAU,MAAM,MAAM,SAAS;AACzC,IAAAA,YAAW,SAAS,QAAQ,UAAU;AACtC,WAAO,SAAS;AAAA,EAClB;AACF;;;AC7KO,SAAS,gBAAgB,WAAW;AACzC,MAAIG,QAAO,GACPC,QAAO,KAAK,GACZ,IAAI,kBAAkB,SAAS,GAC/B,IAAI,EAAED,OAAMC,KAAI;AAEpB,IAAE,YAAY,SAAS,GAAG;AACxB,WAAO,UAAU,SAAS,EAAED,QAAO,EAAE,CAAC,IAAI,SAASC,QAAO,EAAE,CAAC,IAAI,OAAO,IAAI,CAACD,QAAO,SAASC,QAAO,OAAO;AAAA,EAC7G;AAEA,SAAO;AACT;;;ACZO,SAAS,wBAAwBC,OAAM;AAC5C,MAAIC,WAAU,IAAID,KAAI;AAEtB,WAAS,QAAQ,QAAQ,KAAK;AAC5B,WAAO,CAAC,SAASC,UAAS,IAAI,GAAG,IAAIA,QAAO;AAAA,EAC9C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,WAAO,CAAC,IAAIA,UAAS,KAAK,IAAIA,QAAO,CAAC;AAAA,EACxC;AAEA,SAAO;AACT;;;ACVO,SAAS,kBAAkBC,KAAIC,KAAI;AACxC,MAAI,MAAM,IAAID,GAAE,GAAG,KAAK,MAAM,IAAIC,GAAE,KAAK;AAGzC,MAAI,IAAI,CAAC,IAAI;AAAS,WAAO,wBAAwBD,GAAE;AAEvD,MAAI,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI;AAEhD,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;AACnC,WAAO,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;AAAA,EAC1C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,MAAM,KAAK,GACX,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG;AACrC,QAAI,MAAM,IAAI;AACZ,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG;AAC9B,WAAO,CAAC,IAAI,GAAG,MAAM,KAAK,IAAI,IAAI,MAAM,OAAO,IAAI,MAAM,IAAI,EAAE,CAAC;AAAA,EAClE;AAEA,SAAO;AACT;AAEe,SAAR,yBAAmB;AACxB,SAAO,gBAAgB,iBAAiB,EACnC,MAAM,OAAO,EACb,OAAO,CAAC,GAAG,OAAO,CAAC;AAC1B;;;AC9Be,SAAR,iBAAmB;AACxB,SAAO,uBAAe,EACjB,UAAU,CAAC,MAAM,IAAI,CAAC,EACtB,MAAM,IAAI,EACV,UAAU,CAAC,KAAK,GAAG,CAAC,EACpB,OAAO,CAAC,IAAI,CAAC,CAAC,EACd,OAAO,CAAC,MAAM,IAAI,CAAC;AAC1B;;;ACFA,SAAS,UAAU,SAAS;AAC1B,MAAI,IAAI,QAAQ;AAChB,SAAO;AAAA,IACL,OAAO,SAAS,GAAG,GAAG;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI;AAAG,gBAAQ,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,IAAG;AAAA,IAC5E,QAAQ,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI;AAAG,gBAAQ,CAAC,EAAE,OAAO;AAAA,IAAG;AAAA,IACtE,WAAW,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI;AAAG,gBAAQ,CAAC,EAAE,UAAU;AAAA,IAAG;AAAA,IAC5E,SAAS,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI;AAAG,gBAAQ,CAAC,EAAE,QAAQ;AAAA,IAAG;AAAA,IACxE,cAAc,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI;AAAG,gBAAQ,CAAC,EAAE,aAAa;AAAA,IAAG;AAAA,IAClF,YAAY,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI;AAAG,gBAAQ,CAAC,EAAE,WAAW;AAAA,IAAG;AAAA,EAChF;AACF;AAOe,SAAR,oBAAmB;AACxB,MAAI,OACA,aACA,UAAU,eAAO,GAAG,cACpB,SAAS,uBAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,aACnF,SAAS,uBAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,aAClF,OAAO,cAAc,EAAC,OAAO,SAAS,GAAG,GAAG;AAAE,YAAQ,CAAC,GAAG,CAAC;AAAA,EAAG,EAAC;AAEnE,WAAS,UAAUE,cAAa;AAC9B,QAAI,IAAIA,aAAY,CAAC,GAAG,IAAIA,aAAY,CAAC;AACzC,WAAO,QAAQ,OACV,aAAa,MAAM,GAAG,CAAC,GAAG,WACvB,YAAY,MAAM,GAAG,CAAC,GAAG,WACzB,YAAY,MAAM,GAAG,CAAC,GAAG;AAAA,EACnC;AAEA,YAAU,SAAS,SAASA,cAAa;AACvC,QAAI,IAAI,QAAQ,MAAM,GAClB,IAAI,QAAQ,UAAU,GACtB,KAAKA,aAAY,CAAC,IAAI,EAAE,CAAC,KAAK,GAC9B,KAAKA,aAAY,CAAC,IAAI,EAAE,CAAC,KAAK;AAClC,YAAQ,KAAK,QAAS,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,SACzD,KAAK,SAAS,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,SACvD,SAAS,OAAOA,YAAW;AAAA,EACnC;AAEA,YAAU,SAAS,SAAS,QAAQ;AAClC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,UAAU,CAAC,QAAQ,OAAO,cAAc,MAAM,GAAG,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA,EACzJ;AAEA,YAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU;AAAQ,aAAO,QAAQ,UAAU;AAChD,YAAQ,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC;AAC7D,WAAO,MAAM;AAAA,EACf;AAEA,YAAU,QAAQ,SAAS,GAAG;AAC5B,QAAI,CAAC,UAAU;AAAQ,aAAO,QAAQ,MAAM;AAC5C,YAAQ,MAAM,CAAC,GAAG,OAAO,MAAM,IAAI,IAAI,GAAG,OAAO,MAAM,CAAC;AACxD,WAAO,UAAU,UAAU,QAAQ,UAAU,CAAC;AAAA,EAChD;AAEA,YAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU;AAAQ,aAAO,QAAQ,UAAU;AAChD,QAAI,IAAI,QAAQ,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAE5C,mBAAe,QACV,UAAU,CAAC,EACX,WAAW,CAAC,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC,EAC3E,OAAO,WAAW;AAEvB,kBAAc,OACT,UAAU,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EACxC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,OAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,CAAC,CAAC,EACnH,OAAO,WAAW;AAEvB,kBAAc,OACT,UAAU,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EACxC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,CAAC,CAAC,EACnH,OAAO,WAAW;AAEvB,WAAO,MAAM;AAAA,EACf;AAEA,YAAU,YAAY,SAAS,QAAQC,SAAQ;AAC7C,WAAO,UAAU,WAAW,QAAQA,OAAM;AAAA,EAC5C;AAEA,YAAU,UAAU,SAAS,MAAMA,SAAQ;AACzC,WAAO,QAAQ,WAAW,MAAMA,OAAM;AAAA,EACxC;AAEA,YAAU,WAAW,SAAS,OAAOA,SAAQ;AAC3C,WAAO,SAAS,WAAW,OAAOA,OAAM;AAAA,EAC1C;AAEA,YAAU,YAAY,SAAS,QAAQA,SAAQ;AAC7C,WAAO,UAAU,WAAW,QAAQA,OAAM;AAAA,EAC5C;AAEA,WAAS,QAAQ;AACf,YAAQ,cAAc;AACtB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,MAAM,IAAI;AAC7B;;;AC5GO,SAAS,aAAa,OAAO;AAClC,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,KAAK,IAAI,CAAC,GACV,KAAK,IAAI,CAAC,GACV,IAAI,MAAM,KAAK,EAAE;AACjB,QAAI,MAAM;AAAU,aAAO,CAAC,GAAG,CAAC;AACpC,WAAO;AAAA,MACL,IAAI,KAAK,IAAI,CAAC;AAAA,MACd,IAAI,IAAI,CAAC;AAAA,IACX;AAAA,EACF;AACF;AAEO,SAAS,gBAAgBC,QAAO;AACrC,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GACtB,IAAIA,OAAM,CAAC,GACX,KAAK,IAAI,CAAC,GACV,KAAK,IAAI,CAAC;AACd,WAAO;AAAA,MACL,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,MACpB,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,IACtB;AAAA,EACF;AACF;;;ACtBO,IAAI,wBAAwB,aAAa,SAAS,MAAM;AAC7D,SAAO,KAAK,KAAK,IAAI,KAAK;AAC5B,CAAC;AAED,sBAAsB,SAAS,gBAAgB,SAAS,GAAG;AACzD,SAAO,IAAI,KAAK,IAAI,CAAC;AACvB,CAAC;AAEc,SAAR,6BAAmB;AACxB,SAAO,WAAW,qBAAqB,EAClC,MAAM,MAAM,EACZ,UAAU,MAAM,IAAI;AAC3B;;;ACZO,IAAI,0BAA0B,aAAa,SAAS,GAAG;AAC5D,UAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC;AACnC,CAAC;AAED,wBAAwB,SAAS,gBAAgB,SAAS,GAAG;AAC3D,SAAO;AACT,CAAC;AAEc,SAAR,+BAAmB;AACxB,SAAO,WAAW,uBAAuB,EACpC,MAAM,OAAO,EACb,UAAU,MAAM,IAAI;AAC3B;;;ACZO,SAAS,YAAY,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAQ,IAAI,KAAK,SAAS,OAAO,CAAC,CAAC,CAAC;AAC9C;AAEA,YAAY,SAAS,SAAS,GAAG,GAAG;AAClC,SAAO,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM;AACtC;AAEe,SAAR,mBAAmB;AACxB,SAAO,mBAAmB,WAAW,EAChC,MAAM,MAAM,GAAG;AACtB;AAEO,SAAS,mBAAmB,SAAS;AAC1C,MAAI,IAAI,WAAW,OAAO,GACtB,SAAS,EAAE,QACX,QAAQ,EAAE,OACV,YAAY,EAAE,WACd,aAAa,EAAE,YACfC,MAAK,MAAMC,KAAIC,KAAIC;AAEvB,IAAE,QAAQ,SAAS,GAAG;AACpB,WAAO,UAAU,UAAU,MAAM,CAAC,GAAG,OAAO,KAAK,MAAM;AAAA,EACzD;AAEA,IAAE,YAAY,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,UAAU,CAAC,GAAG,OAAO,KAAK,UAAU;AAAA,EACjE;AAEA,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,OAAO,KAAK,OAAO;AAAA,EAC3D;AAEA,IAAE,aAAa,SAAS,GAAG;AACzB,WAAO,UAAU,UAAW,KAAK,OAAOH,MAAKC,MAAKC,MAAKC,MAAK,QAAQH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,OAAO,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACvL;AAEA,WAAS,SAAS;AAChB,QAAI,IAAI,KAAK,MAAM,GACf,IAAI,EAAE,iBAAS,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,WAAO,WAAWH,OAAM,OAClB,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,YAAY,cAC3D,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGA,GAAE,GAAGC,GAAE,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,GAAGC,GAAE,CAAC,IAC3D,CAAC,CAACH,KAAI,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,CAAC,GAAG,CAACC,KAAI,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,CAAC,CAAC,CAAC;AAAA,EACpE;AAEA,SAAO,OAAO;AAChB;;;AC/CA,SAAS,KAAK,GAAG;AACf,SAAO,KAAK,SAAS,KAAK,CAAC;AAC7B;AAEO,SAAS,kBAAkBC,KAAIC,KAAI;AACxC,MAAI,MAAM,IAAID,GAAE,GACZ,IAAIA,QAAOC,MAAK,IAAID,GAAE,IAAI,IAAI,MAAM,IAAIC,GAAE,CAAC,IAAI,IAAI,KAAKA,GAAE,IAAI,KAAKD,GAAE,CAAC,GACtE,IAAI,MAAM,IAAI,KAAKA,GAAE,GAAG,CAAC,IAAI;AAEjC,MAAI,CAAC;AAAG,WAAO;AAEf,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,IAAI,GAAG;AAAE,UAAI,IAAI,CAAC,SAAS;AAAS,YAAI,CAAC,SAAS;AAAA,IAAS,OAC1D;AAAE,UAAI,IAAI,SAAS;AAAS,YAAI,SAAS;AAAA,IAAS;AACvD,QAAI,IAAI,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC;AAC1B,WAAO,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,EAC5C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,GAChD,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE;AACjC,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,WAAO,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM;AAAA,EACrD;AAEA,SAAO;AACT;AAEe,SAAR,yBAAmB;AACxB,SAAO,gBAAgB,iBAAiB,EACnC,MAAM,KAAK,EACX,UAAU,CAAC,IAAI,EAAE,CAAC;AACzB;;;ACnCO,SAAS,mBAAmB,QAAQ,KAAK;AAC9C,SAAO,CAAC,QAAQ,GAAG;AACrB;AAEA,mBAAmB,SAAS;AAEb,SAAR,0BAAmB;AACxB,SAAO,WAAW,kBAAkB,EAC/B,MAAM,MAAM;AACnB;;;ACPO,SAAS,oBAAoBE,KAAIC,KAAI;AAC1C,MAAI,MAAM,IAAID,GAAE,GACZ,IAAIA,QAAOC,MAAK,IAAID,GAAE,KAAK,MAAM,IAAIC,GAAE,MAAMA,MAAKD,MAClD,IAAI,MAAM,IAAIA;AAElB,MAAI,IAAI,CAAC,IAAI;AAAS,WAAO;AAE7B,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,KAAK,IAAI,GAAG,KAAK,IAAI;AACzB,WAAO,CAAC,KAAK,IAAI,EAAE,GAAG,IAAI,KAAK,IAAI,EAAE,CAAC;AAAA,EACxC;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,KAAK,IAAI,GACT,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE;AACnC,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,WAAO,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,CAAC;AAAA,EACpD;AAEA,SAAO;AACT;AAEe,SAAR,2BAAmB;AACxB,SAAO,gBAAgB,mBAAmB,EACrC,MAAM,OAAO,EACb,OAAO,CAAC,GAAG,OAAO,CAAC;AAC1B;;;AC5BA,IAAI,KAAK;AAAT,IACI,KAAK;AADT,IAEI,KAAK;AAFT,IAGI,KAAK;AAHT,IAII,IAAI,KAAK,CAAC,IAAI;AAJlB,IAKI,aAAa;AAEV,SAAS,cAAc,QAAQ,KAAK;AACzC,MAAI,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AACvD,SAAO;AAAA,IACL,SAAS,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,IACnE,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EACtC;AACF;AAEA,cAAc,SAAS,SAAS,GAAG,GAAG;AACpC,MAAI,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AACtC,WAAS,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,YAAY,EAAE,GAAG;AACnD,SAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO;AAChD,UAAM,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACjD,SAAK,QAAQ,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AAClD,QAAI,IAAI,KAAK,IAAI;AAAU;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,IAChE,KAAK,IAAI,CAAC,IAAI,CAAC;AAAA,EACjB;AACF;AAEe,SAAR,qBAAmB;AACxB,SAAO,WAAW,aAAa,EAC1B,MAAM,OAAO;AACpB;;;AC/BO,SAAS,YAAY,GAAG,GAAG;AAChC,MAAI,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI;AAC9B,SAAO,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC;AAEA,YAAY,SAAS,gBAAgB,IAAI;AAE1B,SAAR,mBAAmB;AACxB,SAAO,WAAW,WAAW,EACxB,MAAM,OAAO,EACb,UAAU,EAAE;AACnB;;;ACTe,SAARE,oBAAmB;AACxB,MAAI,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GACpC,QAAQ,GAAG,IAAI,IACfC,MAAK,MAAMC,KAAIC,KAAIC,KACnB,KAAK,GAAG,KAAK,GACbC,aAAY,YAAY;AAAA,IACtB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,IAAIC,YAAW,CAAC,GAAG,CAAC,CAAC;AACzB,WAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC,GACD,WAAW,kBACX,OACA;AAEJ,WAAS,QAAQ;AACf,SAAK,IAAI;AACT,SAAK,IAAI;AACT,YAAQ,cAAc;AACtB,WAAOA;AAAA,EACT;AAEA,WAASA,YAAY,GAAG;AACtB,QAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAC9B,QAAI,OAAO;AACT,UAAI,IAAI,IAAI,KAAK,IAAI;AACrB,UAAI,IAAI,KAAK,IAAI;AACjB,UAAI;AAAA,IACN;AACA,WAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,EACxB;AACA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,QAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAC9B,QAAI,OAAO;AACT,UAAI,IAAI,IAAI,KAAK,IAAI;AACrB,UAAI,IAAI,KAAK,IAAI;AACjB,UAAI;AAAA,IACN;AACA,WAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,EACxB;AACA,EAAAA,YAAW,SAAS,SAAS,QAAQ;AACnC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQD,WAAU,SAAS,cAAc,MAAM,CAAC;AAAA,EACnG;AACA,EAAAC,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,WAAW,GAAGL,MAAKC,MAAKC,MAAKC,MAAK,MAAM,MAAM,KAAK;AAAA,EAChF;AACA,EAAAE,YAAW,aAAa,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,WAAW,KAAK,QAAQL,MAAKC,MAAKC,MAAKC,MAAK,MAAM,oBAAY,cAAcH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACxN;AACA,EAAAE,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,IAAI,CAAC,GAAG,MAAM,KAAK;AAAA,EAChD;AACA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE;AAAA,EACvE;AACA,EAAAA,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,IAAI,MAAM,SAAS,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,QAAQ;AAAA,EAC7G;AACA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK;AAAA,EAC9D;AACA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK;AAAA,EAC9D;AACA,EAAAA,YAAW,YAAY,SAAS,QAAQC,SAAQ;AAC9C,WAAO,UAAUD,aAAY,QAAQC,OAAM;AAAA,EAC7C;AACA,EAAAD,YAAW,UAAU,SAAS,MAAMC,SAAQ;AAC1C,WAAO,QAAQD,aAAY,MAAMC,OAAM;AAAA,EACzC;AACA,EAAAD,YAAW,WAAW,SAAS,OAAOC,SAAQ;AAC5C,WAAO,SAASD,aAAY,OAAOC,OAAM;AAAA,EAC3C;AACA,EAAAD,YAAW,YAAY,SAAS,QAAQC,SAAQ;AAC9C,WAAO,UAAUD,aAAY,QAAQC,OAAM;AAAA,EAC7C;AAEA,SAAOD;AACT;;;ACjFO,SAAS,iBAAiB,QAAQ,KAAK;AAC5C,MAAI,OAAO,MAAM,KAAK,OAAO,OAAO;AACpC,SAAO;AAAA,IACL,UAAU,SAAS,WAAW,OAAO,QAAQ,YAAY,QAAQ,UAAW,OAAO,UAAW;AAAA,IAC9F,OAAO,WAAW,QAAQ,WAAW,QAAQ,YAAY,WAAW,OAAO,UAAW;AAAA,EACxF;AACF;AAEA,iBAAiB,SAAS,SAAS,GAAG,GAAG;AACvC,MAAI,MAAM,GAAG,IAAI,IAAI;AACrB,KAAG;AACD,QAAI,OAAO,MAAM,KAAK,OAAO,OAAO;AACpC,WAAO,SAAS,OAAO,WAAW,QAAQ,WAAW,QAAQ,YAAY,WAAW,OAAO,UAAW,UAAU,MAC3G,WAAW,QAAQ,WAAW,IAAI,QAAQ,YAAY,IAAI,WAAW,IAAI,OAAO,UAAW,KAAK;AAAA,EACvG,SAAS,IAAI,KAAK,IAAI,WAAW,EAAE,IAAI;AACvC,SAAO;AAAA,IACL,KAAK,UAAU,OAAO,MAAM,QAAQ,YAAY,QAAQ,YAAY,OAAO,OAAO,QAAQ,UAAW,UAAW;AAAA,IAChH;AAAA,EACF;AACF;AAEe,SAAR,wBAAmB;AACxB,SAAO,WAAW,gBAAgB,EAC7B,MAAM,OAAO;AACpB;;;ACvBO,SAAS,gBAAgB,GAAG,GAAG;AACpC,SAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACjC;AAEA,gBAAgB,SAAS,gBAAgB,IAAI;AAE9B,SAAR,uBAAmB;AACxB,SAAO,WAAW,eAAe,EAC5B,MAAM,KAAK,EACX,UAAU,KAAK,OAAO;AAC7B;;;ACVO,SAAS,iBAAiB,GAAG,GAAG;AACrC,MAAI,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI;AAClC,SAAO,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC;AAEA,iBAAiB,SAAS,gBAAgB,SAAS,GAAG;AACpD,SAAO,IAAI,KAAK,CAAC;AACnB,CAAC;AAEc,SAAR,wBAAmB;AACxB,SAAO,WAAW,gBAAgB,EAC7B,MAAM,GAAG,EACT,UAAU,GAAG;AACpB;;;ACdO,SAAS,sBAAsB,QAAQ,KAAK;AACjD,SAAO,CAAC,IAAI,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM;AAC/C;AAEA,sBAAsB,SAAS,SAAS,GAAG,GAAG;AAC5C,SAAO,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM;AACvC;AAEe,SAAR,6BAAmB;AACxB,MAAI,IAAI,mBAAmB,qBAAqB,GAC5C,SAAS,EAAE,QACX,SAAS,EAAE;AAEf,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,EAC/E;AAEA,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,SAAS,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE;AAAA,EACvH;AAEA,SAAO,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,EACnB,MAAM,OAAO;AACpB;;;AC1Be,SAARE,kBAAiB,GAAG;AACzB,SAAO;AACT;;;ACAe,SAARC,mBAAiBC,YAAW;AACjC,MAAIA,cAAa;AAAM,WAAOC;AAC9B,MAAIC,KACAC,KACA,KAAKH,WAAU,MAAM,CAAC,GACtB,KAAKA,WAAU,MAAM,CAAC,GACtB,KAAKA,WAAU,UAAU,CAAC,GAC1B,KAAKA,WAAU,UAAU,CAAC;AAC9B,SAAO,SAAS,OAAO,GAAG;AACxB,QAAI,CAAC;AAAG,MAAAE,MAAKC,MAAK;AAClB,QAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,SAAS,IAAI,MAAM,CAAC;AACjD,WAAO,CAAC,KAAKD,OAAM,MAAM,CAAC,KAAK,KAAK;AACpC,WAAO,CAAC,KAAKC,OAAM,MAAM,CAAC,KAAK,KAAK;AACpC,WAAO,IAAI;AAAG,aAAO,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE;AACtC,WAAO;AAAA,EACT;AACF;;;AClBe,SAAR,gBAAiB,OAAO,GAAG;AAChC,MAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,IAAI;AACjC,SAAO,IAAI,EAAE;AAAG,QAAI,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI;AAClE;;;ACAe,SAAR,gBAAiB,UAAU,GAAG;AACnC,MAAI,OAAO,MAAM;AAAU,QAAI,SAAS,QAAQ,CAAC;AACjD,SAAO,EAAE,SAAS,uBACZ,EAAC,MAAM,qBAAqB,UAAU,EAAE,WAAW,IAAI,SAASC,IAAG;AAAE,WAAO,QAAQ,UAAUA,EAAC;AAAA,EAAG,CAAC,EAAC,IACpG,QAAQ,UAAU,CAAC;AAC3B;AAEA,SAAS,QAAQ,UAAU,GAAG;AAC5B,MAAIC,MAAK,EAAE,IACP,OAAO,EAAE,MACT,aAAa,EAAE,cAAc,OAAO,CAAC,IAAI,EAAE,YAC3C,WAAWC,QAAO,UAAU,CAAC;AACjC,SAAOD,OAAM,QAAQ,QAAQ,OAAO,EAAC,MAAM,WAAW,YAAwB,SAAkB,IAC1F,QAAQ,OAAO,EAAC,MAAM,WAAW,IAAIA,KAAI,YAAwB,SAAkB,IACnF,EAAC,MAAM,WAAW,IAAIA,KAAI,MAAY,YAAwB,SAAkB;AACxF;AAEO,SAASC,QAAO,UAAU,GAAG;AAClC,MAAI,iBAAiBC,mBAAU,SAAS,SAAS,GAC7C,OAAO,SAAS;AAEpB,WAAS,IAAI,GAAG,QAAQ;AACtB,QAAI,OAAO;AAAQ,aAAO,IAAI;AAC9B,aAAS,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,GAAG;AAClE,aAAO,KAAK,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC;AAAA,IACrC;AACA,QAAI,IAAI;AAAG,sBAAQ,QAAQ,CAAC;AAAA,EAC9B;AAEA,WAAS,MAAM,GAAG;AAChB,WAAO,eAAe,CAAC;AAAA,EACzB;AAEA,WAAS,KAAKC,OAAM;AAClB,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,IAAI,GAAG,EAAE;AAAG,UAAIA,MAAK,CAAC,GAAG,MAAM;AAChE,QAAI,OAAO,SAAS;AAAG,aAAO,KAAK,OAAO,CAAC,CAAC;AAC5C,WAAO;AAAA,EACT;AAEA,WAAS,KAAKA,OAAM;AAClB,QAAI,SAAS,KAAKA,KAAI;AACtB,WAAO,OAAO,SAAS;AAAG,aAAO,KAAK,OAAO,CAAC,CAAC;AAC/C,WAAO;AAAA,EACT;AAEA,WAAS,QAAQA,OAAM;AACrB,WAAOA,MAAK,IAAI,IAAI;AAAA,EACtB;AAEA,WAAS,SAASJ,IAAG;AACnB,QAAI,OAAOA,GAAE,MAAMK;AACnB,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAsB,eAAO,EAAC,MAAY,YAAYL,GAAE,WAAW,IAAI,QAAQ,EAAC;AAAA,MACrF,KAAK;AAAS,QAAAK,eAAc,MAAML,GAAE,WAAW;AAAG;AAAA,MAClD,KAAK;AAAc,QAAAK,eAAcL,GAAE,YAAY,IAAI,KAAK;AAAG;AAAA,MAC3D,KAAK;AAAc,QAAAK,eAAc,KAAKL,GAAE,IAAI;AAAG;AAAA,MAC/C,KAAK;AAAmB,QAAAK,eAAcL,GAAE,KAAK,IAAI,IAAI;AAAG;AAAA,MACxD,KAAK;AAAW,QAAAK,eAAc,QAAQL,GAAE,IAAI;AAAG;AAAA,MAC/C,KAAK;AAAgB,QAAAK,eAAcL,GAAE,KAAK,IAAI,OAAO;AAAG;AAAA,MACxD;AAAS,eAAO;AAAA,IAClB;AACA,WAAO,EAAC,MAAY,aAAaK,aAAW;AAAA,EAC9C;AAEA,SAAO,SAAS,CAAC;AACnB;;;ACrEe,SAAR,eAAiB,UAAU,MAAM;AACtC,MAAI,eAAe,CAAC,GAChB,kBAAkB,CAAC,GACnB,gBAAgB,CAAC,GACjB,YAAY,CAAC,GACb,aAAa;AAGjB,OAAK,QAAQ,SAAS,GAAG,GAAG;AAC1B,QAAI,MAAM,SAAS,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG;AACzC,QAAI,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;AAC9C,UAAI,KAAK,EAAE,UAAU,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,CAAC,IAAI;AAAA,IAC1D;AAAA,EACF,CAAC;AAED,OAAK,QAAQ,SAAS,GAAG;AACvB,QAAI,IAAI,KAAK,CAAC,GACVC,SAAQ,EAAE,CAAC,GACX,MAAM,EAAE,CAAC,GACT,GAAG;AAEP,QAAI,IAAI,cAAcA,MAAK,GAAG;AAC5B,aAAO,cAAc,EAAE,GAAG;AAC1B,QAAE,KAAK,CAAC;AACR,QAAE,MAAM;AACR,UAAI,IAAI,gBAAgB,GAAG,GAAG;AAC5B,eAAO,gBAAgB,EAAE,KAAK;AAC9B,YAAI,KAAK,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC;AACjC,wBAAgB,GAAG,QAAQ,EAAE,KAAK,IAAI,cAAc,GAAG,MAAM,EAAE,GAAG,IAAI;AAAA,MACxE,OAAO;AACL,wBAAgB,EAAE,KAAK,IAAI,cAAc,EAAE,GAAG,IAAI;AAAA,MACpD;AAAA,IACF,WAAW,IAAI,gBAAgB,GAAG,GAAG;AACnC,aAAO,gBAAgB,EAAE,KAAK;AAC9B,QAAE,QAAQ,CAAC;AACX,QAAE,QAAQA;AACV,UAAI,IAAI,cAAcA,MAAK,GAAG;AAC5B,eAAO,cAAc,EAAE,GAAG;AAC1B,YAAI,KAAK,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC;AACjC,wBAAgB,GAAG,QAAQ,EAAE,KAAK,IAAI,cAAc,GAAG,MAAM,EAAE,GAAG,IAAI;AAAA,MACxE,OAAO;AACL,wBAAgB,EAAE,KAAK,IAAI,cAAc,EAAE,GAAG,IAAI;AAAA,MACpD;AAAA,IACF,OAAO;AACL,UAAI,CAAC,CAAC;AACN,sBAAgB,EAAE,QAAQA,MAAK,IAAI,cAAc,EAAE,MAAM,GAAG,IAAI;AAAA,IAClE;AAAA,EACF,CAAC;AAED,WAAS,KAAK,GAAG;AACf,QAAI,MAAM,SAAS,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,GAAGC,MAAK,IAAI,CAAC,GAAG;AACtD,QAAI,SAAS;AAAW,WAAK,CAAC,GAAG,CAAC,GAAG,IAAI,QAAQ,SAAS,IAAI;AAAE,WAAG,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA;AAC5F,WAAK,IAAI,IAAI,SAAS,CAAC;AAC5B,WAAO,IAAI,IAAI,CAAC,IAAIA,GAAE,IAAI,CAACA,KAAI,EAAE;AAAA,EACnC;AAEA,WAAS,MAAMC,gBAAeC,kBAAiB;AAC7C,aAAS,KAAKD,gBAAe;AAC3B,UAAI,IAAIA,eAAc,CAAC;AACvB,aAAOC,iBAAgB,EAAE,KAAK;AAC9B,aAAO,EAAE;AACT,aAAO,EAAE;AACT,QAAE,QAAQ,SAAS,GAAG;AAAE,qBAAa,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;AAAA,MAAG,CAAC;AAC3D,gBAAU,KAAK,CAAC;AAAA,IAClB;AAAA,EACF;AAEA,QAAM,eAAe,eAAe;AACpC,QAAM,iBAAiB,aAAa;AACpC,OAAK,QAAQ,SAAS,GAAG;AAAE,QAAI,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI,CAAC;AAAG,gBAAU,KAAK,CAAC,CAAC,CAAC;AAAA,EAAG,CAAC;AAEpF,SAAO;AACT;;;ACrEe,SAAR,aAAiB,UAAU;AAChC,SAAOC,QAAO,UAAU,SAAS,MAAM,MAAM,SAAS,CAAC;AACzD;AAEO,SAAS,SAAS,UAAUA,SAAQC,SAAQ;AACjD,MAAI,MAAM,GAAG;AACb,MAAI,UAAU,SAAS;AAAG,WAAO,YAAY,UAAUD,SAAQC,OAAM;AAAA;AAChE,SAAK,IAAI,GAAG,OAAO,IAAI,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;AAAG,WAAK,CAAC,IAAI;AACnF,SAAO,EAAC,MAAM,mBAAmB,MAAM,eAAO,UAAU,IAAI,EAAC;AAC/D;AAEA,SAAS,YAAY,UAAUD,SAAQC,SAAQ;AAC7C,MAAI,OAAO,CAAC,GACR,aAAa,CAAC,GACd;AAEJ,WAAS,SAAS,GAAG;AACnB,QAAI,IAAI,IAAI,IAAI,CAAC,IAAI;AACrB,KAAC,WAAW,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,EAAC,GAAM,GAAG,KAAI,CAAC;AAAA,EAC9D;AAEA,WAAS,SAASC,OAAM;AACtB,IAAAA,MAAK,QAAQ,QAAQ;AAAA,EACvB;AAEA,WAAS,SAASA,OAAM;AACtB,IAAAA,MAAK,QAAQ,QAAQ;AAAA,EACvB;AAEA,WAAS,SAASA,OAAM;AACtB,IAAAA,MAAK,QAAQ,QAAQ;AAAA,EACvB;AAEA,WAAS,SAAS,GAAG;AACnB,YAAQ,OAAO,GAAG,EAAE,MAAM;AAAA,MACxB,KAAK;AAAsB,UAAE,WAAW,QAAQ,QAAQ;AAAG;AAAA,MAC3D,KAAK;AAAc,iBAAS,EAAE,IAAI;AAAG;AAAA,MACrC,KAAK;AAAA,MAAmB,KAAK;AAAW,iBAAS,EAAE,IAAI;AAAG;AAAA,MAC1D,KAAK;AAAgB,iBAAS,EAAE,IAAI;AAAG;AAAA,IACzC;AAAA,EACF;AAEA,WAASF,OAAM;AAEf,aAAW,QAAQC,WAAU,OACvB,SAAS,OAAO;AAAE,SAAK,KAAK,MAAM,CAAC,EAAE,CAAC;AAAA,EAAG,IACzC,SAAS,OAAO;AAAE,QAAIA,QAAO,MAAM,CAAC,EAAE,GAAG,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC;AAAG,WAAK,KAAK,MAAM,CAAC,EAAE,CAAC;AAAA,EAAG,CAAC;AAEnG,SAAO;AACT;;;ACpDA,IAAIE,QAAO,EAAC,OAAO,MAAM;AAAC,EAAC;AAE3B,SAAS,WAAW;AAClB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3D,QAAI,EAAE,IAAI,UAAU,CAAC,IAAI,OAAQ,KAAK,KAAM,QAAQ,KAAK,CAAC;AAAG,YAAM,IAAI,MAAM,mBAAmB,CAAC;AACjG,MAAE,CAAC,IAAI,CAAC;AAAA,EACV;AACA,SAAO,IAAI,SAAS,CAAC;AACvB;AAEA,SAAS,SAAS,GAAG;AACnB,OAAK,IAAI;AACX;AAEA,SAAS,eAAe,WAAW,OAAO;AACxC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK;AAAG,aAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,QAAI,KAAK,CAAC,MAAM,eAAe,CAAC;AAAG,YAAM,IAAI,MAAM,mBAAmB,CAAC;AACvE,WAAO,EAAC,MAAM,GAAG,KAAU;AAAA,EAC7B,CAAC;AACH;AAEA,SAAS,YAAY,SAAS,YAAY;AAAA,EACxC,aAAa;AAAA,EACb,IAAI,SAAS,UAAU,UAAU;AAC/B,QAAI,IAAI,KAAK,GACT,IAAI,eAAe,WAAW,IAAI,CAAC,GACnC,GACA,IAAI,IACJ,IAAI,EAAE;AAGV,QAAI,UAAU,SAAS,GAAG;AACxB,aAAO,EAAE,IAAI;AAAG,aAAK,KAAK,WAAW,EAAE,CAAC,GAAG,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,IAAI;AAAI,iBAAO;AAC3F;AAAA,IACF;AAIA,QAAI,YAAY,QAAQ,OAAO,aAAa;AAAY,YAAM,IAAI,MAAM,uBAAuB,QAAQ;AACvG,WAAO,EAAE,IAAI,GAAG;AACd,UAAI,KAAK,WAAW,EAAE,CAAC,GAAG;AAAM,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,QAAQ;AAAA,eAC/D,YAAY;AAAM,aAAK,KAAK;AAAG,YAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAC9E;AAEA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,WAAW;AACf,QAAI,OAAO,CAAC,GAAG,IAAI,KAAK;AACxB,aAAS,KAAK;AAAG,WAAK,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM;AACtC,WAAO,IAAI,SAAS,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,SAAS,MAAM,MAAM;AACzB,SAAK,IAAI,UAAU,SAAS,KAAK;AAAG,eAAS,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE;AAAG,aAAK,CAAC,IAAI,UAAU,IAAI,CAAC;AACpH,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI;AAAG,YAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,SAAK,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE;AAAG,QAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACrF;AAAA,EACA,OAAO,SAAS,MAAM,MAAM,MAAM;AAChC,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI;AAAG,YAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,aAAS,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE;AAAG,QAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACzF;AACF;AAEA,SAAS,IAAI,MAAM,MAAM;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAK,IAAI,KAAK,CAAC,GAAG,SAAS,MAAM;AAC/B,aAAO,EAAE;AAAA,IACX;AAAA,EACF;AACF;AAEA,SAAS,IAAI,MAAM,MAAM,UAAU;AACjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,QAAI,KAAK,CAAC,EAAE,SAAS,MAAM;AACzB,WAAK,CAAC,IAAIA,OAAM,OAAO,KAAK,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAChE;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY;AAAM,SAAK,KAAK,EAAC,MAAY,OAAO,SAAQ,CAAC;AAC7D,SAAO;AACT;AAEA,IAAO,mBAAQ;;;ACnFR,IAAI,QAAQ;AAEnB,IAAO,qBAAQ;AAAA,EACb,KAAK;AAAA,EACL;AAAA,EACA,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;;;ACNe,SAAR,kBAAiB,MAAM;AAC5B,MAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,QAAQ,GAAG;AAC/C,MAAI,KAAK,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC,OAAO;AAAS,WAAO,KAAK,MAAM,IAAI,CAAC;AAC9E,SAAO,mBAAW,eAAe,MAAM,IAAI,EAAC,OAAO,mBAAW,MAAM,GAAG,OAAO,KAAI,IAAI;AACxF;;;ACHA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,QAAIC,YAAW,KAAK,eAChB,MAAM,KAAK;AACf,WAAO,QAAQ,SAASA,UAAS,gBAAgB,iBAAiB,QAC5DA,UAAS,cAAc,IAAI,IAC3BA,UAAS,gBAAgB,KAAK,IAAI;AAAA,EAC1C;AACF;AAEA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,WAAO,KAAK,cAAc,gBAAgB,SAAS,OAAO,SAAS,KAAK;AAAA,EAC1E;AACF;AAEe,SAAR,gBAAiB,MAAM;AAC5B,MAAI,WAAW,kBAAU,IAAI;AAC7B,UAAQ,SAAS,QACX,eACA,gBAAgB,QAAQ;AAChC;;;ACxBA,SAAS,OAAO;AAAC;AAEF,SAAR,iBAAiB,UAAU;AAChC,SAAO,YAAY,OAAO,OAAO,WAAW;AAC1C,WAAO,KAAK,cAAc,QAAQ;AAAA,EACpC;AACF;;;ACHe,SAAR,eAAiB,QAAQ;AAC9B,MAAI,OAAO,WAAW;AAAY,aAAS,iBAAS,MAAM;AAE1D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAC/E,YAAI,cAAc;AAAM,kBAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;;;AChBe,SAAR,cAAiB,GAAG;AACzB,SAAO,OAAO,MAAM,YAAY,YAAY,IACxC,IACA,MAAM,KAAK,CAAC;AAClB;;;ACJA,SAAS,QAAQ;AACf,SAAO,CAAC;AACV;AAEe,SAAR,oBAAiB,UAAU;AAChC,SAAO,YAAY,OAAO,QAAQ,WAAW;AAC3C,WAAO,KAAK,iBAAiB,QAAQ;AAAA,EACvC;AACF;;;ACJA,SAAS,SAAS,QAAQ;AACxB,SAAO,WAAW;AAChB,QAAI,QAAQ,OAAO,MAAM,MAAM,SAAS;AACxC,WAAO,SAAS,OAAO,CAAC,IAAI,cAAM,KAAK;AAAA,EACzC;AACF;AAEe,SAAR,kBAAiB,QAAQ;AAC9B,MAAI,OAAO,WAAW;AAAY,aAAS,SAAS,MAAM;AAAA;AACrD,aAAS,oBAAY,MAAM;AAEhC,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,KAAK,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,CAAC;AACzD,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,OAAO;AACzC;;;ACzBe,SAAR,gBAAiB,UAAU;AAChC,SAAO,WAAW;AAChB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AACF;AAEO,SAAS,aAAa,UAAU;AACrC,SAAO,SAAS,MAAM;AACpB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AACF;;;ACRA,IAAI,OAAO,MAAM,UAAU;AAE3B,SAAS,UAAU,OAAO;AACxB,SAAO,WAAW;AAChB,WAAO,KAAK,KAAK,KAAK,UAAU,KAAK;AAAA,EACvC;AACF;AAEA,SAAS,aAAa;AACpB,SAAO,KAAK;AACd;AAEe,SAAR,oBAAiB,OAAO;AAC7B,SAAO,KAAK,OAAO,SAAS,OAAO,aAC7B,UAAU,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AAC5E;;;ACfA,IAAI,SAAS,MAAM,UAAU;AAE7B,SAAS,WAAW;AAClB,SAAO,KAAK;AACd;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW;AAChB,WAAO,OAAO,KAAK,KAAK,UAAU,KAAK;AAAA,EACzC;AACF;AAEe,SAAR,uBAAiB,OAAO;AAC7B,SAAO,KAAK,UAAU,SAAS,OAAO,WAChC,eAAe,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AACjF;;;ACde,SAAR,eAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU;AAAY,YAAQ,gBAAQ,KAAK;AAEtD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;;;ACfe,SAAR,eAAiB,QAAQ;AAC9B,SAAO,IAAI,MAAM,OAAO,MAAM;AAChC;;;ACCe,SAAR,gBAAmB;AACxB,SAAO,IAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,IAAI,cAAM,GAAG,KAAK,QAAQ;AAC7E;AAEO,SAAS,UAAU,QAAQC,QAAO;AACvC,OAAK,gBAAgB,OAAO;AAC5B,OAAK,eAAe,OAAO;AAC3B,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,WAAWA;AAClB;AAEA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,aAAa,SAAS,OAAO;AAAE,WAAO,KAAK,QAAQ,aAAa,OAAO,KAAK,KAAK;AAAA,EAAG;AAAA,EACpF,cAAc,SAAS,OAAO,MAAM;AAAE,WAAO,KAAK,QAAQ,aAAa,OAAO,IAAI;AAAA,EAAG;AAAA,EACrF,eAAe,SAAS,UAAU;AAAE,WAAO,KAAK,QAAQ,cAAc,QAAQ;AAAA,EAAG;AAAA,EACjF,kBAAkB,SAAS,UAAU;AAAE,WAAO,KAAK,QAAQ,iBAAiB,QAAQ;AAAA,EAAG;AACzF;;;ACrBe,SAARC,kBAAiB,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACCA,SAAS,UAAU,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC3D,MAAI,IAAI,GACJ,MACA,cAAc,MAAM,QACpB,aAAa,KAAK;AAKtB,SAAO,IAAI,YAAY,EAAE,GAAG;AAC1B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,WAAW,KAAK,CAAC;AACtB,aAAO,CAAC,IAAI;AAAA,IACd,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAGA,SAAO,IAAI,aAAa,EAAE,GAAG;AAC3B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM,KAAK;AAC9D,MAAI,GACA,MACA,iBAAiB,oBAAI,OACrB,cAAc,MAAM,QACpB,aAAa,KAAK,QAClB,YAAY,IAAI,MAAM,WAAW,GACjC;AAIJ,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,gBAAU,CAAC,IAAI,WAAW,IAAI,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AACpE,UAAI,eAAe,IAAI,QAAQ,GAAG;AAChC,aAAK,CAAC,IAAI;AAAA,MACZ,OAAO;AACL,uBAAe,IAAI,UAAU,IAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAKA,OAAK,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AAC/B,eAAW,IAAI,KAAK,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI;AAChD,QAAI,OAAO,eAAe,IAAI,QAAQ,GAAG;AACvC,aAAO,CAAC,IAAI;AACZ,WAAK,WAAW,KAAK,CAAC;AACtB,qBAAe,OAAO,QAAQ;AAAA,IAChC,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAGA,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,SAAK,OAAO,MAAM,CAAC,MAAO,eAAe,IAAI,UAAU,CAAC,CAAC,MAAM,MAAO;AACpE,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,MAAM,MAAM;AACnB,SAAO,KAAK;AACd;AAEe,SAAR,aAAiB,OAAO,KAAK;AAClC,MAAI,CAAC,UAAU;AAAQ,WAAO,MAAM,KAAK,MAAM,KAAK;AAEpD,MAAI,OAAO,MAAM,UAAU,WACvB,UAAU,KAAK,UACf,SAAS,KAAK;AAElB,MAAI,OAAO,UAAU;AAAY,YAAQC,kBAAS,KAAK;AAEvD,WAAS,IAAI,OAAO,QAAQ,SAAS,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,QAAI,SAAS,QAAQ,CAAC,GAClB,QAAQ,OAAO,CAAC,GAChB,cAAc,MAAM,QACpB,OAAO,cAAM,MAAM,KAAK,QAAQ,UAAU,OAAO,UAAU,GAAG,OAAO,CAAC,GACtE,aAAa,KAAK,QAClB,aAAa,MAAM,CAAC,IAAI,IAAI,MAAM,UAAU,GAC5C,cAAc,OAAO,CAAC,IAAI,IAAI,MAAM,UAAU,GAC9C,YAAY,KAAK,CAAC,IAAI,IAAI,MAAM,WAAW;AAE/C,SAAK,QAAQ,OAAO,YAAY,aAAa,WAAW,MAAM,GAAG;AAKjE,aAAS,KAAK,GAAG,KAAK,GAAG,UAAU,MAAM,KAAK,YAAY,EAAE,IAAI;AAC9D,UAAI,WAAW,WAAW,EAAE,GAAG;AAC7B,YAAI,MAAM;AAAI,eAAK,KAAK;AACxB,eAAO,EAAE,OAAO,YAAY,EAAE,MAAM,EAAE,KAAK;AAAW;AACtD,iBAAS,QAAQ,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAEA,WAAS,IAAI,UAAU,QAAQ,OAAO;AACtC,SAAO,SAAS;AAChB,SAAO,QAAQ;AACf,SAAO;AACT;;;ACjHe,SAAR,eAAmB;AACxB,SAAO,IAAI,UAAU,KAAK,SAAS,KAAK,QAAQ,IAAI,cAAM,GAAG,KAAK,QAAQ;AAC5E;;;ACLe,SAAR,aAAiB,SAAS,UAAU,QAAQ;AACjD,MAAI,QAAQ,KAAK,MAAM,GAAG,SAAS,MAAM,OAAO,KAAK,KAAK;AAC1D,UAAQ,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI,MAAM,OAAO,UAAU,EAAE;AAClF,MAAI,YAAY;AAAM,aAAS,SAAS,MAAM;AAC9C,MAAI,UAAU;AAAM,SAAK,OAAO;AAAA;AAAQ,WAAO,IAAI;AACnD,SAAO,SAAS,SAAS,MAAM,MAAM,MAAM,EAAE,MAAM,IAAI;AACzD;;;ACJe,SAARC,eAAiBC,YAAW;AACjC,MAAI,EAAEA,sBAAqB;AAAY,UAAM,IAAI,MAAM,eAAe;AAEtE,WAAS,UAAU,KAAK,SAAS,UAAUA,WAAU,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACvK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQC,SAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,QAAAA,OAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AAEA,SAAO,IAAI,UAAU,QAAQ,KAAK,QAAQ;AAC5C;;;AClBe,SAAR,gBAAmB;AAExB,WAAS,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,OAAO,QAAQ,EAAE,IAAI,KAAI;AACnE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,SAAS,GAAG,OAAO,MAAM,CAAC,GAAG,MAAM,EAAE,KAAK,KAAI;AAClF,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAI,QAAQ,KAAK,wBAAwB,IAAI,IAAI;AAAG,eAAK,WAAW,aAAa,MAAM,IAAI;AAC3F,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACVe,SAAR,aAAiB,SAAS;AAC/B,MAAI,CAAC;AAAS,cAAU;AAExB,WAAS,YAAY,GAAG,GAAG;AACzB,WAAO,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC;AAAA,EAC1D;AAEA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,aAAa,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,YAAY,WAAW,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AACA,cAAU,KAAK,WAAW;AAAA,EAC5B;AAEA,SAAO,IAAI,UAAU,YAAY,KAAK,QAAQ,EAAE,MAAM;AACxD;AAEA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAC/C;;;ACvBe,SAAR,eAAmB;AACxB,MAAI,WAAW,UAAU,CAAC;AAC1B,YAAU,CAAC,IAAI;AACf,WAAS,MAAM,MAAM,SAAS;AAC9B,SAAO;AACT;;;ACLe,SAAR,gBAAmB;AACxB,SAAO,MAAM,KAAK,IAAI;AACxB;;;ACFe,SAAR,eAAmB;AAExB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC/D,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI;AAAM,eAAO;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;;;ACVe,SAAR,eAAmB;AACxB,MAAI,OAAO;AACX,aAAW,QAAQ;AAAM,MAAE;AAC3B,SAAO;AACT;;;ACJe,SAAR,gBAAmB;AACxB,SAAO,CAAC,KAAK,KAAK;AACpB;;;ACFe,SAAR,aAAiB,UAAU;AAEhC,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC;AAAG,iBAAS,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK;AAAA,IAClE;AAAA,EACF;AAEA,SAAO;AACT;;;ACPA,SAAS,WAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAEA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,SAAK,aAAa,MAAM,KAAK;AAAA,EAC/B;AACF;AAEA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,KAAK;AAAA,EAC3D;AACF;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AAAM,WAAK,gBAAgB,IAAI;AAAA;AACnC,WAAK,aAAa,MAAM,CAAC;AAAA,EAChC;AACF;AAEA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AAAM,WAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA;AAC/D,WAAK,eAAe,SAAS,OAAO,SAAS,OAAO,CAAC;AAAA,EAC5D;AACF;AAEe,SAAR,aAAiB,MAAM,OAAO;AACnC,MAAI,WAAW,kBAAU,IAAI;AAE7B,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,SAAS,QACV,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK,IAClD,KAAK,aAAa,QAAQ;AAAA,EAClC;AAEA,SAAO,KAAK,MAAM,SAAS,OACpB,SAAS,QAAQ,eAAe,aAAe,OAAO,UAAU,aAChE,SAAS,QAAQ,iBAAiB,eAClC,SAAS,QAAQ,iBAAiB,cAAgB,UAAU,KAAK,CAAC;AAC3E;;;ACxDe,SAAR,eAAiB,MAAM;AAC5B,SAAQ,KAAK,iBAAiB,KAAK,cAAc,eACzC,KAAK,YAAY,QAClB,KAAK;AACd;;;ACFA,SAAS,YAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AAEA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,SAAK,MAAM,YAAY,MAAM,OAAO,QAAQ;AAAA,EAC9C;AACF;AAEA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AAAM,WAAK,MAAM,eAAe,IAAI;AAAA;AACxC,WAAK,MAAM,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC/C;AACF;AAEe,SAAR,cAAiB,MAAM,OAAO,UAAU;AAC7C,SAAO,UAAU,SAAS,IACpB,KAAK,MAAM,SAAS,OACd,cAAc,OAAO,UAAU,aAC/B,gBACA,eAAe,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC,IACnE,WAAW,KAAK,KAAK,GAAG,IAAI;AACpC;AAEO,SAAS,WAAW,MAAM,MAAM;AACrC,SAAO,KAAK,MAAM,iBAAiB,IAAI,KAChC,eAAY,IAAI,EAAE,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,IAAI;AAC7E;;;AClCA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;AAEA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,SAAK,IAAI,IAAI;AAAA,EACf;AACF;AAEA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AAAM,aAAO,KAAK,IAAI;AAAA;AAC1B,WAAK,IAAI,IAAI;AAAA,EACpB;AACF;AAEe,SAAR,iBAAiB,MAAM,OAAO;AACnC,SAAO,UAAU,SAAS,IACpB,KAAK,MAAM,SAAS,OAChB,iBAAiB,OAAO,UAAU,aAClC,mBACA,kBAAkB,MAAM,KAAK,CAAC,IAClC,KAAK,KAAK,EAAE,IAAI;AACxB;;;AC3BA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,KAAK,EAAE,MAAM,OAAO;AACpC;AAEA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,aAAa,IAAI,UAAU,IAAI;AAC7C;AAEA,SAAS,UAAU,MAAM;AACvB,OAAK,QAAQ;AACb,OAAK,SAAS,WAAW,KAAK,aAAa,OAAO,KAAK,EAAE;AAC3D;AAEA,UAAU,YAAY;AAAA,EACpB,KAAK,SAAS,MAAM;AAClB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,IAAI,GAAG;AACT,WAAK,OAAO,KAAK,IAAI;AACrB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,KAAK,GAAG;AACV,WAAK,OAAO,OAAO,GAAG,CAAC;AACvB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,WAAO,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACtC;AACF;AAEA,SAAS,WAAW,MAAM,OAAO;AAC/B,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI;AAAG,SAAK,IAAI,MAAM,CAAC,CAAC;AACnC;AAEA,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI;AAAG,SAAK,OAAO,MAAM,CAAC,CAAC;AACtC;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,WAAW;AAChB,eAAW,MAAM,KAAK;AAAA,EACxB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,kBAAc,MAAM,KAAK;AAAA,EAC3B;AACF;AAEA,SAAS,gBAAgB,OAAO,OAAO;AACrC,SAAO,WAAW;AAChB,KAAC,MAAM,MAAM,MAAM,SAAS,IAAI,aAAa,eAAe,MAAM,KAAK;AAAA,EACzE;AACF;AAEe,SAAR,gBAAiB,MAAM,OAAO;AACnC,MAAI,QAAQ,WAAW,OAAO,EAAE;AAEhC,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM;AACrD,WAAO,EAAE,IAAI;AAAG,UAAI,CAAC,KAAK,SAAS,MAAM,CAAC,CAAC;AAAG,eAAO;AACrD,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,OAAO,UAAU,aAC7B,kBAAkB,QAClB,cACA,cAAc,OAAO,KAAK,CAAC;AACnC;;;AC1EA,SAAS,aAAa;AACpB,OAAK,cAAc;AACrB;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,cAAc,KAAK,OAAO,KAAK;AAAA,EACtC;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,KAAK,SAAS,OACf,cAAc,OAAO,UAAU,aAC/B,eACA,cAAc,KAAK,CAAC,IACxB,KAAK,KAAK,EAAE;AACpB;;;ACxBA,SAAS,aAAa;AACpB,OAAK,YAAY;AACnB;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,YAAY,KAAK,OAAO,KAAK;AAAA,EACpC;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,KAAK,SAAS,OACf,cAAc,OAAO,UAAU,aAC/B,eACA,cAAc,KAAK,CAAC,IACxB,KAAK,KAAK,EAAE;AACpB;;;ACxBA,SAAS,QAAQ;AACf,MAAI,KAAK;AAAa,SAAK,WAAW,YAAY,IAAI;AACxD;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,KAAK,KAAK;AACxB;;;ACNA,SAAS,QAAQ;AACf,MAAI,KAAK;AAAiB,SAAK,WAAW,aAAa,MAAM,KAAK,WAAW,UAAU;AACzF;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,KAAK,KAAK;AACxB;;;ACJe,SAAR,eAAiB,MAAM;AAC5B,MAAIC,UAAS,OAAO,SAAS,aAAa,OAAO,gBAAQ,IAAI;AAC7D,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,YAAYA,QAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EACvD,CAAC;AACH;;;ACJA,SAAS,eAAe;AACtB,SAAO;AACT;AAEe,SAAR,eAAiB,MAAM,QAAQ;AACpC,MAAIC,UAAS,OAAO,SAAS,aAAa,OAAO,gBAAQ,IAAI,GACzD,SAAS,UAAU,OAAO,eAAe,OAAO,WAAW,aAAa,SAAS,iBAAS,MAAM;AACpG,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,aAAaA,QAAO,MAAM,MAAM,SAAS,GAAG,OAAO,MAAM,MAAM,SAAS,KAAK,IAAI;AAAA,EAC/F,CAAC;AACH;;;ACbA,SAAS,SAAS;AAChB,MAAI,SAAS,KAAK;AAClB,MAAI;AAAQ,WAAO,YAAY,IAAI;AACrC;AAEe,SAAR,iBAAmB;AACxB,SAAO,KAAK,KAAK,MAAM;AACzB;;;ACPA,SAAS,yBAAyB;AAChC,MAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,SAAS,KAAK;AACjD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AAEA,SAAS,sBAAsB;AAC7B,MAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,SAAS,KAAK;AAChD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AAEe,SAAR,cAAiB,MAAM;AAC5B,SAAO,KAAK,OAAO,OAAO,sBAAsB,sBAAsB;AACxE;;;ACZe,SAAR,cAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,SAAS,YAAY,KAAK,IAC/B,KAAK,KAAK,EAAE;AACpB;;;ACJA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,OAAO;AACrB,aAAS,KAAK,MAAM,OAAO,KAAK,QAAQ;AAAA,EAC1C;AACF;AAEA,SAASC,gBAAe,WAAW;AACjC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK;AAAG,aAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,WAAO,EAAC,MAAM,GAAG,KAAU;AAAA,EAC7B,CAAC;AACH;AAEA,SAAS,SAAS,UAAU;AAC1B,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK;AACd,QAAI,CAAC;AAAI;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,UAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,SAAS,SAAS,EAAE,SAAS,SAAS,MAAM;AACvF,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AAAA,MACxD,OAAO;AACL,WAAG,EAAE,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AACA,QAAI,EAAE;AAAG,SAAG,SAAS;AAAA;AAChB,aAAO,KAAK;AAAA,EACnB;AACF;AAEA,SAAS,MAAM,UAAU,OAAO,SAAS;AACvC,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK,MAAM,GAAG,WAAW,gBAAgB,KAAK;AACvD,QAAI;AAAI,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AACjD,aAAK,IAAI,GAAG,CAAC,GAAG,SAAS,SAAS,QAAQ,EAAE,SAAS,SAAS,MAAM;AAClE,eAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AACtD,eAAK,iBAAiB,EAAE,MAAM,EAAE,WAAW,UAAU,EAAE,UAAU,OAAO;AACxE,YAAE,QAAQ;AACV;AAAA,QACF;AAAA,MACF;AACA,SAAK,iBAAiB,SAAS,MAAM,UAAU,OAAO;AACtD,QAAI,EAAC,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,OAAc,UAAoB,QAAgB;AACjG,QAAI,CAAC;AAAI,WAAK,OAAO,CAAC,CAAC;AAAA;AAClB,SAAG,KAAK,CAAC;AAAA,EAChB;AACF;AAEe,SAAR,WAAiB,UAAU,OAAO,SAAS;AAChD,MAAI,YAAYA,gBAAe,WAAW,EAAE,GAAG,GAAG,IAAI,UAAU,QAAQ;AAExE,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,KAAK,KAAK,KAAK,EAAE;AACrB,QAAI;AAAI,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,aAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACjC,eAAK,IAAI,UAAU,CAAC,GAAG,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM;AAC3D,mBAAO,EAAE;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA;AAAA,EACF;AAEA,OAAK,QAAQ,QAAQ;AACrB,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE;AAAG,SAAK,KAAK,GAAG,UAAU,CAAC,GAAG,OAAO,OAAO,CAAC;AAClE,SAAO;AACT;;;AChEA,SAAS,cAAc,MAAM,MAAM,QAAQ;AACzC,MAAIC,UAAS,eAAY,IAAI,GACzB,QAAQA,QAAO;AAEnB,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,EAChC,OAAO;AACL,YAAQA,QAAO,SAAS,YAAY,OAAO;AAC3C,QAAI;AAAQ,YAAM,UAAU,MAAM,OAAO,SAAS,OAAO,UAAU,GAAG,MAAM,SAAS,OAAO;AAAA;AACvF,YAAM,UAAU,MAAM,OAAO,KAAK;AAAA,EACzC;AAEA,OAAK,cAAc,KAAK;AAC1B;AAEA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,MAAM;AAAA,EACzC;AACF;AAEA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EAChE;AACF;AAEe,SAARC,kBAAiB,MAAM,QAAQ;AACpC,SAAO,KAAK,MAAM,OAAO,WAAW,aAC9B,mBACA,kBAAkB,MAAM,MAAM,CAAC;AACvC;;;ACjCe,UAAR,mBAAoB;AACzB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC;AAAG,cAAM;AAAA,IAC7B;AAAA,EACF;AACF;;;AC6BO,IAAI,OAAO,CAAC,IAAI;AAEhB,SAAS,UAAU,QAAQ,SAAS;AACzC,OAAK,UAAU;AACf,OAAK,WAAW;AAClB;AAEA,SAAS,YAAY;AACnB,SAAO,IAAI,UAAU,CAAC,CAAC,SAAS,eAAe,CAAC,GAAG,IAAI;AACzD;AAEA,SAAS,sBAAsB;AAC7B,SAAO;AACT;AAEA,UAAU,YAAY,UAAU,YAAY;AAAA,EAC1C,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAOC;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,UAAUC;AAAA,EACV,CAAC,OAAO,QAAQ,GAAG;AACrB;AAEA,IAAO,oBAAQ;;;ACvFA,SAARC,gBAAiB,UAAU;AAChC,SAAO,OAAO,aAAa,WACrB,IAAI,UAAU,CAAC,CAAC,SAAS,cAAc,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,IAC9E,IAAI,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI;AACxC;;;ACNA,IAAI,SAAS;AAEE,SAAR,QAAyB;AAC9B,SAAO,IAAI;AACb;AAEA,SAAS,QAAQ;AACf,OAAK,IAAI,OAAO,EAAE,QAAQ,SAAS,EAAE;AACvC;AAEA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,QAAIC,MAAK,KAAK;AACd,WAAO,EAAEA,OAAM;AAAO,UAAI,EAAE,OAAO,KAAK;AAAa;AACrD,WAAO,KAAKA,GAAE;AAAA,EAChB;AAAA,EACA,KAAK,SAAS,MAAM,OAAO;AACzB,WAAO,KAAK,KAAK,CAAC,IAAI;AAAA,EACxB;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,WAAO,KAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,CAAC;AAAA,EAC7C;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,KAAK;AAAA,EACd;AACF;;;AC1Be,SAAR,oBAAiB,OAAO;AAC7B,MAAI;AACJ,SAAO,cAAc,MAAM;AAAa,YAAQ;AAChD,SAAO;AACT;;;ACFe,SAAR,gBAAiB,OAAO,MAAM;AACnC,UAAQ,oBAAY,KAAK;AACzB,MAAI,SAAS;AAAW,WAAO,MAAM;AACrC,MAAI,MAAM;AACR,QAAI,MAAM,KAAK,mBAAmB;AAClC,QAAI,IAAI,gBAAgB;AACtB,UAAI,QAAQ,IAAI,eAAe;AAC/B,YAAM,IAAI,MAAM,SAAS,MAAM,IAAI,MAAM;AACzC,cAAQ,MAAM,gBAAgB,KAAK,aAAa,EAAE,QAAQ,CAAC;AAC3D,aAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,IAC1B;AACA,QAAI,KAAK,uBAAuB;AAC9B,UAAI,OAAO,KAAK,sBAAsB;AACtC,aAAO,CAAC,MAAM,UAAU,KAAK,OAAO,KAAK,YAAY,MAAM,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,IAChG;AAAA,EACF;AACA,SAAO,CAAC,MAAM,OAAO,MAAM,KAAK;AAClC;;;ACfe,SAAR,gBAAiB,OAAO;AAC7B,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;;;ACJe,SAAR,eAAiB,MAAM;AAC5B,MAAIC,QAAO,KAAK,SAAS,iBACrBC,aAAYC,gBAAO,IAAI,EAAE,GAAG,kBAAkB,iBAAS,IAAI;AAC/D,MAAI,mBAAmBF,OAAM;AAC3B,IAAAC,WAAU,GAAG,oBAAoB,iBAAS,IAAI;AAAA,EAChD,OAAO;AACL,IAAAD,MAAK,aAAaA,MAAK,MAAM;AAC7B,IAAAA,MAAK,MAAM,gBAAgB;AAAA,EAC7B;AACF;AAEO,SAAS,QAAQ,MAAM,SAAS;AACrC,MAAIA,QAAO,KAAK,SAAS,iBACrBC,aAAYC,gBAAO,IAAI,EAAE,GAAG,kBAAkB,IAAI;AACtD,MAAI,SAAS;AACX,IAAAD,WAAU,GAAG,cAAc,iBAAS,IAAI;AACxC,eAAW,WAAW;AAAE,MAAAA,WAAU,GAAG,cAAc,IAAI;AAAA,IAAG,GAAG,CAAC;AAAA,EAChE;AACA,MAAI,mBAAmBD,OAAM;AAC3B,IAAAC,WAAU,GAAG,oBAAoB,IAAI;AAAA,EACvC,OAAO;AACL,IAAAD,MAAK,MAAM,gBAAgBA,MAAK;AAChC,WAAOA,MAAK;AAAA,EACd;AACF;;;AC3Be,SAAR,UAA2B,MAAM;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAAG;AAAA,EAAG;AAAA,EAAI;AAAA,EACV,UAAAG;AACF,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAC,OAAO,MAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,aAAa,EAAC,OAAO,aAAa,YAAY,MAAM,cAAc,KAAI;AAAA,IACtE,SAAS,EAAC,OAAO,SAAS,YAAY,MAAM,cAAc,KAAI;AAAA,IAC9D,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,YAAY,EAAC,OAAO,YAAY,YAAY,MAAM,cAAc,KAAI;AAAA,IACpE,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,GAAG,EAAC,OAAO,GAAG,YAAY,MAAM,cAAc,KAAI;AAAA,IAClD,GAAG,EAAC,OAAO,GAAG,YAAY,MAAM,cAAc,KAAI;AAAA,IAClD,IAAI,EAAC,OAAO,IAAI,YAAY,MAAM,cAAc,KAAI;AAAA,IACpD,IAAI,EAAC,OAAO,IAAI,YAAY,MAAM,cAAc,KAAI;AAAA,IACpD,GAAG,EAAC,OAAOA,UAAQ;AAAA,EACrB,CAAC;AACH;AAEA,UAAU,UAAU,KAAK,WAAW;AAClC,MAAI,QAAQ,KAAK,EAAE,GAAG,MAAM,KAAK,GAAG,SAAS;AAC7C,SAAO,UAAU,KAAK,IAAI,OAAO;AACnC;;;AC3BA,IAAI,QAAQ;AAAZ,IACI,UAAU;AADd,IAEI,WAAW;AAFf,IAGI,YAAY;AAHhB,IAII;AAJJ,IAKI;AALJ,IAMI,YAAY;AANhB,IAOI,WAAW;AAPf,IAQI,YAAY;AARhB,IASI,QAAQ,OAAO,gBAAgB,YAAY,YAAY,MAAM,cAAc;AAT/E,IAUI,WAAW,OAAO,WAAW,YAAY,OAAO,wBAAwB,OAAO,sBAAsB,KAAK,MAAM,IAAI,SAAS,GAAG;AAAE,aAAW,GAAG,EAAE;AAAG;AAElJ,SAAS,MAAM;AACpB,SAAO,aAAa,SAAS,QAAQ,GAAG,WAAW,MAAM,IAAI,IAAI;AACnE;AAEA,SAAS,WAAW;AAClB,aAAW;AACb;AAEO,SAAS,QAAQ;AACtB,OAAK,QACL,KAAK,QACL,KAAK,QAAQ;AACf;AAEA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,SAAS,SAAS,UAAU,OAAO,MAAM;AACvC,QAAI,OAAO,aAAa;AAAY,YAAM,IAAI,UAAU,4BAA4B;AACpF,YAAQ,QAAQ,OAAO,IAAI,IAAI,CAAC,SAAS,SAAS,OAAO,IAAI,CAAC;AAC9D,QAAI,CAAC,KAAK,SAAS,aAAa,MAAM;AACpC,UAAI;AAAU,iBAAS,QAAQ;AAAA;AAC1B,mBAAW;AAChB,iBAAW;AAAA,IACb;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,UAAM;AAAA,EACR;AAAA,EACA,MAAM,WAAW;AACf,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAEO,SAAS,MAAM,UAAU,OAAO,MAAM;AAC3C,MAAI,IAAI,IAAI;AACZ,IAAE,QAAQ,UAAU,OAAO,IAAI;AAC/B,SAAO;AACT;AAEO,SAAS,aAAa;AAC3B,MAAI;AACJ,IAAE;AACF,MAAI,IAAI,UAAU;AAClB,SAAO,GAAG;AACR,SAAK,IAAI,WAAW,EAAE,UAAU;AAAG,QAAE,MAAM,KAAK,MAAM,CAAC;AACvD,QAAI,EAAE;AAAA,EACR;AACA,IAAE;AACJ;AAEA,SAAS,OAAO;AACd,cAAY,YAAY,MAAM,IAAI,KAAK;AACvC,UAAQ,UAAU;AAClB,MAAI;AACF,eAAW;AAAA,EACb,UAAE;AACA,YAAQ;AACR,QAAI;AACJ,eAAW;AAAA,EACb;AACF;AAEA,SAAS,OAAO;AACd,MAAIC,OAAM,MAAM,IAAI,GAAG,QAAQA,OAAM;AACrC,MAAI,QAAQ;AAAW,iBAAa,OAAO,YAAYA;AACzD;AAEA,SAAS,MAAM;AACb,MAAI,IAAI,KAAK,UAAU,IAAI,OAAO;AAClC,SAAO,IAAI;AACT,QAAI,GAAG,OAAO;AACZ,UAAI,OAAO,GAAG;AAAO,eAAO,GAAG;AAC/B,WAAK,IAAI,KAAK,GAAG;AAAA,IACnB,OAAO;AACL,WAAK,GAAG,OAAO,GAAG,QAAQ;AAC1B,WAAK,KAAK,GAAG,QAAQ,KAAK,WAAW;AAAA,IACvC;AAAA,EACF;AACA,aAAW;AACX,QAAM,IAAI;AACZ;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI;AAAO;AACX,MAAI;AAAS,cAAU,aAAa,OAAO;AAC3C,MAAI,QAAQ,OAAO;AACnB,MAAI,QAAQ,IAAI;AACd,QAAI,OAAO;AAAU,gBAAU,WAAW,MAAM,OAAO,MAAM,IAAI,IAAI,SAAS;AAC9E,QAAI;AAAU,iBAAW,cAAc,QAAQ;AAAA,EACjD,OAAO;AACL,QAAI,CAAC;AAAU,kBAAY,MAAM,IAAI,GAAG,WAAW,YAAY,MAAM,SAAS;AAC9E,YAAQ,GAAG,SAAS,IAAI;AAAA,EAC1B;AACF;;;AC3Ge,SAAR,gBAAiB,UAAU,OAAO,MAAM;AAC7C,MAAI,IAAI,IAAI;AACZ,UAAQ,SAAS,OAAO,IAAI,CAAC;AAC7B,IAAE,QAAQ,aAAW;AACnB,MAAE,KAAK;AACP,aAAS,UAAU,KAAK;AAAA,EAC1B,GAAG,OAAO,IAAI;AACd,SAAO;AACT;;;ACPA,IAAI,UAAU,iBAAS,SAAS,OAAO,UAAU,WAAW;AAC5D,IAAI,aAAa,CAAC;AAEX,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AAEJ,SAAR,iBAAiB,MAAM,MAAMC,KAAI,OAAO,OAAO,QAAQ;AAC5D,MAAI,YAAY,KAAK;AACrB,MAAI,CAAC;AAAW,SAAK,eAAe,CAAC;AAAA,WAC5BA,OAAM;AAAW;AAC1B,SAAO,MAAMA,KAAI;AAAA,IACf;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM,OAAO;AAAA,IACb,OAAO,OAAO;AAAA,IACd,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,KAAK,MAAMA,KAAI;AAC7B,MAAI,WAAWC,KAAI,MAAMD,GAAE;AAC3B,MAAI,SAAS,QAAQ;AAAS,UAAM,IAAI,MAAM,6BAA6B;AAC3E,SAAO;AACT;AAEO,SAASE,KAAI,MAAMF,KAAI;AAC5B,MAAI,WAAWC,KAAI,MAAMD,GAAE;AAC3B,MAAI,SAAS,QAAQ;AAAS,UAAM,IAAI,MAAM,2BAA2B;AACzE,SAAO;AACT;AAEO,SAASC,KAAI,MAAMD,KAAI;AAC5B,MAAI,WAAW,KAAK;AACpB,MAAI,CAAC,YAAY,EAAE,WAAW,SAASA,GAAE;AAAI,UAAM,IAAI,MAAM,sBAAsB;AACnF,SAAO;AACT;AAEA,SAAS,OAAO,MAAMA,KAAI,MAAM;AAC9B,MAAI,YAAY,KAAK,cACjB;AAIJ,YAAUA,GAAE,IAAI;AAChB,OAAK,QAAQ,MAAM,UAAU,GAAG,KAAK,IAAI;AAEzC,WAAS,SAAS,SAAS;AACzB,SAAK,QAAQ;AACb,SAAK,MAAM,QAAQG,QAAO,KAAK,OAAO,KAAK,IAAI;AAG/C,QAAI,KAAK,SAAS;AAAS,MAAAA,OAAM,UAAU,KAAK,KAAK;AAAA,EACvD;AAEA,WAASA,OAAM,SAAS;AACtB,QAAI,GAAG,GAAG,GAAG;AAGb,QAAI,KAAK,UAAU;AAAW,aAAO,KAAK;AAE1C,SAAK,KAAK,WAAW;AACnB,UAAI,UAAU,CAAC;AACf,UAAI,EAAE,SAAS,KAAK;AAAM;AAK1B,UAAI,EAAE,UAAU;AAAS,eAAO,gBAAQA,MAAK;AAG7C,UAAI,EAAE,UAAU,SAAS;AACvB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,aAAa,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AAC5D,eAAO,UAAU,CAAC;AAAA,MACpB,WAGS,CAAC,IAAIH,KAAI;AAChB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,UAAU,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AACzD,eAAO,UAAU,CAAC;AAAA,MACpB;AAAA,IACF;AAMA,oBAAQ,WAAW;AACjB,UAAI,KAAK,UAAU,SAAS;AAC1B,aAAK,QAAQ;AACb,aAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,IAAI;AAC9C,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAID,SAAK,QAAQ;AACb,SAAK,GAAG,KAAK,SAAS,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AACjE,QAAI,KAAK,UAAU;AAAU;AAC7B,SAAK,QAAQ;AAGb,YAAQ,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM;AACvC,SAAK,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,CAAC,EAAE,MAAM,KAAK,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK,GAAG;AAC7E,cAAM,EAAE,CAAC,IAAI;AAAA,MACf;AAAA,IACF;AACA,UAAM,SAAS,IAAI;AAAA,EACrB;AAEA,WAAS,KAAK,SAAS;AACrB,QAAI,IAAI,UAAU,KAAK,WAAW,KAAK,KAAK,KAAK,MAAM,UAAU,KAAK,QAAQ,KAAK,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAC9H,IAAI,IACJ,IAAI,MAAM;AAEd,WAAO,EAAE,IAAI,GAAG;AACd,YAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,IACvB;AAGA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,GAAG,KAAK,OAAO,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AAC/D,WAAK;AAAA,IACP;AAAA,EACF;AAEA,WAAS,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,MAAM,KAAK;AAChB,WAAO,UAAUA,GAAE;AACnB,aAAS,KAAK;AAAW;AACzB,WAAO,KAAK;AAAA,EACd;AACF;;;ACtJe,SAAR,kBAAiB,MAAM,MAAM;AAClC,MAAI,YAAY,KAAK,cACjB,UACA,QACAI,SAAQ,MACR;AAEJ,MAAI,CAAC;AAAW;AAEhB,SAAO,QAAQ,OAAO,OAAO,OAAO;AAEpC,OAAK,KAAK,WAAW;AACnB,SAAK,WAAW,UAAU,CAAC,GAAG,SAAS,MAAM;AAAE,MAAAA,SAAQ;AAAO;AAAA,IAAU;AACxE,aAAS,SAAS,QAAQ,YAAY,SAAS,QAAQ;AACvD,aAAS,QAAQ;AACjB,aAAS,MAAM,KAAK;AACpB,aAAS,GAAG,KAAK,SAAS,cAAc,UAAU,MAAM,KAAK,UAAU,SAAS,OAAO,SAAS,KAAK;AACrG,WAAO,UAAU,CAAC;AAAA,EACpB;AAEA,MAAIA;AAAO,WAAO,KAAK;AACzB;;;ACrBe,SAARC,mBAAiB,MAAM;AAC5B,SAAO,KAAK,KAAK,WAAW;AAC1B,sBAAU,MAAM,IAAI;AAAA,EACtB,CAAC;AACH;;;ACJA,SAAS,YAAYC,KAAI,MAAM;AAC7B,MAAI,QAAQ;AACZ,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,QAAQ,SAAS;AAKrB,QAAI,UAAU,QAAQ;AACpB,eAAS,SAAS;AAClB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,mBAAS,OAAO,MAAM;AACtB,iBAAO,OAAO,GAAG,CAAC;AAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,QAAQ;AAAA,EACnB;AACF;AAEA,SAAS,cAAcA,KAAI,MAAM,OAAO;AACtC,MAAI,QAAQ;AACZ,MAAI,OAAO,UAAU;AAAY,UAAM,IAAI;AAC3C,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,QAAQ,SAAS;AAKrB,QAAI,UAAU,QAAQ;AACpB,gBAAU,SAAS,OAAO,MAAM;AAChC,eAAS,IAAI,EAAC,MAAY,MAAY,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7E,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,iBAAO,CAAC,IAAI;AACZ;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM;AAAG,eAAO,KAAK,CAAC;AAAA,IAC5B;AAEA,aAAS,QAAQ;AAAA,EACnB;AACF;AAEe,SAAR,cAAiB,MAAM,OAAO;AACnC,MAAIA,MAAK,KAAK;AAEd,UAAQ;AAER,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,QAAQE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AACjC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/C,WAAK,IAAI,MAAM,CAAC,GAAG,SAAS,MAAM;AAChC,eAAO,EAAE;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,SAAS,OAAO,cAAc,eAAeA,KAAI,MAAM,KAAK,CAAC;AACjF;AAEO,SAAS,WAAWG,aAAY,MAAM,OAAO;AAClD,MAAIH,MAAKG,YAAW;AAEpB,EAAAA,YAAW,KAAK,WAAW;AACzB,QAAI,WAAWF,KAAI,MAAMD,GAAE;AAC3B,KAAC,SAAS,UAAU,SAAS,QAAQ,CAAC,IAAI,IAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AAAA,EAC/E,CAAC;AAED,SAAO,SAAS,MAAM;AACpB,WAAOE,KAAI,MAAMF,GAAE,EAAE,MAAM,IAAI;AAAA,EACjC;AACF;;;AC7Ee,SAARI,qBAAiB,GAAG,GAAG;AAC5B,MAAI;AACJ,UAAQ,OAAO,MAAM,WAAW,iBAC1B,aAAa,QAAQ,eACpB,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG,eACzB,gBAAmB,GAAG,CAAC;AAC/B;;;ACJA,SAASC,YAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAEA,SAASC,cAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AAEA,SAASC,cAAa,MAAM,aAAa,QAAQ;AAC/C,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,aAAa,IAAI;AACpC,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAASC,gBAAe,UAAU,aAAa,QAAQ;AACrD,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAChE,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAASC,cAAa,MAAM,aAAa,OAAO;AAC9C,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU;AAAM,aAAO,KAAK,KAAK,gBAAgB,IAAI;AACzD,cAAU,KAAK,aAAa,IAAI;AAChC,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEA,SAASC,gBAAe,UAAU,aAAa,OAAO;AACpD,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU;AAAM,aAAO,KAAK,KAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AACrF,cAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAC5D,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEe,SAARC,cAAiB,MAAM,OAAO;AACnC,MAAI,WAAW,kBAAU,IAAI,GAAG,IAAI,aAAa,cAAc,0BAAuBC;AACtF,SAAO,KAAK,UAAU,MAAM,OAAO,UAAU,cACtC,SAAS,QAAQF,kBAAiBD,eAAc,UAAU,GAAG,WAAW,MAAM,UAAU,MAAM,KAAK,CAAC,IACrG,SAAS,QAAQ,SAAS,QAAQH,gBAAeD,aAAY,QAAQ,KACpE,SAAS,QAAQG,kBAAiBD,eAAc,UAAU,GAAG,KAAK,CAAC;AAC5E;;;AC3EA,SAAS,gBAAgB,MAAM,GAAG;AAChC,SAAO,SAAS,GAAG;AACjB,SAAK,aAAa,MAAM,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACzC;AACF;AAEA,SAAS,kBAAkB,UAAU,GAAG;AACtC,SAAO,SAAS,GAAG;AACjB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;AAEA,SAAS,YAAY,UAAU,OAAO;AACpC,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM;AAAI,YAAM,KAAK,MAAM,kBAAkB,UAAU,CAAC;AAC5D,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEA,SAAS,UAAU,MAAM,OAAO;AAC9B,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM;AAAI,YAAM,KAAK,MAAM,gBAAgB,MAAM,CAAC;AACtD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,kBAAiB,MAAM,OAAO;AACnC,MAAI,MAAM,UAAU;AACpB,MAAI,UAAU,SAAS;AAAG,YAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS;AAAM,WAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU;AAAY,UAAM,IAAI;AAC3C,MAAI,WAAW,kBAAU,IAAI;AAC7B,SAAO,KAAK,MAAM,MAAM,SAAS,QAAQ,cAAc,WAAW,UAAU,KAAK,CAAC;AACpF;;;ACzCA,SAAS,cAAcM,KAAI,OAAO;AAChC,SAAO,WAAW;AAChB,SAAK,MAAMA,GAAE,EAAE,QAAQ,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACrD;AACF;AAEA,SAAS,cAAcA,KAAI,OAAO;AAChC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,SAAK,MAAMA,GAAE,EAAE,QAAQ;AAAA,EACzB;AACF;AAEe,SAAR,cAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,MAAM,OAAO,UAAU,aACxB,gBACA,eAAeA,KAAI,KAAK,CAAC,IAC7BC,KAAI,KAAK,KAAK,GAAGD,GAAE,EAAE;AAC7B;;;ACpBA,SAAS,iBAAiBE,KAAI,OAAO;AACnC,SAAO,WAAW;AAChB,IAAAC,KAAI,MAAMD,GAAE,EAAE,WAAW,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACvD;AACF;AAEA,SAAS,iBAAiBA,KAAI,OAAO;AACnC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,IAAAC,KAAI,MAAMD,GAAE,EAAE,WAAW;AAAA,EAC3B;AACF;AAEe,SAAR,iBAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,MAAM,OAAO,UAAU,aACxB,mBACA,kBAAkBA,KAAI,KAAK,CAAC,IAChCE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AAC7B;;;ACpBA,SAAS,aAAaG,KAAI,OAAO;AAC/B,MAAI,OAAO,UAAU;AAAY,UAAM,IAAI;AAC3C,SAAO,WAAW;AAChB,IAAAC,KAAI,MAAMD,GAAE,EAAE,OAAO;AAAA,EACvB;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,KAAK,aAAaA,KAAI,KAAK,CAAC,IACjCE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AAC7B;;;ACbA,SAAS,YAAYG,KAAI,OAAO;AAC9B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,OAAO,MAAM;AAAY,YAAM,IAAI;AACvC,IAAAC,KAAI,MAAMD,GAAE,EAAE,OAAO;AAAA,EACvB;AACF;AAEe,SAAR,oBAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU;AAAY,UAAM,IAAI;AAC3C,SAAO,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,CAAC;AAC/C;;;ACVe,SAARE,gBAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU;AAAY,YAAQ,gBAAQ,KAAK;AAEtD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACtE;;;ACbe,SAARC,eAAiBC,aAAY;AAClC,MAAIA,YAAW,QAAQ,KAAK;AAAK,UAAM,IAAI;AAE3C,WAAS,UAAU,KAAK,SAAS,UAAUA,YAAW,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQC,SAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,QAAAA,OAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AAEA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACnE;;;AChBA,SAAS,MAAM,MAAM;AACnB,UAAQ,OAAO,IAAI,KAAK,EAAE,MAAM,OAAO,EAAE,MAAM,SAAS,GAAG;AACzD,QAAI,IAAI,EAAE,QAAQ,GAAG;AACrB,QAAI,KAAK;AAAG,UAAI,EAAE,MAAM,GAAG,CAAC;AAC5B,WAAO,CAAC,KAAK,MAAM;AAAA,EACrB,CAAC;AACH;AAEA,SAAS,WAAWC,KAAI,MAAM,UAAU;AACtC,MAAI,KAAK,KAAK,MAAM,MAAM,IAAI,IAAI,OAAOC;AACzC,SAAO,WAAW;AAChB,QAAI,WAAW,IAAI,MAAMD,GAAE,GACvB,KAAK,SAAS;AAKlB,QAAI,OAAO;AAAK,OAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,MAAM,QAAQ;AAE3D,aAAS,KAAK;AAAA,EAChB;AACF;AAEe,SAARE,YAAiB,MAAM,UAAU;AACtC,MAAIF,MAAK,KAAK;AAEd,SAAO,UAAU,SAAS,IACpBG,KAAI,KAAK,KAAK,GAAGH,GAAE,EAAE,GAAG,GAAG,IAAI,IAC/B,KAAK,KAAK,WAAWA,KAAI,MAAM,QAAQ,CAAC;AAChD;;;AC/BA,SAAS,eAAeI,KAAI;AAC1B,SAAO,WAAW;AAChB,QAAI,SAAS,KAAK;AAClB,aAAS,KAAK,KAAK;AAAc,UAAI,CAAC,MAAMA;AAAI;AAChD,QAAI;AAAQ,aAAO,YAAY,IAAI;AAAA,EACrC;AACF;AAEe,SAARC,kBAAmB;AACxB,SAAO,KAAK,GAAG,cAAc,eAAe,KAAK,GAAG,CAAC;AACvD;;;ACNe,SAARC,gBAAiB,QAAQ;AAC9B,MAAI,OAAO,KAAK,OACZC,MAAK,KAAK;AAEd,MAAI,OAAO,WAAW;AAAY,aAAS,iBAAS,MAAM;AAE1D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAC/E,YAAI,cAAc;AAAM,kBAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AACd,yBAAS,SAAS,CAAC,GAAG,MAAMA,KAAI,GAAG,UAAUC,KAAI,MAAMD,GAAE,CAAC;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,MAAMA,GAAE;AAC1D;;;ACjBe,SAARE,mBAAiB,QAAQ;AAC9B,MAAI,OAAO,KAAK,OACZC,MAAK,KAAK;AAEd,MAAI,OAAO,WAAW;AAAY,aAAS,oBAAY,MAAM;AAE7D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,iBAASC,YAAW,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG,OAAOC,WAAUC,KAAI,MAAMH,GAAE,GAAG,IAAI,GAAG,IAAIC,UAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AACtI,cAAI,QAAQA,UAAS,CAAC,GAAG;AACvB,6BAAS,OAAO,MAAMD,KAAI,GAAGC,WAAUC,QAAO;AAAA,UAChD;AAAA,QACF;AACA,kBAAU,KAAKD,SAAQ;AACvB,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,SAAS,MAAMD,GAAE;AACpD;;;ACvBA,IAAII,aAAY,kBAAU,UAAU;AAErB,SAARC,qBAAmB;AACxB,SAAO,IAAID,WAAU,KAAK,SAAS,KAAK,QAAQ;AAClD;;;ACAA,SAAS,UAAU,MAAM,aAAa;AACpC,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI,GAC1B,WAAW,KAAK,MAAM,eAAe,IAAI,GAAG,WAAM,MAAM,IAAI;AAChE,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,eAC/C,eAAe,YAAY,WAAW,SAAS,WAAW,OAAO;AAAA,EACzE;AACF;AAEA,SAASE,aAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AAEA,SAASC,eAAc,MAAM,aAAa,QAAQ;AAChD,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI;AAC9B,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAASC,eAAc,MAAM,aAAa,OAAO;AAC/C,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI,GAC1B,SAAS,MAAM,IAAI,GACnB,UAAU,SAAS;AACvB,QAAI,UAAU;AAAM,gBAAU,UAAU,KAAK,MAAM,eAAe,IAAI,GAAG,WAAM,MAAM,IAAI;AACzF,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEA,SAAS,iBAAiBC,KAAI,MAAM;AAClC,MAAI,KAAK,KAAK,WAAW,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAKC;AACtE,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMF,GAAE,GACvB,KAAK,SAAS,IACd,WAAW,SAAS,MAAM,GAAG,KAAK,OAAOC,YAAWA,UAASJ,aAAY,IAAI,KAAK;AAKtF,QAAI,OAAO,OAAO,cAAc;AAAU,OAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,OAAO,YAAY,QAAQ;AAElG,aAAS,KAAK;AAAA,EAChB;AACF;AAEe,SAARM,eAAiB,MAAM,OAAO,UAAU;AAC7C,MAAI,KAAK,QAAQ,QAAQ,cAAc,0BAAuBC;AAC9D,SAAO,SAAS,OAAO,KAClB,WAAW,MAAM,UAAU,MAAM,CAAC,CAAC,EACnC,GAAG,eAAe,MAAMP,aAAY,IAAI,CAAC,IAC1C,OAAO,UAAU,aAAa,KAC7B,WAAW,MAAME,eAAc,MAAM,GAAG,WAAW,MAAM,WAAW,MAAM,KAAK,CAAC,CAAC,EACjF,KAAK,iBAAiB,KAAK,KAAK,IAAI,CAAC,IACtC,KACC,WAAW,MAAMD,eAAc,MAAM,GAAG,KAAK,GAAG,QAAQ,EACxD,GAAG,eAAe,MAAM,IAAI;AACnC;;;AC/EA,SAAS,iBAAiB,MAAM,GAAG,UAAU;AAC3C,SAAO,SAAS,GAAG;AACjB,SAAK,MAAM,YAAY,MAAM,EAAE,KAAK,MAAM,CAAC,GAAG,QAAQ;AAAA,EACxD;AACF;AAEA,SAAS,WAAW,MAAM,OAAO,UAAU;AACzC,MAAI,GAAG;AACP,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM;AAAI,WAAK,KAAK,MAAM,iBAAiB,MAAM,GAAG,QAAQ;AAChE,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,mBAAiB,MAAM,OAAO,UAAU;AAC7C,MAAI,MAAM,YAAY,QAAQ;AAC9B,MAAI,UAAU,SAAS;AAAG,YAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS;AAAM,WAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU;AAAY,UAAM,IAAI;AAC3C,SAAO,KAAK,MAAM,KAAK,WAAW,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC;AAClF;;;ACrBA,SAASO,cAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AAEA,SAASC,cAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,SAAS,MAAM,IAAI;AACvB,SAAK,cAAc,UAAU,OAAO,KAAK;AAAA,EAC3C;AACF;AAEe,SAARC,cAAiB,OAAO;AAC7B,SAAO,KAAK,MAAM,QAAQ,OAAO,UAAU,aACrCD,cAAa,WAAW,MAAM,QAAQ,KAAK,CAAC,IAC5CD,cAAa,SAAS,OAAO,KAAK,QAAQ,EAAE,CAAC;AACrD;;;ACnBA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,SAAS,GAAG;AACjB,SAAK,cAAc,EAAE,KAAK,MAAM,CAAC;AAAA,EACnC;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM;AAAI,YAAM,KAAK,MAAM,gBAAgB,CAAC;AAChD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,kBAAiB,OAAO;AAC7B,MAAI,MAAM;AACV,MAAI,UAAU,SAAS;AAAG,YAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS;AAAM,WAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU;AAAY,UAAM,IAAI;AAC3C,SAAO,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AACzC;;;ACpBe,SAAR,qBAAmB;AACxB,MAAI,OAAO,KAAK,OACZ,MAAM,KAAK,KACX,MAAM,MAAM;AAEhB,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAIG,WAAUC,KAAI,MAAM,GAAG;AAC3B,yBAAS,MAAM,MAAM,KAAK,GAAG,OAAO;AAAA,UAClC,MAAMD,SAAQ,OAAOA,SAAQ,QAAQA,SAAQ;AAAA,UAC7C,OAAO;AAAA,UACP,UAAUA,SAAQ;AAAA,UAClB,MAAMA,SAAQ;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,MAAM,GAAG;AACxD;;;ACrBe,SAAR,cAAmB;AACxB,MAAI,KAAK,KAAK,OAAO,MAAME,MAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAC3D,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,SAAS,EAAC,OAAO,OAAM,GACvB,MAAM,EAAC,OAAO,WAAW;AAAE,UAAI,EAAE,SAAS;AAAG,gBAAQ;AAAA,IAAG,EAAC;AAE7D,SAAK,KAAK,WAAW;AACnB,UAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,KAAK,SAAS;AAKlB,UAAI,OAAO,KAAK;AACd,eAAO,MAAM,IAAI,KAAK;AACtB,YAAI,EAAE,OAAO,KAAK,MAAM;AACxB,YAAI,EAAE,UAAU,KAAK,MAAM;AAC3B,YAAI,EAAE,IAAI,KAAK,GAAG;AAAA,MACpB;AAEA,eAAS,KAAK;AAAA,IAChB,CAAC;AAGD,QAAI,SAAS;AAAG,cAAQ;AAAA,EAC1B,CAAC;AACH;;;ACNA,IAAI,KAAK;AAEF,SAAS,WAAW,QAAQ,SAAS,MAAME,KAAI;AACpD,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,QAAQ;AACb,OAAK,MAAMA;AACb;AAEe,SAAR,WAA4B,MAAM;AACvC,SAAO,kBAAU,EAAE,WAAW,IAAI;AACpC;AAEO,SAAS,QAAQ;AACtB,SAAO,EAAE;AACX;AAEA,IAAI,sBAAsB,kBAAU;AAEpC,WAAW,YAAY,WAAW,YAAY;AAAA,EAC5C,aAAa;AAAA,EACb,QAAQC;AAAA,EACR,WAAWC;AAAA,EACX,QAAQC;AAAA,EACR,OAAOC;AAAA,EACP,WAAWC;AAAA,EACX,YAAY;AAAA,EACZ,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,IAAIC;AAAA,EACJ,MAAMC;AAAA,EACN,WAAW;AAAA,EACX,OAAOC;AAAA,EACP,YAAY;AAAA,EACZ,MAAMC;AAAA,EACN,WAAW;AAAA,EACX,QAAQC;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,aAAa;AAAA,EACb,KAAK;AAAA,EACL,CAAC,OAAO,QAAQ,GAAG,oBAAoB,OAAO,QAAQ;AACxD;;;AC9DO,SAAS,WAAW,GAAG;AAC5B,WAAS,KAAK,MAAM,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK;AAC9D;;;ACVA,IAAI,WAAW;AAER,IAAI,SAAU,SAAS,OAAO,GAAG;AACtC,MAAI,CAAC;AAEL,WAASC,QAAO,GAAG;AACjB,WAAO,KAAK,IAAI,GAAG,CAAC;AAAA,EACtB;AAEA,EAAAA,QAAO,WAAW;AAElB,SAAOA;AACT,EAAG,QAAQ;AAEJ,IAAI,UAAW,SAASC,QAAO,GAAG;AACvC,MAAI,CAAC;AAEL,WAASC,SAAQ,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,EAC9B;AAEA,EAAAA,SAAQ,WAAWD;AAEnB,SAAOC;AACT,EAAG,QAAQ;AAEJ,IAAI,YAAa,SAASD,QAAO,GAAG;AACzC,MAAI,CAAC;AAEL,WAASE,WAAU,GAAG;AACpB,aAAS,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK;AAAA,EACrE;AAEA,EAAAA,WAAU,WAAWF;AAErB,SAAOE;AACT,EAAG,QAAQ;;;ACpCX,IAAIC,MAAK,KAAK;AAAd,IACIC,UAASD,MAAK;;;ACAX,SAAS,KAAK,GAAG;AACtB,UAAQ,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,eAAgB;AACjD;;;ACHA,IAAI,KAAK,IAAI;AAAb,IACI,KAAK,IAAI;AADb,IAEI,KAAK,IAAI;AAFb,IAGI,KAAK,IAAI;AAHb,IAII,KAAK,IAAI;AAJb,IAKI,KAAK,KAAK;AALd,IAMI,KAAK,KAAK;AANd,IAOI,KAAK,KAAK;AAPd,IAQI,KAAK,KAAK;AARd,IASI,KAAK,IAAI,KAAK;;;ACTlB,IAAI,YAAY;AAET,IAAI,SAAU,SAASE,QAAO,GAAG;AACtC,MAAI,CAAC;AAEL,WAASC,QAAO,GAAG;AACjB,YAAQ,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK;AAAA,EACvC;AAEA,EAAAA,QAAO,YAAYD;AAEnB,SAAOC;AACT,EAAG,SAAS;AAEL,IAAI,UAAW,SAASD,QAAO,GAAG;AACvC,MAAI,CAAC;AAEL,WAASE,SAAQ,GAAG;AAClB,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,EACvC;AAEA,EAAAA,SAAQ,YAAYF;AAEpB,SAAOE;AACT,EAAG,SAAS;AAEL,IAAI,YAAa,SAASF,QAAO,GAAG;AACzC,MAAI,CAAC;AAEL,WAASG,WAAU,GAAG;AACpB,aAAS,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,EAC7F;AAEA,EAAAA,WAAU,YAAYH;AAEtB,SAAOG;AACT,EAAG,SAAS;;;AClCZ,IAAIC,OAAM,IAAI,KAAK;AAAnB,IACI,YAAY;AADhB,IAEI,SAAS;AAEN,IAAI,YAAa,SAASC,QAAO,GAAG,GAAG;AAC5C,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,KAAKD;AAEpD,WAASE,WAAU,GAAG;AACpB,WAAO,IAAI,KAAK,EAAE,EAAE,CAAE,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EAChD;AAEA,EAAAA,WAAU,YAAY,SAASC,IAAG;AAAE,WAAOF,QAAOE,IAAG,IAAIH,IAAG;AAAA,EAAG;AAC/D,EAAAE,WAAU,SAAS,SAASE,IAAG;AAAE,WAAOH,QAAO,GAAGG,EAAC;AAAA,EAAG;AAEtD,SAAOF;AACT,EAAG,WAAW,MAAM;AAEb,IAAI,aAAc,SAASD,QAAO,GAAG,GAAG;AAC7C,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,KAAKD;AAEpD,WAASK,YAAW,GAAG;AACrB,WAAO,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EACpD;AAEA,EAAAA,YAAW,YAAY,SAASF,IAAG;AAAE,WAAOF,QAAOE,IAAG,IAAIH,IAAG;AAAA,EAAG;AAChE,EAAAK,YAAW,SAAS,SAASD,IAAG;AAAE,WAAOH,QAAO,GAAGG,EAAC;AAAA,EAAG;AAEvD,SAAOC;AACT,EAAG,WAAW,MAAM;AAEb,IAAI,eAAgB,SAASJ,QAAO,GAAG,GAAG;AAC/C,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,KAAKD;AAEpD,WAASM,cAAa,GAAG;AACvB,aAAS,IAAI,IAAI,IAAI,KAAK,IACpB,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,IACnC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK;AAAA,EACnD;AAEA,EAAAA,cAAa,YAAY,SAASH,IAAG;AAAE,WAAOF,QAAOE,IAAG,IAAIH,IAAG;AAAA,EAAG;AAClE,EAAAM,cAAa,SAAS,SAASF,IAAG;AAAE,WAAOH,QAAO,GAAGG,EAAC;AAAA,EAAG;AAEzD,SAAOE;AACT,EAAG,WAAW,MAAM;;;ACxCpB,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AACR;AAEA,SAAS,QAAQ,MAAMC,KAAI;AACzB,MAAI;AACJ,SAAO,EAAE,SAAS,KAAK,iBAAiB,EAAE,SAAS,OAAOA,GAAE,IAAI;AAC9D,QAAI,EAAE,OAAO,KAAK,aAAa;AAC7B,YAAM,IAAI,MAAM,cAAcA,GAAE,YAAY;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AACT;AAEe,SAARC,oBAAiB,MAAM;AAC5B,MAAID,KACA;AAEJ,MAAI,gBAAgB,YAAY;AAC9B,IAAAA,MAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EAC7B,OAAO;AACL,IAAAA,MAAK,MAAM,IAAI,SAAS,eAAe,OAAO,IAAI,GAAG,OAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC3F;AAEA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,yBAAS,MAAM,MAAMA,KAAI,GAAG,OAAO,UAAU,QAAQ,MAAMA,GAAE,CAAC;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,MAAMA,GAAE;AACvD;;;ACrCA,kBAAU,UAAU,YAAYE;AAChC,kBAAU,UAAU,aAAaC;;;ACLjC,IAAOC,oBAAQ,OAAK,MAAM;;;ACAX,SAAR,UAA2B,MAAM;AAAA,EACtC;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,EACA,UAAAC;AACF,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAC,OAAO,MAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,aAAa,EAAC,OAAO,aAAa,YAAY,MAAM,cAAc,KAAI;AAAA,IACtE,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,WAAW,EAAC,OAAOD,YAAW,YAAY,MAAM,cAAc,KAAI;AAAA,IAClE,GAAG,EAAC,OAAOC,UAAQ;AAAA,EACrB,CAAC;AACH;;;ACbO,SAAS,UAAU,GAAG,GAAG,GAAG;AACjC,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACX;AAEA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,OAAO,SAAS,GAAG;AACjB,WAAO,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EAClE;AAAA,EACA,WAAW,SAAS,GAAG,GAAG;AACxB,WAAO,MAAM,IAAI,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,CAAC;AAAA,EAClG;AAAA,EACA,OAAO,SAAS,OAAO;AACrB,WAAO,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EAChE;AAAA,EACA,QAAQ,SAAS,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS,UAAU;AACzB,WAAO,EAAE,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1E;AAAA,EACA,SAAS,SAAS,GAAG;AACnB,YAAQ,IAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,SAAS,SAAS,GAAG;AACnB,YAAQ,IAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,UAAU,SAAS,GAAG;AACpB,WAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,SAAS,GAAG;AACpB,WAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,eAAe,KAAK,IAAI,MAAM,KAAK,IAAI,aAAa,KAAK,IAAI;AAAA,EACtE;AACF;AAEO,IAAI,WAAW,IAAI,UAAU,GAAG,GAAG,CAAC;AAE3C,UAAU,YAAY,UAAU;AAEjB,SAAR,UAA2B,MAAM;AACtC,SAAO,CAAC,KAAK;AAAQ,QAAI,EAAE,OAAO,KAAK;AAAa,aAAO;AAC3D,SAAO,KAAK;AACd;;;AClDO,SAASC,eAAc,OAAO;AACnC,QAAM,yBAAyB;AACjC;AAEe,SAARC,iBAAiB,OAAO;AAC7B,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;;;ACKA,SAAS,cAAc,OAAO;AAC5B,UAAQ,CAAC,MAAM,WAAW,MAAM,SAAS,YAAY,CAAC,MAAM;AAC9D;AAEA,SAAS,gBAAgB;AACvB,MAAI,IAAI;AACR,MAAI,aAAa,YAAY;AAC3B,QAAI,EAAE,mBAAmB;AACzB,QAAI,EAAE,aAAa,SAAS,GAAG;AAC7B,UAAI,EAAE,QAAQ;AACd,aAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC;AAAA,IACrD;AACA,WAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,KAAK,CAAC;AAAA,EACjE;AACA,SAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,YAAY,CAAC;AACjD;AAEA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,UAAU;AACxB;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,CAAC,MAAM,UAAU,MAAM,cAAc,IAAI,OAAO,MAAM,YAAY,IAAI,SAAU,MAAM,UAAU,KAAK;AAC9G;AAEA,SAAS,mBAAmB;AAC1B,SAAO,UAAU,kBAAmB,kBAAkB;AACxD;AAEA,SAAS,iBAAiBC,YAAW,QAAQ,iBAAiB;AAC5D,MAAI,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC;AAChE,SAAOA,WAAU;AAAA,IACf,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,IACjE,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,EACnE;AACF;AAEe,SAARC,gBAAmB;AACxB,MAAIC,UAAS,eACT,SAAS,eACT,YAAY,kBACZ,aAAa,mBACb,YAAY,kBACZ,cAAc,CAAC,GAAG,QAAQ,GAC1B,kBAAkB,CAAC,CAAC,WAAW,SAAS,GAAG,CAAC,UAAU,QAAQ,CAAC,GAC/D,WAAW,KACX,cAAc,cACd,YAAY,iBAAS,SAAS,QAAQ,KAAK,GAC3C,eACA,YACA,aACA,aAAa,KACb,aAAa,KACb,iBAAiB,GACjB,cAAc;AAElB,WAAS,KAAKC,YAAW;AACvB,IAAAA,WACK,SAAS,UAAU,gBAAgB,EACnC,GAAG,cAAc,OAAO,EACxB,GAAG,kBAAkB,WAAW,EAChC,GAAG,iBAAiB,UAAU,EAChC,OAAO,SAAS,EACd,GAAG,mBAAmB,YAAY,EAClC,GAAG,kBAAkB,UAAU,EAC/B,GAAG,kCAAkC,UAAU,EAC/C,MAAM,+BAA+B,eAAe;AAAA,EAC3D;AAEA,OAAK,YAAY,SAAS,YAAYH,YAAW,OAAO,OAAO;AAC7D,QAAIG,aAAY,WAAW,YAAY,WAAW,UAAU,IAAI;AAChE,IAAAA,WAAU,SAAS,UAAU,gBAAgB;AAC7C,QAAI,eAAeA,YAAW;AAC5B,eAAS,YAAYH,YAAW,OAAO,KAAK;AAAA,IAC9C,OAAO;AACL,MAAAG,WAAU,UAAU,EAAE,KAAK,WAAW;AACpC,gBAAQ,MAAM,SAAS,EACpB,MAAM,KAAK,EACX,MAAM,EACN,KAAK,MAAM,OAAOH,eAAc,aAAaA,WAAU,MAAM,MAAM,SAAS,IAAIA,UAAS,EACzF,IAAI;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAEA,OAAK,UAAU,SAASG,YAAW,GAAG,GAAG,OAAO;AAC9C,SAAK,QAAQA,YAAW,WAAW;AACjC,UAAI,KAAK,KAAK,OAAO,GACjB,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC9D,aAAO,KAAK;AAAA,IACd,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,OAAK,UAAU,SAASA,YAAW,GAAG,GAAG,OAAO;AAC9C,SAAK,UAAUA,YAAW,WAAW;AACnC,UAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,KAAK,KAAK,QACVC,MAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI,GACpF,KAAK,GAAG,OAAOA,GAAE,GACjB,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC9D,aAAO,UAAU,UAAU,MAAM,IAAI,EAAE,GAAGA,KAAI,EAAE,GAAG,GAAG,eAAe;AAAA,IACvE,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,OAAK,cAAc,SAASD,YAAW,GAAG,GAAG,OAAO;AAClD,SAAK,UAAUA,YAAW,WAAW;AACnC,aAAO,UAAU,KAAK,OAAO;AAAA,QAC3B,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAAA,QACrD,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAAA,MACvD,GAAG,OAAO,MAAM,MAAM,SAAS,GAAG,eAAe;AAAA,IACnD,GAAG,MAAM,KAAK;AAAA,EAChB;AAEA,OAAK,cAAc,SAASA,YAAW,GAAG,GAAG,GAAG,OAAO;AACrD,SAAK,UAAUA,YAAW,WAAW;AACnC,UAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,IAAI,KAAK,QACTC,MAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AACxF,aAAO,UAAU,SAAS,UAAUA,IAAG,CAAC,GAAGA,IAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AAAA,QAC3D,OAAO,MAAM,aAAa,CAAC,EAAE,MAAM,MAAM,SAAS,IAAI,CAAC;AAAA,QACvD,OAAO,MAAM,aAAa,CAAC,EAAE,MAAM,MAAM,SAAS,IAAI,CAAC;AAAA,MACzD,GAAG,GAAG,eAAe;AAAA,IACvB,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,WAAS,MAAMJ,YAAW,GAAG;AAC3B,QAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;AACxD,WAAO,MAAMA,WAAU,IAAIA,aAAY,IAAI,UAAU,GAAGA,WAAU,GAAGA,WAAU,CAAC;AAAA,EAClF;AAEA,WAAS,UAAUA,YAAWI,KAAI,IAAI;AACpC,QAAI,IAAIA,IAAG,CAAC,IAAI,GAAG,CAAC,IAAIJ,WAAU,GAAG,IAAII,IAAG,CAAC,IAAI,GAAG,CAAC,IAAIJ,WAAU;AACnE,WAAO,MAAMA,WAAU,KAAK,MAAMA,WAAU,IAAIA,aAAY,IAAI,UAAUA,WAAU,GAAG,GAAG,CAAC;AAAA,EAC7F;AAEA,WAAS,SAASK,SAAQ;AACxB,WAAO,EAAE,CAACA,QAAO,CAAC,EAAE,CAAC,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,KAAK,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,KAAK,CAAC;AAAA,EAClF;AAEA,WAAS,SAASC,aAAYN,YAAW,OAAO,OAAO;AACrD,IAAAM,YACK,GAAG,cAAc,WAAW;AAAE,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,MAAM;AAAA,IAAG,CAAC,EAC9E,GAAG,2BAA2B,WAAW;AAAE,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,IAAI;AAAA,IAAG,CAAC,EACzF,MAAM,QAAQ,WAAW;AACxB,UAAI,OAAO,MACP,OAAO,WACP,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,IAAI,OAAO,MAAM,MAAM,IAAI,GAC3B,IAAI,SAAS,OAAO,SAAS,CAAC,IAAI,OAAO,UAAU,aAAa,MAAM,MAAM,MAAM,IAAI,IAAI,OAC1F,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GACjD,IAAI,KAAK,QACT,IAAI,OAAON,eAAc,aAAaA,WAAU,MAAM,MAAM,IAAI,IAAIA,YACpE,IAAI,YAAY,EAAE,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAC5E,aAAO,SAAS,GAAG;AACjB,YAAI,MAAM;AAAG,cAAI;AAAA,aACZ;AAAE,cAAI,IAAI,EAAE,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AAAG,cAAI,IAAI,UAAU,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;AAAA,QAAG;AAC3F,UAAE,KAAK,MAAM,CAAC;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACP;AAEA,WAAS,QAAQ,MAAM,MAAM,OAAO;AAClC,WAAQ,CAAC,SAAS,KAAK,aAAc,IAAI,QAAQ,MAAM,IAAI;AAAA,EAC7D;AAEA,WAAS,QAAQ,MAAM,MAAM;AAC3B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,SAAS,OAAO,MAAM,MAAM,IAAI;AACrC,SAAK,OAAO;AAAA,EACd;AAEA,UAAQ,YAAY;AAAA,IAClB,OAAO,SAAS,OAAO;AACrB,UAAI;AAAO,aAAK,cAAc;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,aAAK,KAAK,YAAY;AACtB,aAAK,KAAK,OAAO;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAAS,KAAKA,YAAW;AAC7B,UAAI,KAAK,SAAS,QAAQ;AAAS,aAAK,MAAM,CAAC,IAAIA,WAAU,OAAO,KAAK,MAAM,CAAC,CAAC;AACjF,UAAI,KAAK,UAAU,QAAQ;AAAS,aAAK,OAAO,CAAC,IAAIA,WAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,UAAI,KAAK,UAAU,QAAQ;AAAS,aAAK,OAAO,CAAC,IAAIA,WAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,WAAK,KAAK,SAASA;AACnB,WAAK,KAAK,MAAM;AAChB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,WAAW;AACd,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,eAAO,KAAK,KAAK;AACjB,aAAK,KAAK,KAAK;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAAS,MAAM;AACnB,UAAI,IAAIO,gBAAO,KAAK,IAAI,EAAE,MAAM;AAChC,gBAAU;AAAA,QACR;AAAA,QACA,KAAK;AAAA,QACL,IAAI,UAAU,MAAM;AAAA,UAClB,aAAa,KAAK;AAAA,UAClB,QAAQ;AAAA,UACR;AAAA,UACA,WAAW,KAAK,KAAK;AAAA,UACrB,UAAU;AAAA,QACZ,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,QAAQ,UAAU,MAAM;AAC/B,QAAI,CAACL,QAAO,MAAM,MAAM,SAAS;AAAG;AACpC,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,IAAI,KAAK,QACT,IAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,GAAG,WAAW,MAAM,MAAM,SAAS,CAAC,CAAC,CAAC,GAC3G,IAAI,gBAAQ,KAAK;AAIrB,QAAI,EAAE,OAAO;AACX,UAAI,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACpD,UAAE,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC;AAAA,MACtC;AACA,mBAAa,EAAE,KAAK;AAAA,IACtB,WAGS,EAAE,MAAM;AAAG;AAAA,SAGf;AACH,QAAE,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACzB,wBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AAEA,IAAAM,iBAAQ,KAAK;AACb,MAAE,QAAQ,WAAW,YAAY,UAAU;AAC3C,MAAE,KAAK,SAAS,UAAU,UAAU,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAEpG,aAAS,aAAa;AACpB,QAAE,QAAQ;AACV,QAAE,IAAI;AAAA,IACR;AAAA,EACF;AAEA,WAAS,YAAY,UAAU,MAAM;AACnC,QAAI,eAAe,CAACN,QAAO,MAAM,MAAM,SAAS;AAAG;AACnD,QAAI,IAAI,QAAQ,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GACzC,IAAIK,gBAAO,MAAM,IAAI,EAAE,GAAG,kBAAkB,YAAY,IAAI,EAAE,GAAG,gBAAgB,YAAY,IAAI,GACjG,IAAI,gBAAQ,OAAO,aAAa,GAChC,gBAAgB,MAAM,eACtBE,MAAK,MAAM,SACXC,MAAK,MAAM;AAEf,mBAAY,MAAM,IAAI;AACtB,IAAAC,eAAc,KAAK;AACnB,MAAE,QAAQ,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,CAAC;AACnC,sBAAU,IAAI;AACd,MAAE,MAAM;AAER,aAAS,WAAWC,QAAO;AACzB,MAAAJ,iBAAQI,MAAK;AACb,UAAI,CAAC,EAAE,OAAO;AACZ,YAAI,KAAKA,OAAM,UAAUH,KAAI,KAAKG,OAAM,UAAUF;AAClD,UAAE,QAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,MAChC;AACA,QAAE,MAAME,MAAK,EACX,KAAK,SAAS,UAAU,UAAU,EAAE,KAAK,QAAQ,EAAE,MAAM,CAAC,IAAI,gBAAQA,QAAO,aAAa,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,IACxI;AAEA,aAAS,WAAWA,QAAO;AACzB,QAAE,GAAG,+BAA+B,IAAI;AACxC,cAAWA,OAAM,MAAM,EAAE,KAAK;AAC9B,MAAAJ,iBAAQI,MAAK;AACb,QAAE,MAAMA,MAAK,EAAE,IAAI;AAAA,IACrB;AAAA,EACF;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAACV,QAAO,MAAM,MAAM,SAAS;AAAG;AACpC,QAAI,KAAK,KAAK,QACVE,MAAK,gBAAQ,MAAM,iBAAiB,MAAM,eAAe,CAAC,IAAI,OAAO,IAAI,GACzE,KAAK,GAAG,OAAOA,GAAE,GACjB,KAAK,GAAG,KAAK,MAAM,WAAW,MAAM,IACpC,KAAK,UAAU,UAAU,MAAM,IAAI,EAAE,GAAGA,KAAI,EAAE,GAAG,OAAO,MAAM,MAAM,IAAI,GAAG,eAAe;AAE9F,IAAAI,iBAAQ,KAAK;AACb,QAAI,WAAW;AAAG,MAAAD,gBAAO,IAAI,EAAE,WAAW,EAAE,SAAS,QAAQ,EAAE,KAAK,UAAU,IAAIH,KAAI,KAAK;AAAA;AACtF,MAAAG,gBAAO,IAAI,EAAE,KAAK,KAAK,WAAW,IAAIH,KAAI,KAAK;AAAA,EACtD;AAEA,WAAS,aAAa,UAAU,MAAM;AACpC,QAAI,CAACF,QAAO,MAAM,MAAM,SAAS;AAAG;AACpC,QAAI,UAAU,MAAM,SAChB,IAAI,QAAQ,QACZ,IAAI,QAAQ,MAAM,MAAM,MAAM,eAAe,WAAW,CAAC,EAAE,MAAM,KAAK,GACtE,SAAS,GAAG,GAAG;AAEnB,IAAAS,eAAc,KAAK;AACnB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,gBAAQ,GAAG,IAAI;AACnC,UAAI,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,GAAG,EAAE,UAAU;AAC3C,UAAI,CAAC,EAAE;AAAQ,UAAE,SAAS,GAAG,UAAU,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,eACnD,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;AAAG,UAAE,SAAS,GAAG,EAAE,OAAO;AAAA,IACrE;AAEA,QAAI;AAAe,sBAAgB,aAAa,aAAa;AAE7D,QAAI,SAAS;AACX,UAAI,EAAE,OAAO;AAAG,qBAAa,EAAE,CAAC,GAAG,gBAAgB,WAAW,WAAW;AAAE,0BAAgB;AAAA,QAAM,GAAG,UAAU;AAC9G,wBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AAAA,EACF;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK;AAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG,GAAG,GAAG;AAEjC,IAAAH,iBAAQ,KAAK;AACb,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,gBAAQ,GAAG,IAAI;AACnC,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE;AAAY,UAAE,OAAO,CAAC,IAAI;AAAA,eACnD,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE;AAAY,UAAE,OAAO,CAAC,IAAI;AAAA,IACnE;AACA,QAAI,EAAE,KAAK;AACX,QAAI,EAAE,QAAQ;AACZ,UAAIJ,MAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GACjC,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GACjC,MAAM,KAAK,GAAG,CAAC,IAAIA,IAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAIA,IAAG,CAAC,KAAK,IACxD,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAC5D,UAAI,MAAM,GAAG,KAAK,KAAK,KAAK,EAAE,CAAC;AAC/B,UAAI,EAAEA,IAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAIA,IAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAC7C,UAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAAA,IAC/C,WACS,EAAE;AAAQ,UAAI,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC;AAAA;AAC7C;AAEL,MAAE,KAAK,SAAS,UAAU,UAAU,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,EAC1E;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK;AAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG;AAE3B,IAAAO,eAAc,KAAK;AACnB,QAAI;AAAa,mBAAa,WAAW;AACzC,kBAAc,WAAW,WAAW;AAAE,oBAAc;AAAA,IAAM,GAAG,UAAU;AACvE,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC;AACb,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE;AAAY,eAAO,EAAE;AAAA,eAC9C,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE;AAAY,eAAO,EAAE;AAAA,IAC9D;AACA,QAAI,EAAE,UAAU,CAAC,EAAE;AAAQ,QAAE,SAAS,EAAE,QAAQ,OAAO,EAAE;AACzD,QAAI,EAAE;AAAQ,QAAE,OAAO,CAAC,IAAI,KAAK,OAAO,OAAO,EAAE,OAAO,CAAC,CAAC;AAAA,SACrD;AACH,QAAE,IAAI;AAEN,UAAI,EAAE,SAAS,GAAG;AAChB,YAAI,gBAAQ,GAAG,IAAI;AACnB,YAAI,KAAK,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,aAAa;AACxE,cAAI,IAAIJ,gBAAO,IAAI,EAAE,GAAG,eAAe;AACvC,cAAI;AAAG,cAAE,MAAM,MAAM,SAAS;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,OAAK,aAAa,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAIM,kBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUX,UAAS,OAAO,MAAM,aAAa,IAAIW,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQX;AAAA,EAC3F;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAIW,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EACpI;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,EACpH;AAEA,OAAK,kBAAkB,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAC5Q;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,GAAG,QAAQ;AAAA,EACpD;AAEA,OAAK,WAAW,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,QAAQ;AAAA,EACpD;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,GAAG,QAAQ;AAAA,EACtD;AAEA,OAAK,KAAK,WAAW;AACnB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAY,OAAO;AAAA,EACtC;AAEA,OAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK,cAAc;AAAA,EAC5F;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,CAAC,GAAG,QAAQ;AAAA,EACvD;AAEA,SAAO;AACT;;;A9JtbA,SAAS,QAAQC,SAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAKA,OAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsBA,OAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AACzD,aAAO,OAAO,yBAAyBA,SAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AACzD,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,KAAK;AACpB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,MAAK;AAClG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,MAAK;AACjB,WAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,EAC1H,GAAG,QAAQ,GAAG;AAChB;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,WAAW;AAClB,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU;AAAM,WAAO,CAAC;AAE5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG;AAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO;AACjC;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAEvG,MAAI,MAAM;AAAM;AAChB,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI,IAAI;AAER,MAAI;AACF,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAChE,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW;AAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;AAAM,WAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI;AAAI,cAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC;AAAG;AACR,MAAI,OAAO,MAAM;AAAU,WAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,UAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAAK,SAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AAEA,IAAI,cAAc,CAAC,SAAS,UAAU,cAAc,kBAAkB;AAEtE,IAAI,UAAgB;AAApB,IACI,cAAc,yBAAyB,aAAO,CAAC,SAAS,CAAC;AAE7D,IAAI,iBAAa,4BAAc;AAE/B,IAAI,iBAAiB,SAASC,gBAAe,MAAM;AACjD,MAAI,wBAAwB,KAAK,kBAC7B,mBAAmB,0BAA0B,SAAS,CAAC,IAAI,uBAC3D,kBAAkB,KAAK,YACvBC,cAAa,oBAAoB,SAAS,kBAAkB,iBAC5D,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,MAAM,YACtC,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,MAAM;AAC5C,MAAI,SAAS,OAAOA,gBAAe;AACnC,MAAI;AAAQ,WAAOA;AACnB,MAAI,OAAO,YAAYA,WAAU,EAAE,EAAE,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;AACtE,MAAI,YAAY,CAAC,KAAK,SAAS,WAAW,MAAM,KAAK,SAAS,WAAW,MAAM,KAAK,QAAQ,UAAU,MAAM,KAAK,YAAY,cAAc,IAAI;AAC/I,YAAU,QAAQ,SAAU,GAAG;AAC7B,QAAI,CAAC;AAAG;AACR,WAAO,KAAK,CAAC,EAAE,iBAAiB,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;AAAA,EACjD,CAAC;AACD,SAAO;AACT;AAEA,IAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,MAAI,QAAQ,MAAM,OACd,SAAS,MAAM,QACfD,cAAa,MAAM,YACnB,mBAAmB,MAAM,kBACzB,YAAY,yBAAyB,OAAO,WAAW;AAE3D,MAAI,QAAQ,iBAAiB,UAAU,CAAC,GACpC,QAAQ,eAAe,OAAO,CAAC,GAC/B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,QAAQ,iBAAiB,UAAU,CAAC,GACpC,QAAQ,eAAe,OAAO,CAAC,GAC/B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,QAAQ,iBAAiB,aAAa,CAAC,GACvC,QAAQ,eAAe,OAAO,CAAC,GAC/B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,IAAI,iBAAiB,SAAS;AAClC,MAAI,eAAW,sBAAQ,WAAY;AACjC,WAAO,eAAe;AAAA,MACpB,kBAAkB;AAAA,QAChB,QAAQ,MAAM,OAAO,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI;AAAA,QACtD,QAAQ,MAAM,OAAO,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI;AAAA,QAC1D,WAAW,MAAM,OAAO,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI;AAAA,QACzD,OAAO;AAAA,MACT;AAAA,MACA,YAAYA;AAAA,MACZ;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,OAAO,QAAQA,aAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC;AAC7D,MAAI,WAAO,0BAAY,UAAU,CAAC,QAAQ,CAAC;AAC3C,MAAI,YAAQ,sBAAQ,WAAY;AAC9B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,MAAM,QAAQ,EAAE,WAAW,IAAI;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,OAAO,QAAQ,IAAI,CAAC;AACxB,SAAoB,aAAAE,QAAM,cAAc,WAAW,UAAU,SAAS;AAAA,IACpE;AAAA,EACF,GAAG,SAAS,CAAC;AACf;AAEA,YAAY,YAAY;AAAA,EACtB,OAAO,kBAAAC,QAAU;AAAA,EACjB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC;AAAA,EAClE,kBAAkB,kBAAAA,QAAU;AAC9B;AAEA,IAAI,gBAAgB,SAASC,iBAAgB;AAC3C,aAAO,yBAAW,UAAU;AAC9B;AAEA,IAAI,cAAc,CAAC,SAAS,UAAU,cAAc,oBAAoB,WAAW;AACnF,IAAI,oBAAgB,yBAAW,SAAU,MAAM,KAAK;AAClD,MAAI,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,MAAM,YACtC,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,MAAM,aACxC,kBAAkB,KAAK,YACvBJ,cAAa,oBAAoB,SAAS,kBAAkB,iBAC5D,wBAAwB,KAAK,kBAC7B,mBAAmB,0BAA0B,SAAS,CAAC,IAAI,uBAC3D,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,SAAoB,aAAAE,QAAM,cAAc,aAAa;AAAA,IACnD;AAAA,IACA;AAAA,IACA,YAAYF;AAAA,IACZ;AAAA,EACF,GAAgB,aAAAE,QAAM,cAAc,OAAO,SAAS;AAAA,IAClD;AAAA,IACA,SAAS,OAAO,OAAO,OAAO,GAAG,EAAE,OAAO,MAAM;AAAA,IAChD,WAAW,WAAW,OAAO,SAAS;AAAA,EACxC,GAAG,SAAS,CAAC,CAAC;AAChB,CAAC;AACD,cAAc,cAAc;AAC5B,cAAc,YAAY;AAAA,EACxB,OAAO,kBAAAC,QAAU;AAAA,EACjB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC;AAAA,EAClE,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,WAAW,kBAAAA,QAAU;AACvB;AAEA,SAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,MAAI,WAAW,IAAI,EAAE,IAAI,KAAK;AAC9B,MAAI,WAAW,IAAI,EAAE,IAAI,KAAK;AAC9B,SAAO,CAAC,IAAI,KAAK,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,KAAK,UAAU,EAAE,KAAK,EAAE,CAAC;AACtE;AACA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,MAAM,GAAG,EAAE,KAAK,SAAU,KAAK;AACpC,QAAI,CAAC,IAAI,IAAI;AACX,YAAM,MAAM,IAAI,UAAU;AAAA,IAC5B;AAEA,WAAO,IAAI,KAAK;AAAA,EAClB,CAAC,EAAE,OAAO,EAAE,SAAU,OAAO;AAC3B,YAAQ,IAAI,gDAAgD,KAAK;AAAA,EACnE,CAAC;AACH;AACA,SAAS,YAAY,aAAa,kBAAkB;AAClD,MAAI,aAAa,YAAY,SAAS;AAEtC,MAAI,CAAC,YAAY;AACf,WAAO,mBAAmB,iBAAiB,YAAY,YAAY,WAAW,IAAI,YAAY,YAAY;AAAA,EAC5G;AAEA,MAAI,QAAQ,gBAAQ,aAAa,YAAY,QAAQ,OAAO,KAAK,YAAY,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;AAC3F,SAAO,mBAAmB,iBAAiB,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,aAAa;AAC5B,MAAI,aAAa,YAAY,SAAS;AACtC,MAAI,CAAC;AAAY,WAAO;AACxB,MAAI,UAAU,aAAK,aAAa,YAAY,QAAQ,OAAO,KAAK,YAAY,OAAO,EAAE,CAAC,CAAC,GAAG,SAAU,GAAG,GAAG;AACxG,WAAO,MAAM;AAAA,EACf,CAAC;AACD,MAAI,UAAU,aAAK,aAAa,YAAY,QAAQ,OAAO,KAAK,YAAY,OAAO,EAAE,CAAC,CAAC,GAAG,SAAU,GAAG,GAAG;AACxG,WAAO,MAAM;AAAA,EACf,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,YAAY,SAAS,SAAS,MAAM;AAC3C,SAAO,WAAW,UAAU;AAAA,IAC1B,SAAS,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,MACvD,QAAQ;AAAA,MACR,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AAAA,IACD,SAAS,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,MACvD,QAAQ;AAAA,MACR,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AAAA,EACH,IAAI,CAAC;AACP;AACA,SAAS,gBAAgB,aAAa,MAAM;AAC1C,SAAO,cAAc,YAAY,IAAI,SAAU,GAAG,GAAG;AACnD,WAAO,eAAe,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AAAA,MAC/C,QAAQ,OAAO,OAAO,CAAC;AAAA,MACvB,SAAS,KAAK,CAAC;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,IAAI,CAAC;AACR;AACA,SAAS,sBAAsB;AAC7B,MAAI,KAAK,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC7E,MAAI,KAAK,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC7E,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,MAAI,YAAY,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,OAAO,KAAK;AAC5D,MAAI,SAAS,KAAK,IAAI,UAAU,CAAC;AACjC,MAAI,SAAS,KAAK,IAAI,UAAU,CAAC;AACjC,SAAO,IAAI,OAAO,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,IAAI,QAAQ,GAAG,EAAE,OAAO,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,EAAE;AACrH;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ;AACxB;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAI,YAAY,KAAK,WACjB,mBAAmB,KAAK;AAE5B,MAAI,kBAAc,yBAAW,UAAU,GACnC,OAAO,YAAY;AAEvB,MAAI,gBAAY,uBAAS,CAAC,CAAC,GACvB,aAAa,eAAe,WAAW,CAAC,GACxC,SAAS,WAAW,CAAC,GACrB,YAAY,WAAW,CAAC;AAE5B,8BAAU,WAAY;AACpB,SAAK,OAAO,WAAW,cAAc,cAAc,QAAQ,MAAM,OAAO;AAAa;AACrF,QAAI,CAAC;AAAW;AAEhB,QAAI,SAAS,SAAS,GAAG;AACvB,uBAAiB,SAAS,EAAE,KAAK,SAAU,MAAM;AAC/C,YAAI,MAAM;AACR,oBAAU;AAAA,YACR,aAAa,YAAY,MAAM,gBAAgB;AAAA,YAC/C,MAAM,QAAQ,IAAI;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,gBAAU;AAAA,QACR,aAAa,YAAY,WAAW,gBAAgB;AAAA,QACpD,MAAM,QAAQ,SAAS;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,WAAW,gBAAgB,CAAC;AAEhC,MAAI,eAAW,sBAAQ,WAAY;AACjC,QAAI,OAAO,OAAO,QAAQ,CAAC;AAC3B,QAAI,eAAe,YAAY,KAAK,SAAS,KAAK,SAAS,IAAI;AAC/D,WAAO;AAAA,MACL,aAAa,gBAAgB,OAAO,aAAa,IAAI;AAAA,MACrD,SAAS,aAAa;AAAA,MACtB,SAAS,aAAa;AAAA,IACxB;AAAA,EACF,GAAG,CAAC,QAAQ,IAAI,CAAC,GACb,cAAc,SAAS,aACvB,UAAU,SAAS,SACnB,UAAU,SAAS;AAEvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,cAAc,CAAC,aAAa,YAAY,oBAAoB,WAAW;AAC3E,IAAI,kBAAc,yBAAW,SAAU,MAAM,KAAK;AAChD,MAAI,YAAY,KAAK,WACjBE,YAAW,KAAK,UAChB,mBAAmB,KAAK,kBACxB,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,MAAI,kBAAc,yBAAW,UAAU,GACnC,OAAO,YAAY,MACnBL,cAAa,YAAY;AAE7B,MAAI,kBAAkB,eAAe;AAAA,IACnC;AAAA,IACA;AAAA,EACF,CAAC,GACG,cAAc,gBAAgB,aAC9B,UAAU,gBAAgB,SAC1B,UAAU,gBAAgB;AAE9B,SAAoB,aAAAE,QAAM,cAAc,KAAK,SAAS;AAAA,IACpD;AAAA,IACA,WAAW,mBAAmB,OAAO,SAAS;AAAA,EAChD,GAAG,SAAS,GAAG,eAAe,YAAY,SAAS,KAAKG,UAAS;AAAA,IAC/D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAYL;AAAA,EACd,CAAC,CAAC;AACJ,CAAC;AACD,YAAY,cAAc;AAC1B,YAAY,YAAY;AAAA,EACtB,WAAW,kBAAAG,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,KAAK,CAAC;AAAA,EACpF,UAAU,kBAAAA,QAAU;AAAA,EACpB,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,WAAW,kBAAAA,QAAU;AACvB;AAEA,IAAI,cAAc,CAAC,aAAa,gBAAgB,gBAAgB,eAAe,aAAa,WAAW,UAAU,SAAS,WAAW;AACrI,IAAI,gBAAY,yBAAW,SAAU,MAAM,KAAK;AAC9C,MAAI,YAAY,KAAK,WACjB,eAAe,KAAK,cACpB,eAAe,KAAK,cACpB,cAAc,KAAK,aACnB,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,SAAS,KAAK,QACd,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,CAAC,IAAI,YACrC,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,MAAI,gBAAY,uBAAS,KAAK,GAC1B,aAAa,eAAe,WAAW,CAAC,GACxC,YAAY,WAAW,CAAC,GACxB,aAAa,WAAW,CAAC;AAE7B,MAAI,iBAAa,uBAAS,KAAK,GAC3B,aAAa,eAAe,YAAY,CAAC,GACzC,YAAY,WAAW,CAAC,GACxB,WAAW,WAAW,CAAC;AAE3B,WAAS,iBAAiB,KAAK;AAC7B,aAAS,IAAI;AACb,QAAI;AAAc,mBAAa,GAAG;AAAA,EACpC;AAEA,WAAS,iBAAiB,KAAK;AAC7B,aAAS,KAAK;AACd,QAAI;AAAW,iBAAW,KAAK;AAC/B,QAAI;AAAc,mBAAa,GAAG;AAAA,EACpC;AAEA,WAAS,YAAY,KAAK;AACxB,aAAS,IAAI;AACb,QAAI;AAAS,cAAQ,GAAG;AAAA,EAC1B;AAEA,WAAS,WAAW,KAAK;AACvB,aAAS,KAAK;AACd,QAAI;AAAW,iBAAW,KAAK;AAC/B,QAAI;AAAQ,aAAO,GAAG;AAAA,EACxB;AAEA,WAAS,gBAAgB,KAAK;AAC5B,eAAW,IAAI;AACf,QAAI;AAAa,kBAAY,GAAG;AAAA,EAClC;AAEA,WAAS,cAAc,KAAK;AAC1B,eAAW,KAAK;AAChB,QAAI;AAAW,gBAAU,GAAG;AAAA,EAC9B;AAEA,SAAoB,aAAAD,QAAM,cAAc,QAAQ,SAAS;AAAA,IACvD;AAAA,IACA,UAAU;AAAA,IACV,WAAW,iBAAiB,OAAO,SAAS;AAAA,IAC5C,GAAG,UAAU;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO,MAAM,aAAa,YAAY,YAAY,YAAY,UAAU,SAAS;AAAA,EACnF,GAAG,SAAS,CAAC;AACf,CAAC;AACD,UAAU,cAAc;AACxB,UAAU,YAAY;AAAA,EACpB,WAAW,kBAAAC,QAAU;AAAA,EACrB,cAAc,kBAAAA,QAAU;AAAA,EACxB,cAAc,kBAAAA,QAAU;AAAA,EACxB,aAAa,kBAAAA,QAAU;AAAA,EACvB,WAAW,kBAAAA,QAAU;AAAA,EACrB,SAAS,kBAAAA,QAAU;AAAA,EACnB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,OAAO,kBAAAA,QAAU;AAAA,EACjB,WAAW,kBAAAA,QAAU;AACvB;AACA,IAAI,kBAAc,mBAAK,SAAS;AAEhC,IAAI,cAAc,CAAC,QAAQ,UAAU,QAAQ,WAAW;AACxD,IAAI,gBAAY,yBAAW,SAAU,MAAM,KAAK;AAC9C,MAAI,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,gBAAgB,WAC9C,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,iBAAiB,aACnD,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,CAAC,IAAI,EAAE,IAAI,WACzC,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,MAAI,kBAAc,yBAAW,UAAU,GACnC,OAAO,YAAY;AAEvB,SAAoB,aAAAD,QAAM,cAAc,QAAQ,SAAS;AAAA,IACvD;AAAA,IACA,GAAG,KAAK,UAAa,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,IACnC;AAAA,IACA;AAAA,IACA,WAAW,iBAAiB,OAAO,SAAS;AAAA,EAC9C,GAAG,SAAS,CAAC;AACf,CAAC;AACD,UAAU,cAAc;AACxB,UAAU,YAAY;AAAA,EACpB,MAAM,kBAAAC,QAAU;AAAA,EAChB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,MAAM,kBAAAA,QAAU;AAAA,EAChB,WAAW,kBAAAA,QAAU;AACvB;AACA,IAAI,kBAAc,mBAAK,SAAS;AAEhC,IAAI,cAAc,CAAC,OAAO;AAC1B,IAAI,qBAAiB,4BAAc;AACnC,IAAI,eAAe;AAAA,EACjB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,iBAAiB;AACnB;AAEA,IAAI,kBAAkB,SAASG,iBAAgB,MAAM;AACnD,MAAI,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,eAAe,YAC/C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,SAAoB,aAAAJ,QAAM,cAAc,eAAe,UAAU,SAAS;AAAA,IACxE;AAAA,EACF,GAAG,SAAS,CAAC;AACf;AAEA,gBAAgB,YAAY;AAAA,EAC1B,GAAG,kBAAAC,QAAU;AAAA,EACb,GAAG,kBAAAA,QAAU;AAAA,EACb,GAAG,kBAAAA,QAAU;AAAA,EACb,iBAAiB,kBAAAA,QAAU;AAC7B;AAEA,IAAI,oBAAoB,SAASI,qBAAoB;AACnD,aAAO,yBAAW,cAAc;AAClC;AAEA,SAAS,WAAW,MAAM;AACxB,MAAI,SAAS,KAAK,QACd,kBAAkB,KAAK,iBACvB,cAAc,KAAK,aACnB,YAAY,KAAK,WACjB,SAAS,KAAK,QACd,uBAAuB,KAAK,iBAC5B,kBAAkB,yBAAyB,SAAS,CAAC,CAAC,WAAW,SAAS,GAAG,CAAC,UAAU,QAAQ,CAAC,IAAI,sBACrG,mBAAmB,KAAK,aACxB,cAAc,qBAAqB,SAAS,CAAC,GAAG,CAAC,IAAI,kBACrD,YAAY,KAAK,MACjB,SAAS,cAAc,SAAS,IAAI;AAExC,MAAI,kBAAc,yBAAW,UAAU,GACnC,QAAQ,YAAY,OACpB,SAAS,YAAY,QACrBP,cAAa,YAAY;AAE7B,MAAI,UAAU,eAAe,QAAQ,CAAC,GAClC,MAAM,QAAQ,CAAC,GACf,MAAM,QAAQ,CAAC;AAEnB,MAAI,gBAAY,uBAAS;AAAA,IACvB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC,GACG,aAAa,eAAe,WAAW,CAAC,GACxC,WAAW,WAAW,CAAC,GACvB,cAAc,WAAW,CAAC;AAE9B,MAAI,mBAAe,qBAAO;AAAA,IACxB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,MAAI,aAAS,qBAAO;AACpB,MAAI,cAAU,qBAAO;AACrB,MAAI,mBAAe,qBAAO,KAAK;AAE/B,MAAI,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,IAAI,iBAAiB,CAAC,GACtB,IAAI,iBAAiB,CAAC;AAE1B,MAAI,KAAK,eAAe,GAAG,CAAC,GACxB,KAAK,GAAG,CAAC,GACT,KAAK,GAAG,CAAC;AAEb,MAAI,KAAK,eAAe,GAAG,CAAC,GACxBQ,MAAK,GAAG,CAAC,GACTC,MAAK,GAAG,CAAC;AAEb,MAAI,eAAe,eAAe,aAAa,CAAC,GAC5C,UAAU,aAAa,CAAC,GACxB,UAAU,aAAa,CAAC;AAE5B,8BAAU,WAAY;AACpB,QAAI,MAAMC,gBAAO,OAAO,OAAO;AAE/B,aAAS,gBAAgB,SAAS;AAChC,UAAI,CAAC,eAAe,aAAa;AAAS;AAC1C,kBAAY;AAAA,QACV,aAAaV,YAAW,OAAO,UAAU,OAAO,QAAQ,QAAQ,SAAS,CAAC;AAAA,QAC1E,MAAM,QAAQ,UAAU;AAAA,MAC1B,GAAG,OAAO;AAAA,IACZ;AAEA,aAAS,WAAW,SAAS;AAC3B,UAAI,aAAa;AAAS;AAC1B,UAAIW,aAAY,QAAQ,WACpB,cAAc,QAAQ;AAC1B,kBAAY;AAAA,QACV,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,QACb,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,CAAC;AAAQ;AACb,aAAO;AAAA,QACL,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,QACb,MAAMA,WAAU;AAAA,QAChB,UAAU;AAAA,MACZ,GAAG,OAAO;AAAA,IACZ;AAEA,aAAS,cAAc,SAAS;AAC9B,UAAI,aAAa,SAAS;AACxB,qBAAa,UAAU;AACvB;AAAA,MACF;AAEA,UAAI,qBAAqBX,YAAW,OAAO,UAAU,OAAO,QAAQ,QAAQ,SAAS,CAAC,GAClF,sBAAsB,eAAe,oBAAoB,CAAC,GAC1D,IAAI,oBAAoB,CAAC,GACzB,IAAI,oBAAoB,CAAC;AAE7B,mBAAa,UAAU;AAAA,QACrB;AAAA,QACA;AAAA,QACA,GAAG,QAAQ,UAAU;AAAA,MACvB;AACA,UAAI,CAAC;AAAW;AAChB,gBAAU;AAAA,QACR,aAAa,CAAC,GAAG,CAAC;AAAA,QAClB,MAAM,QAAQ,UAAU;AAAA,MAC1B,GAAG,OAAO;AAAA,IACZ;AAEA,aAAS,WAAW,SAAS;AAC3B,UAAI,iBAAiB;AACnB,eAAO,gBAAgB,OAAO;AAAA,MAChC;AAEA,aAAO,UAAU,CAAC,QAAQ,WAAW,CAAC,QAAQ,SAAS;AAAA,IACzD;AAEA,QAAIY,UAASC,cAAK,EAAE,OAAO,UAAU,EAAE,YAAY,CAAC,SAAS,OAAO,CAAC,EAAE,gBAAgB,CAAC,CAAC,IAAI,EAAE,GAAG,CAACL,KAAIC,GAAE,CAAC,CAAC,EAAE,GAAG,SAAS,eAAe,EAAE,GAAG,QAAQ,UAAU,EAAE,GAAG,OAAO,aAAa;AACxL,YAAQ,UAAUG;AAClB,QAAI,KAAKA,OAAM;AAAA,EACjB,GAAG,CAAC,OAAO,QAAQ,IAAI,IAAIJ,KAAIC,KAAI,SAAS,SAAST,aAAY,aAAa,QAAQ,WAAW,eAAe,CAAC;AACjH,8BAAU,WAAY;AACpB,QAAI,QAAQ,aAAa,QAAQ,KAAK,QAAQ,aAAa,QAAQ,KAAK,WAAW,aAAa,QAAQ;AAAG;AAC3G,QAAI,SAASA,YAAW,CAAC,KAAK,GAAG,CAAC;AAClC,QAAI,IAAI,OAAO,CAAC,IAAI;AACpB,QAAI,IAAI,OAAO,CAAC,IAAI;AACpB,QAAI,MAAMU,gBAAO,OAAO,OAAO;AAC/B,iBAAa,UAAU;AACvB,QAAI,KAAK,QAAQ,QAAQ,WAAW,SAAa,UAAU,QAAQ,IAAI,GAAG,SAAS,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC;AACvG,gBAAY;AAAA,MACV,GAAG,QAAQ,IAAI;AAAA,MACf,GAAG,SAAS,IAAI;AAAA,MAChB,GAAG;AAAA,IACL,CAAC;AACD,iBAAa,UAAU;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF,GAAG,CAAC,KAAK,KAAK,QAAQ,OAAO,QAAQV,WAAU,CAAC;AAChD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,iBAAiB,aAAa,OAAO,SAAS,GAAG,GAAG,EAAE,OAAO,SAAS,GAAG,UAAU,EAAE,OAAO,SAAS,GAAG,GAAG;AAAA,EAC7G;AACF;AAEA,IAAI,cAAc,CAAC,UAAU,QAAQ,WAAW,WAAW,mBAAmB,mBAAmB,eAAe,UAAU,aAAa,WAAW;AAClJ,IAAI,oBAAgB,yBAAW,SAAU,MAAM,KAAK;AAClD,MAAI,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,CAAC,GAAG,CAAC,IAAI,aAC3C,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,IAAI,WAClC,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAS,IAAI,cACxC,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAS,IAAI,cACxC,kBAAkB,KAAK,iBACvB,kBAAkB,KAAK,iBACvB,cAAc,KAAK,aACnB,SAAS,KAAK,QACd,YAAY,KAAK,WACjB,YAAY,KAAK,WACjB,YAAY,yBAAyB,MAAM,WAAW;AAE1D,MAAI,kBAAc,yBAAW,UAAU,GACnC,QAAQ,YAAY,OACpB,SAAS,YAAY;AAEzB,MAAI,cAAc,WAAW;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,CAAC,SAAS,OAAO;AAAA,IAC9B;AAAA,IACA;AAAA,EACF,CAAC,GACG,SAAS,YAAY,QACrB,kBAAkB,YAAY,iBAC9B,WAAW,YAAY;AAE3B,SAAoB,aAAAE,QAAM,cAAc,iBAAiB;AAAA,IACvD,OAAO;AAAA,MACL,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,MACZ;AAAA,IACF;AAAA,EACF,GAAgB,aAAAA,QAAM,cAAc,KAAK;AAAA,IACvC,KAAK;AAAA,EACP,GAAgB,aAAAA,QAAM,cAAc,QAAQ;AAAA,IAC1C;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC,GAAgB,aAAAA,QAAM,cAAc,KAAK,SAAS;AAAA,IACjD;AAAA,IACA,WAAW;AAAA,IACX,WAAW,sBAAsB,OAAO,SAAS;AAAA,EACnD,GAAG,SAAS,CAAC,CAAC,CAAC;AACjB,CAAC;AACD,cAAc,cAAc;AAC5B,cAAc,YAAY;AAAA,EACxB,QAAQ,kBAAAC,QAAU;AAAA,EAClB,MAAM,kBAAAA,QAAU;AAAA,EAChB,SAAS,kBAAAA,QAAU;AAAA,EACnB,SAAS,kBAAAA,QAAU;AAAA,EACnB,iBAAiB,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,KAAK;AAAA,EAClD,aAAa,kBAAAA,QAAU;AAAA,EACvB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,WAAW,kBAAAA,QAAU;AAAA,EACrB,WAAW,kBAAAA,QAAU;AACvB;AAEA,IAAI,cAAc,CAAC,MAAM,QAAQ,UAAU,eAAe,WAAW;AACrE,IAAI,aAAS,yBAAW,SAAU,MAAM,KAAK;AAC3C,MAAI,UAAU,KAAK,IACfW,MAAK,YAAY,SAAS,eAAe,SACzC,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,gBAAgB,WAC9C,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,iBAAiB,aACnD,mBAAmB,KAAK,aACxB,cAAc,qBAAqB,SAAS,MAAM,kBAClD,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,MAAI,kBAAc,yBAAW,UAAU,GACnC,OAAO,YAAY;AAEvB,MAAI,iBAAa,sBAAQ,WAAY;AACnC,WAAO,KAAK;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,IAAI,CAAC;AACT,SAAoB,aAAAZ,QAAM,cAAc,uBAAU,MAAmB,aAAAA,QAAM,cAAc,QAAQ,MAAmB,aAAAA,QAAM,cAAc,YAAY;AAAA,IAClJ,IAAIY;AAAA,EACN,GAAgB,aAAAZ,QAAM,cAAc,QAAQ;AAAA,IAC1C,GAAG;AAAA,EACL,CAAC,CAAC,CAAC,GAAgB,aAAAA,QAAM,cAAc,QAAQ,SAAS;AAAA,IACtD;AAAA,IACA,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,IACA,WAAW,cAAc,OAAO,SAAS;AAAA,EAC3C,GAAG,SAAS,CAAC,CAAC;AAChB,CAAC;AACD,OAAO,cAAc;AACrB,OAAO,YAAY;AAAA,EACjB,IAAI,kBAAAC,QAAU;AAAA,EACd,MAAM,kBAAAA,QAAU;AAAA,EAChB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,aAAa,kBAAAA,QAAU;AAAA,EACvB,WAAW,kBAAAA,QAAU;AACvB;AACA,IAAI,eAAW,mBAAK,MAAM;AAE1B,IAAI,cAAc,CAAC,eAAe,YAAY,gBAAgB,gBAAgB,eAAe,aAAa,WAAW,UAAU,SAAS,WAAW;AACnJ,IAAI,aAAS,yBAAW,SAAU,MAAM,KAAK;AAC3C,MAAIY,eAAc,KAAK,aACnBV,YAAW,KAAK,UAChB,eAAe,KAAK,cACpB,eAAe,KAAK,cACpB,cAAc,KAAK,aACnB,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,SAAS,KAAK,QACd,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,CAAC,IAAI,YACrC,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,MAAI,kBAAc,yBAAW,UAAU,GACnCL,cAAa,YAAY;AAE7B,MAAI,gBAAY,uBAAS,KAAK,GAC1B,aAAa,eAAe,WAAW,CAAC,GACxC,YAAY,WAAW,CAAC,GACxB,aAAa,WAAW,CAAC;AAE7B,MAAI,iBAAa,uBAAS,KAAK,GAC3B,aAAa,eAAe,YAAY,CAAC,GACzC,YAAY,WAAW,CAAC,GACxB,WAAW,WAAW,CAAC;AAE3B,MAAI,cAAcA,YAAWe,YAAW,GACpC,eAAe,eAAe,aAAa,CAAC,GAC5C,IAAI,aAAa,CAAC,GAClB,IAAI,aAAa,CAAC;AAEtB,WAAS,iBAAiB,KAAK;AAC7B,aAAS,IAAI;AACb,QAAI;AAAc,mBAAa,GAAG;AAAA,EACpC;AAEA,WAAS,iBAAiB,KAAK;AAC7B,aAAS,KAAK;AACd,QAAI;AAAW,iBAAW,KAAK;AAC/B,QAAI;AAAc,mBAAa,GAAG;AAAA,EACpC;AAEA,WAAS,YAAY,KAAK;AACxB,aAAS,IAAI;AACb,QAAI;AAAS,cAAQ,GAAG;AAAA,EAC1B;AAEA,WAAS,WAAW,KAAK;AACvB,aAAS,KAAK;AACd,QAAI;AAAW,iBAAW,KAAK;AAC/B,QAAI;AAAQ,aAAO,GAAG;AAAA,EACxB;AAEA,WAAS,gBAAgB,KAAK;AAC5B,eAAW,IAAI;AACf,QAAI;AAAa,kBAAY,GAAG;AAAA,EAClC;AAEA,WAAS,cAAc,KAAK;AAC1B,eAAW,KAAK;AAChB,QAAI;AAAW,gBAAU,GAAG;AAAA,EAC9B;AAEA,SAAoB,aAAAb,QAAM,cAAc,KAAK,SAAS;AAAA,IACpD;AAAA,IACA,WAAW,aAAa,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,GAAG;AAAA,IACrD,WAAW,cAAc,OAAO,SAAS;AAAA,IACzC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO,MAAM,aAAa,YAAY,YAAY,YAAY,UAAU,SAAS;AAAA,EACnF,GAAG,SAAS,GAAGG,SAAQ;AACzB,CAAC;AACD,OAAO,cAAc;AACrB,OAAO,YAAY;AAAA,EACjB,aAAa,kBAAAF,QAAU;AAAA,EACvB,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC;AAAA,EACjF,cAAc,kBAAAA,QAAU;AAAA,EACxB,cAAc,kBAAAA,QAAU;AAAA,EACxB,aAAa,kBAAAA,QAAU;AAAA,EACvB,WAAW,kBAAAA,QAAU;AAAA,EACrB,SAAS,kBAAAA,QAAU;AAAA,EACnB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,OAAO,kBAAAA,QAAU;AAAA,EACjB,WAAW,kBAAAA,QAAU;AACvB;AAEA,IAAI,cAAc,CAAC,QAAQ,MAAM,eAAe,UAAU,eAAe,QAAQ,WAAW;AAC5F,IAAI,WAAO,yBAAW,SAAU,MAAM,KAAK;AACzC,MAAI,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,CAAC,GAAG,CAAC,IAAI,WACvC,UAAU,KAAK,IACf,KAAK,YAAY,SAAS,CAAC,GAAG,CAAC,IAAI,SACnCY,eAAc,KAAK,aACnB,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,iBAAiB,aACnD,mBAAmB,KAAK,aACxB,cAAc,qBAAqB,SAAS,IAAI,kBAChD,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,gBAAgB,WAC9C,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,WAAW;AAE1D,MAAI,kBAAc,yBAAW,UAAU,GACnC,OAAO,YAAY;AAEvB,MAAI,WAAW;AAAA,IACb,MAAM;AAAA,IACN,aAAaA,gBAAe,CAAC,MAAM,EAAE;AAAA,EACvC;AACA,SAAoB,aAAAb,QAAM,cAAc,QAAQ,SAAS;AAAA,IACvD;AAAA,IACA,GAAG,KAAK,QAAQ;AAAA,IAChB,WAAW,YAAY,OAAO,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,SAAS,CAAC;AACf,CAAC;AACD,KAAK,cAAc;AACnB,KAAK,YAAY;AAAA,EACf,MAAM,kBAAAC,QAAU;AAAA,EAChB,IAAI,kBAAAA,QAAU;AAAA,EACd,aAAa,kBAAAA,QAAU;AAAA,EACvB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,aAAa,kBAAAA,QAAU;AAAA,EACvB,MAAM,kBAAAA,QAAU;AAAA,EAChB,WAAW,kBAAAA,QAAU;AACvB;AAEA,IAAI,YAAY,CAAC,WAAW,YAAY,kBAAkB,MAAM,MAAM,SAAS,WAAW;AAC1F,IAAI,iBAAa,yBAAW,SAAU,MAAM,KAAK;AAC/C,MAAI,UAAU,KAAK,SACfE,YAAW,KAAK,UAChB,iBAAiB,KAAK,gBACtB,UAAU,KAAK,IACf,KAAK,YAAY,SAAS,KAAK,SAC/B,UAAU,KAAK,IACf,KAAK,YAAY,SAAS,KAAK,SAC/B,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,IAAI,YACpC,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,YAAY,yBAAyB,MAAM,SAAS;AAExD,MAAI,kBAAc,yBAAW,UAAU,GACnCL,cAAa,YAAY;AAE7B,MAAI,cAAcA,YAAW,OAAO,GAChC,eAAe,eAAe,aAAa,CAAC,GAC5C,IAAI,aAAa,CAAC,GAClB,IAAI,aAAa,CAAC;AAEtB,MAAI,gBAAgB,oBAAoB,IAAI,IAAI,KAAK;AACrD,SAAoB,aAAAE,QAAM,cAAc,KAAK,SAAS;AAAA,IACpD;AAAA,IACA,WAAW,aAAa,OAAO,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,GAAG;AAAA,IAC/D,WAAW,kBAAkB,OAAO,SAAS;AAAA,EAC/C,GAAG,SAAS,GAAgB,aAAAA,QAAM,cAAc,QAAQ,SAAS;AAAA,IAC/D,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,GAAG,cAAc,CAAC,GAAGG,SAAQ;AAC/B,CAAC;AACD,WAAW,cAAc;AACzB,WAAW,YAAY;AAAA,EACrB,SAAS,kBAAAF,QAAU;AAAA,EACnB,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC;AAAA,EACjF,IAAI,kBAAAA,QAAU;AAAA,EACd,IAAI,kBAAAA,QAAU;AAAA,EACd,OAAO,kBAAAA,QAAU;AAAA,EACjB,gBAAgB,kBAAAA,QAAU;AAAA,EAC1B,WAAW,kBAAAA,QAAU;AACvB;", "names": ["circle_default", "identity_default", "object", "coordinates", "object", "cartesian", "spherical", "lambda0", "lambda00", "phi00", "sign", "lambda1", "range", "feature", "lambda00", "phi00", "object", "coordinates", "circle", "compareIntersection", "n", "p0", "start", "angle", "lambda0", "phi0", "sinPhi0", "cosPhi0", "lambda1", "phi1", "sign", "start", "point", "lambda0", "phi0", "lambda1", "phi1", "cosPhi0", "circle_default", "lambda0", "lambda1", "phi0", "phi1", "delta", "code", "x0", "y0", "x1", "y1", "x0", "y0", "x1", "y1", "compareIntersection", "ring", "point", "b0", "b1", "visible", "linePoint", "x0", "y0", "x1", "y1", "lambda0", "sinPhi0", "cosPhi0", "object", "object", "coordinates", "y0", "y1", "y", "x0", "x1", "x", "X1", "X0", "Y1", "Y0", "graticule", "coordinates", "x0", "y0", "x1", "y1", "areaSum", "areaRingSum", "x0", "y0", "areaStream", "areaRingStart", "areaRingEnd", "areaPointFirst", "areaPoint", "area_default", "x0", "y0", "boundsStream", "boundsPoint", "bounds_default", "X0", "Y0", "Z0", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "x00", "y00", "x0", "y0", "centroidStream", "centroidPoint", "centroidLineStart", "centroidLineEnd", "centroidRingStart", "centroidRingEnd", "centroid_default", "lengthSum", "x00", "y00", "x0", "y0", "lengthStream", "lengthPointFirst", "lengthPoint", "projection", "object", "area_default", "bounds_default", "centroid_default", "projection", "object", "bounds_default", "x0", "y0", "lambda0", "b0", "x1", "y1", "lambda1", "b1", "lambda2", "lambda00", "x00", "y00", "linePoint", "transform", "x0", "y0", "x1", "y1", "projection", "circle_default", "object", "phi0", "phi1", "phi0", "cosPhi0", "y0", "y1", "coordinates", "object", "angle", "x0", "y0", "x1", "y1", "y0", "y1", "y0", "y1", "identity_default", "x0", "y0", "x1", "y1", "transform", "projection", "object", "identity_default", "transform_default", "transform", "identity_default", "x0", "y0", "o", "id", "object", "transform_default", "arcs", "coordinates", "start", "p0", "fragmentByEnd", "fragmentByStart", "object", "filter", "arcs", "noop", "document", "datum", "constant_default", "constant_default", "merge_default", "selection", "merge", "create", "create", "parseTypenames", "window", "dispatch_default", "merge_default", "dispatch_default", "select_default", "id", "root", "selection", "select_default", "dispatch", "now", "id", "get", "set", "start", "empty", "interrupt_default", "id", "set", "get", "transition", "interpolate_default", "attrRemove", "attrRemoveNS", "attrConstant", "attrConstantNS", "attrFunction", "attrFunctionNS", "attr_default", "interpolate_default", "id", "get", "id", "set", "get", "id", "set", "get", "id", "set", "filter_default", "merge_default", "transition", "merge", "id", "set", "on_default", "get", "id", "remove_default", "select_default", "id", "get", "selectAll_default", "id", "children", "inherit", "get", "Selection", "selection_default", "styleRemove", "styleConstant", "styleFunction", "id", "remove", "set", "style_default", "interpolate_default", "textConstant", "textFunction", "text_default", "inherit", "get", "id", "set", "id", "select_default", "selectAll_default", "filter_default", "merge_default", "selection_default", "on_default", "attr_default", "style_default", "text_default", "remove_default", "polyIn", "custom", "polyOut", "polyInOut", "pi", "halfPi", "custom", "backIn", "backOut", "backInOut", "tau", "custom", "elasticIn", "a", "p", "elasticOut", "elasticInOut", "id", "transition_default", "interrupt_default", "transition_default", "constant_default", "transform", "dispatch", "nopropagation", "noevent_default", "transform", "zoom_default", "filter", "selection", "p0", "extent", "transition", "select_default", "noevent_default", "x0", "y0", "nopropagation", "event", "constant_default", "object", "obj", "makeProjection", "projection", "MapProvider", "React", "PropTypes", "useMapContext", "children", "ZoomPanProvider", "useZoomPanContext", "b1", "b2", "select_default", "transform", "zoom$1", "zoom_default", "id", "coordinates"]}