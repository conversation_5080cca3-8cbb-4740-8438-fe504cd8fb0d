import axios from 'axios'
import MockProductService from './MockProductService'

const API_URL = 'http://localhost:8000/api/products'
// Use environment variable to determine if we should use mock data
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'

class ProductService {
  // Get all products with optional filters
  async getProducts(filters = {}) {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 700));

      // Mock product data
      const mockProducts = [
        {
          id: 1,
          name: 'Premium Wheat',
          description: 'High-quality wheat grain suitable for bread and pastry production.',
          category: 'Grains',
          price: 320.45,
          unit: 'ton',
          quantity: 500,
          origin: 'United States',
          image_url: '/images/product-placeholder.jpg'
        },
        {
          id: 2,
          name: 'Organic Rice',
          description: 'Certified organic rice grown without pesticides or chemical fertilizers.',
          category: 'Grains',
          price: 420.75,
          unit: 'ton',
          quantity: 300,
          origin: 'Thailand',
          image_url: '/images/product-placeholder.jpg'
        },
        {
          id: 3,
          name: 'Yellow Corn',
          description: 'Non-GMO yellow corn suitable for animal feed and industrial use.',
          category: 'Grains',
          price: 180.30,
          unit: 'ton',
          quantity: 800,
          origin: 'Brazil',
          image_url: '/images/product-placeholder.jpg'
        },
        {
          id: 4,
          name: 'Soybeans',
          description: 'High-protein soybeans for food production and animal feed.',
          category: 'Legumes',
          price: 520.80,
          unit: 'ton',
          quantity: 400,
          origin: 'Argentina',
          image_url: '/images/product-placeholder.jpg'
        },
        {
          id: 5,
          name: 'Fresh Apples',
          description: 'Crisp and juicy apples harvested at peak ripeness.',
          category: 'Fruits',
          price: 1200.50,
          unit: 'ton',
          quantity: 100,
          origin: 'New Zealand',
          image_url: '/images/product-placeholder.jpg'
        },
        {
          id: 6,
          name: 'Organic Potatoes',
          description: 'Versatile organic potatoes suitable for various culinary applications.',
          category: 'Vegetables',
          price: 450.25,
          unit: 'ton',
          quantity: 250,
          origin: 'Ireland',
          image_url: '/images/product-placeholder.jpg'
        }
      ];

      // Apply filters if provided
      let filteredProducts = [...mockProducts];

      if (filters.category) {
        filteredProducts = filteredProducts.filter(p => p.category.toLowerCase() === filters.category.toLowerCase());
      }

      if (filters.origin) {
        filteredProducts = filteredProducts.filter(p => p.origin.toLowerCase().includes(filters.origin.toLowerCase()));
      }

      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredProducts = filteredProducts.filter(p =>
          p.name.toLowerCase().includes(searchTerm) ||
          p.description.toLowerCase().includes(searchTerm)
        );
      }

      // Apply limit if provided
      if (filters.limit && !isNaN(parseInt(filters.limit))) {
        filteredProducts = filteredProducts.slice(0, parseInt(filters.limit));
      }

      return {
        data: {
          status: 'success',
          data: filteredProducts,
          meta: {
            total: filteredProducts.length,
            page: 1,
            per_page: filteredProducts.length
          }
        }
      };
    }

    const params = new URLSearchParams()

    // Add filters to params
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params.append(key, filters[key])
      }
    })

    const response = await axios.get(API_URL, { params })
    return response.data
  }

  // Get a single product by ID
  async getProduct(id) {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Get products from the getProducts method
      const allProductsResponse = await this.getProducts();
      const allProducts = allProductsResponse.data.data;

      // Find the product with the matching ID
      const product = allProducts.find(p => p.id === parseInt(id));

      if (!product) {
        return {
          data: {
            status: 'error',
            message: 'Product not found'
          }
        };
      }

      return {
        data: {
          status: 'success',
          data: product
        }
      };
    }

    const response = await axios.get(`${API_URL}/${id}`)
    return response.data
  }

  // Create a new product
  async createProduct(productData) {
    // Handle file upload for image
    const formData = new FormData()

    Object.keys(productData).forEach(key => {
      if (key === 'image' && productData[key] instanceof File) {
        formData.append(key, productData[key])
      } else {
        formData.append(key, productData[key])
      }
    })

    const response = await axios.post(API_URL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data
  }

  // Update an existing product
  async updateProduct(id, productData) {
    // Handle file upload for image
    const formData = new FormData()

    Object.keys(productData).forEach(key => {
      if (key === 'image' && productData[key] instanceof File) {
        formData.append(key, productData[key])
      } else if (productData[key] !== undefined) {
        formData.append(key, productData[key])
      }
    })

    // Use PUT method with FormData
    formData.append('_method', 'PUT')

    const response = await axios.post(`${API_URL}/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data
  }

  // Delete a product
  async deleteProduct(id) {
    const response = await axios.delete(`${API_URL}/${id}`)
    return response.data
  }

  // Get products by category
  async getProductsByCategory() {
    if (USE_MOCK_DATA) {
      return MockProductService.getProductsByCategory()
    }

    const response = await axios.get(`${API_URL}/categories/count`)
    return response.data
  }

  // Get public products (for filters, etc.)
  async getPublicProducts() {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Mock product types
      const mockProductTypes = [
        { id: 1, type: 'Wheat', category: 'Grains' },
        { id: 2, type: 'Rice', category: 'Grains' },
        { id: 3, type: 'Corn', category: 'Grains' },
        { id: 4, type: 'Soybeans', category: 'Legumes' },
        { id: 5, type: 'Coffee', category: 'Beverages' },
        { id: 6, type: 'Cotton', category: 'Fibers' },
        { id: 7, type: 'Sugar', category: 'Sweeteners' },
        { id: 8, type: 'Cocoa', category: 'Beverages' },
        { id: 9, type: 'Palm Oil', category: 'Oils' },
        { id: 10, type: 'Rubber', category: 'Industrial' }
      ];

      return {
        data: {
          status: 'success',
          data: mockProductTypes
        }
      };
    }

    const response = await axios.get(`${API_URL}/public`)
    return response.data
  }
}

export default new ProductService()
