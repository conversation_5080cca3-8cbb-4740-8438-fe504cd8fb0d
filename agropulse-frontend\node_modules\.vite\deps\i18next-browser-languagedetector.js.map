{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/classCallCheck.js", "../../@babel/runtime/helpers/esm/typeof.js", "../../@babel/runtime/helpers/esm/toPrimitive.js", "../../@babel/runtime/helpers/esm/toPropertyKey.js", "../../@babel/runtime/helpers/esm/createClass.js", "../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js"], "sourcesContent": ["function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nexport { _typeof as default };", "import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "import _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\n\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nfunction defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\n\n// eslint-disable-next-line no-control-regex\nvar fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nvar serializeCookie = function serializeCookie(name, val, options) {\n  var opt = options || {};\n  opt.path = opt.path || '/';\n  var value = encodeURIComponent(val);\n  var str = \"\".concat(name, \"=\").concat(value);\n  if (opt.maxAge > 0) {\n    var maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += \"; Max-Age=\".concat(Math.floor(maxAge));\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += \"; Domain=\".concat(opt.domain);\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += \"; Path=\".concat(opt.path);\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += \"; Expires=\".concat(opt.expires.toUTCString());\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  return str;\n};\nvar cookie = {\n  create: function create(name, value, minutes, domain) {\n    var cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read: function read(name) {\n    var nameEQ = \"\".concat(name, \"=\");\n    var ca = document.cookie.split(';');\n    for (var i = 0; i < ca.length; i++) {\n      var c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove: function remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      var c = cookie.read(options.lookupCookie);\n      if (c) found = c;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      cookie.create(options.lookupCookie, lng, options.cookieMinutes, options.cookieDomain, options.cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var search = window.location.search;\n      if (!window.location.search && window.location.hash && window.location.hash.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      var query = search.substring(1);\n      var params = query.split('&');\n      for (var i = 0; i < params.length; i++) {\n        var pos = params[i].indexOf('=');\n        if (pos > 0) {\n          var key = params[i].substring(0, pos);\n          if (key === options.lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar hasLocalStorageSupport = null;\nvar localStorageAvailable = function localStorageAvailable() {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = window !== 'undefined' && window.localStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      var lng = window.localStorage.getItem(options.lookupLocalStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(options.lookupLocalStorage, lng);\n    }\n  }\n};\n\nvar hasSessionStorageSupport = null;\nvar sessionStorageAvailable = function sessionStorageAvailable() {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = window !== 'undefined' && window.sessionStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      var lng = window.sessionStorage.getItem(options.lookupSessionStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(options.lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup: function lookup(options) {\n    var found = [];\n    if (typeof navigator !== 'undefined') {\n      if (navigator.languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (var i = 0; i < navigator.languages.length; i++) {\n          found.push(navigator.languages[i]);\n        }\n      }\n      if (navigator.userLanguage) {\n        found.push(navigator.userLanguage);\n      }\n      if (navigator.language) {\n        found.push(navigator.language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  lookup: function lookup(options) {\n    var found;\n    var htmlTag = options.htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (htmlTag && typeof htmlTag.getAttribute === 'function') {\n      found = htmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n      if (language instanceof Array) {\n        if (typeof options.lookupFromPathIndex === 'number') {\n          if (typeof language[options.lookupFromPathIndex] !== 'string') {\n            return undefined;\n          }\n          found = language[options.lookupFromPathIndex].replace('/', '');\n        } else {\n          found = language[0].replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup: function lookup(options) {\n    // If given get the subdomain index else 1\n    var lookupFromSubdomainIndex = typeof options.lookupFromSubdomainIndex === 'number' ? options.lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group macht which sould be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    var language = typeof window !== 'undefined' && window.location && window.location.hostname && window.location.hostname.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[lookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nvar canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nvar order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nfunction getDefaults() {\n  return {\n    order: order,\n    lookupQuerystring: 'lng',\n    lookupCookie: 'i18next',\n    lookupLocalStorage: 'i18nextLng',\n    lookupSessionStorage: 'i18nextLng',\n    // cache user language\n    caches: ['localStorage'],\n    excludeCacheFor: ['cimode'],\n    // cookieMinutes: 10,\n    // cookieDomain: 'myDomain'\n\n    convertDetectedLanguage: function convertDetectedLanguage(l) {\n      return l;\n    }\n  };\n}\nvar Browser = /*#__PURE__*/function () {\n  function Browser(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Browser);\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  return _createClass(Browser, [{\n    key: \"init\",\n    value: function init(services) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services || {\n        languageUtils: {}\n      }; // this way the language detector can be used without i18next\n      this.options = defaults(options, this.options || {}, getDefaults());\n      if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n        this.options.convertDetectedLanguage = function (l) {\n          return l.replace('-', '_');\n        };\n      }\n\n      // backwards compatibility\n      if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n      this.i18nOptions = i18nOptions;\n      this.addDetector(cookie$1);\n      this.addDetector(querystring);\n      this.addDetector(localStorage);\n      this.addDetector(sessionStorage);\n      this.addDetector(navigator$1);\n      this.addDetector(htmlTag);\n      this.addDetector(path);\n      this.addDetector(subdomain);\n    }\n  }, {\n    key: \"addDetector\",\n    value: function addDetector(detector) {\n      this.detectors[detector.name] = detector;\n      return this;\n    }\n  }, {\n    key: \"detect\",\n    value: function detect(detectionOrder) {\n      var _this = this;\n      if (!detectionOrder) detectionOrder = this.options.order;\n      var detected = [];\n      detectionOrder.forEach(function (detectorName) {\n        if (_this.detectors[detectorName]) {\n          var lookup = _this.detectors[detectorName].lookup(_this.options);\n          if (lookup && typeof lookup === 'string') lookup = [lookup];\n          if (lookup) detected = detected.concat(lookup);\n        }\n      });\n      detected = detected.map(function (d) {\n        return _this.options.convertDetectedLanguage(d);\n      });\n      if (this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n      return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n    }\n  }, {\n    key: \"cacheUserLanguage\",\n    value: function cacheUserLanguage(lng, caches) {\n      var _this2 = this;\n      if (!caches) caches = this.options.caches;\n      if (!caches) return;\n      if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n      caches.forEach(function (cacheName) {\n        if (_this2.detectors[cacheName]) _this2.detectors[cacheName].cacheUserLanguage(lng, _this2.options);\n      });\n    }\n  }]);\n}();\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n"], "mappings": ";;;AAAA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,EAAE,aAAa;AAAI,UAAM,IAAI,UAAU,mCAAmC;AAChF;;;ACFA,SAAS,QAAQ,GAAG;AAClB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQ,CAAC;AACd;;;ACPA,SAAS,YAAY,GAAG,GAAG;AACzB,MAAI,YAAY,QAAQ,CAAC,KAAK,CAAC;AAAG,WAAO;AACzC,MAAI,IAAI,EAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,QAAI,YAAY,QAAQ,CAAC;AAAG,aAAO;AACnC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAC7C;;;ACRA,SAAS,cAAc,GAAG;AACxB,MAAI,IAAI,YAAY,GAAG,QAAQ;AAC/B,SAAO,YAAY,QAAQ,CAAC,IAAI,IAAI,IAAI;AAC1C;;;ACJA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,cAAc,EAAE,GAAG,GAAG,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,aAAa,GAAG,GAAG,GAAG;AAC7B,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACjH,UAAU;AAAA,EACZ,CAAC,GAAG;AACN;;;ACRA,IAAI,MAAM,CAAC;AACX,IAAI,OAAO,IAAI;AACf,IAAI,QAAQ,IAAI;AAChB,SAAS,SAAS,KAAK;AACrB,OAAK,KAAK,MAAM,KAAK,WAAW,CAAC,GAAG,SAAU,QAAQ;AACpD,QAAI,QAAQ;AACV,eAAS,QAAQ,QAAQ;AACvB,YAAI,IAAI,IAAI,MAAM;AAAW,cAAI,IAAI,IAAI,OAAO,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,IAAI,qBAAqB;AACzB,IAAI,kBAAkB,SAASC,iBAAgB,MAAM,KAAK,SAAS;AACjE,MAAI,MAAM,WAAW,CAAC;AACtB,MAAI,OAAO,IAAI,QAAQ;AACvB,MAAI,QAAQ,mBAAmB,GAAG;AAClC,MAAI,MAAM,GAAG,OAAO,MAAM,GAAG,EAAE,OAAO,KAAK;AAC3C,MAAI,IAAI,SAAS,GAAG;AAClB,QAAI,SAAS,IAAI,SAAS;AAC1B,QAAI,OAAO,MAAM,MAAM;AAAG,YAAM,IAAI,MAAM,2BAA2B;AACrE,WAAO,aAAa,OAAO,KAAK,MAAM,MAAM,CAAC;AAAA,EAC/C;AACA,MAAI,IAAI,QAAQ;AACd,QAAI,CAAC,mBAAmB,KAAK,IAAI,MAAM,GAAG;AACxC,YAAM,IAAI,UAAU,0BAA0B;AAAA,IAChD;AACA,WAAO,YAAY,OAAO,IAAI,MAAM;AAAA,EACtC;AACA,MAAI,IAAI,MAAM;AACZ,QAAI,CAAC,mBAAmB,KAAK,IAAI,IAAI,GAAG;AACtC,YAAM,IAAI,UAAU,wBAAwB;AAAA,IAC9C;AACA,WAAO,UAAU,OAAO,IAAI,IAAI;AAAA,EAClC;AACA,MAAI,IAAI,SAAS;AACf,QAAI,OAAO,IAAI,QAAQ,gBAAgB,YAAY;AACjD,YAAM,IAAI,UAAU,2BAA2B;AAAA,IACjD;AACA,WAAO,aAAa,OAAO,IAAI,QAAQ,YAAY,CAAC;AAAA,EACtD;AACA,MAAI,IAAI;AAAU,WAAO;AACzB,MAAI,IAAI;AAAQ,WAAO;AACvB,MAAI,IAAI,UAAU;AAChB,QAAI,WAAW,OAAO,IAAI,aAAa,WAAW,IAAI,SAAS,YAAY,IAAI,IAAI;AACnF,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO;AACP;AAAA,MACF;AACE,cAAM,IAAI,UAAU,4BAA4B;AAAA,IACpD;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,SAAS;AAAA,EACX,QAAQ,SAAS,OAAO,MAAM,OAAO,SAAS,QAAQ;AACpD,QAAI,gBAAgB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MACtF,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AACA,QAAI,SAAS;AACX,oBAAc,UAAU,oBAAI,KAAK;AACjC,oBAAc,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,IAAI,UAAU,KAAK,GAAI;AAAA,IACrF;AACA,QAAI;AAAQ,oBAAc,SAAS;AACnC,aAAS,SAAS,gBAAgB,MAAM,mBAAmB,KAAK,GAAG,aAAa;AAAA,EAClF;AAAA,EACA,MAAM,SAAS,KAAK,MAAM;AACxB,QAAI,SAAS,GAAG,OAAO,MAAM,GAAG;AAChC,QAAI,KAAK,SAAS,OAAO,MAAM,GAAG;AAClC,aAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,UAAI,IAAI,GAAG,CAAC;AACZ,aAAO,EAAE,OAAO,CAAC,MAAM;AAAK,YAAI,EAAE,UAAU,GAAG,EAAE,MAAM;AACvD,UAAI,EAAE,QAAQ,MAAM,MAAM;AAAG,eAAO,EAAE,UAAU,OAAO,QAAQ,EAAE,MAAM;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,OAAO,MAAM;AAC5B,SAAK,OAAO,MAAM,IAAI,EAAE;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,QAAQ,SAAS,OAAO,SAAS;AAC/B,QAAI;AACJ,QAAI,QAAQ,gBAAgB,OAAO,aAAa,aAAa;AAC3D,UAAI,IAAI,OAAO,KAAK,QAAQ,YAAY;AACxC,UAAI;AAAG,gBAAQ;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,SAAS,kBAAkB,KAAK,SAAS;AAC1D,QAAI,QAAQ,gBAAgB,OAAO,aAAa,aAAa;AAC3D,aAAO,OAAO,QAAQ,cAAc,KAAK,QAAQ,eAAe,QAAQ,cAAc,QAAQ,aAAa;AAAA,IAC7G;AAAA,EACF;AACF;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ,SAASC,QAAO,SAAS;AAC/B,QAAI;AACJ,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI,SAAS,OAAO,SAAS;AAC7B,UAAI,CAAC,OAAO,SAAS,UAAU,OAAO,SAAS,QAAQ,OAAO,SAAS,KAAK,QAAQ,GAAG,IAAI,IAAI;AAC7F,iBAAS,OAAO,SAAS,KAAK,UAAU,OAAO,SAAS,KAAK,QAAQ,GAAG,CAAC;AAAA,MAC3E;AACA,UAAI,QAAQ,OAAO,UAAU,CAAC;AAC9B,UAAI,SAAS,MAAM,MAAM,GAAG;AAC5B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,MAAM,OAAO,CAAC,EAAE,QAAQ,GAAG;AAC/B,YAAI,MAAM,GAAG;AACX,cAAI,MAAM,OAAO,CAAC,EAAE,UAAU,GAAG,GAAG;AACpC,cAAI,QAAQ,QAAQ,mBAAmB;AACrC,oBAAQ,OAAO,CAAC,EAAE,UAAU,MAAM,CAAC;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,yBAAyB;AAC7B,IAAI,wBAAwB,SAASC,yBAAwB;AAC3D,MAAI,2BAA2B;AAAM,WAAO;AAC5C,MAAI;AACF,6BAAyB,WAAW,eAAe,OAAO,iBAAiB;AAC3E,QAAI,UAAU;AACd,WAAO,aAAa,QAAQ,SAAS,KAAK;AAC1C,WAAO,aAAa,WAAW,OAAO;AAAA,EACxC,SAAS,GAAG;AACV,6BAAyB;AAAA,EAC3B;AACA,SAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,QAAQ,SAASD,QAAO,SAAS;AAC/B,QAAI;AACJ,QAAI,QAAQ,sBAAsB,sBAAsB,GAAG;AACzD,UAAI,MAAM,OAAO,aAAa,QAAQ,QAAQ,kBAAkB;AAChE,UAAI;AAAK,gBAAQ;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,SAASE,mBAAkB,KAAK,SAAS;AAC1D,QAAI,QAAQ,sBAAsB,sBAAsB,GAAG;AACzD,aAAO,aAAa,QAAQ,QAAQ,oBAAoB,GAAG;AAAA,IAC7D;AAAA,EACF;AACF;AAEA,IAAI,2BAA2B;AAC/B,IAAI,0BAA0B,SAASC,2BAA0B;AAC/D,MAAI,6BAA6B;AAAM,WAAO;AAC9C,MAAI;AACF,+BAA2B,WAAW,eAAe,OAAO,mBAAmB;AAC/E,QAAI,UAAU;AACd,WAAO,eAAe,QAAQ,SAAS,KAAK;AAC5C,WAAO,eAAe,WAAW,OAAO;AAAA,EAC1C,SAAS,GAAG;AACV,+BAA2B;AAAA,EAC7B;AACA,SAAO;AACT;AACA,IAAI,iBAAiB;AAAA,EACnB,MAAM;AAAA,EACN,QAAQ,SAASH,QAAO,SAAS;AAC/B,QAAI;AACJ,QAAI,QAAQ,wBAAwB,wBAAwB,GAAG;AAC7D,UAAI,MAAM,OAAO,eAAe,QAAQ,QAAQ,oBAAoB;AACpE,UAAI;AAAK,gBAAQ;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,SAASE,mBAAkB,KAAK,SAAS;AAC1D,QAAI,QAAQ,wBAAwB,wBAAwB,GAAG;AAC7D,aAAO,eAAe,QAAQ,QAAQ,sBAAsB,GAAG;AAAA,IACjE;AAAA,EACF;AACF;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ,SAASF,QAAO,SAAS;AAC/B,QAAI,QAAQ,CAAC;AACb,QAAI,OAAO,cAAc,aAAa;AACpC,UAAI,UAAU,WAAW;AAEvB,iBAAS,IAAI,GAAG,IAAI,UAAU,UAAU,QAAQ,KAAK;AACnD,gBAAM,KAAK,UAAU,UAAU,CAAC,CAAC;AAAA,QACnC;AAAA,MACF;AACA,UAAI,UAAU,cAAc;AAC1B,cAAM,KAAK,UAAU,YAAY;AAAA,MACnC;AACA,UAAI,UAAU,UAAU;AACtB,cAAM,KAAK,UAAU,QAAQ;AAAA,MAC/B;AAAA,IACF;AACA,WAAO,MAAM,SAAS,IAAI,QAAQ;AAAA,EACpC;AACF;AAEA,IAAI,UAAU;AAAA,EACZ,MAAM;AAAA,EACN,QAAQ,SAASA,QAAO,SAAS;AAC/B,QAAI;AACJ,QAAII,WAAU,QAAQ,YAAY,OAAO,aAAa,cAAc,SAAS,kBAAkB;AAC/F,QAAIA,YAAW,OAAOA,SAAQ,iBAAiB,YAAY;AACzD,cAAQA,SAAQ,aAAa,MAAM;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,QAAQ,SAASJ,QAAO,SAAS;AAC/B,QAAI;AACJ,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI,WAAW,OAAO,SAAS,SAAS,MAAM,iBAAiB;AAC/D,UAAI,oBAAoB,OAAO;AAC7B,YAAI,OAAO,QAAQ,wBAAwB,UAAU;AACnD,cAAI,OAAO,SAAS,QAAQ,mBAAmB,MAAM,UAAU;AAC7D,mBAAO;AAAA,UACT;AACA,kBAAQ,SAAS,QAAQ,mBAAmB,EAAE,QAAQ,KAAK,EAAE;AAAA,QAC/D,OAAO;AACL,kBAAQ,SAAS,CAAC,EAAE,QAAQ,KAAK,EAAE;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,YAAY;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,SAASA,QAAO,SAAS;AAE/B,QAAI,2BAA2B,OAAO,QAAQ,6BAA6B,WAAW,QAAQ,2BAA2B,IAAI;AAI7H,QAAI,WAAW,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS,YAAY,OAAO,SAAS,SAAS,MAAM,wDAAwD;AAGtL,QAAI,CAAC;AAAU,aAAO;AAEtB,WAAO,SAAS,wBAAwB;AAAA,EAC1C;AACF;AAGA,IAAI,aAAa;AACjB,IAAI;AAEF,WAAS;AACT,eAAa;AAEf,SAAS,GAAG;AAAC;AACb,IAAI,QAAQ,CAAC,eAAe,UAAU,gBAAgB,kBAAkB,aAAa,SAAS;AAC9F,IAAI,CAAC;AAAY,QAAM,OAAO,GAAG,CAAC;AAClC,SAAS,cAAc;AACrB,SAAO;AAAA,IACL;AAAA,IACA,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,sBAAsB;AAAA;AAAA,IAEtB,QAAQ,CAAC,cAAc;AAAA,IACvB,iBAAiB,CAAC,QAAQ;AAAA;AAAA;AAAA,IAI1B,yBAAyB,SAAS,wBAAwB,GAAG;AAC3D,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAI,UAAuB,WAAY;AACrC,WAASK,SAAQ,UAAU;AACzB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,oBAAgB,MAAMA,QAAO;AAC7B,SAAK,OAAO;AACZ,SAAK,YAAY,CAAC;AAClB,SAAK,KAAK,UAAU,OAAO;AAAA,EAC7B;AACA,SAAO,aAAaA,UAAS,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,UAAU;AAC7B,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACvF,WAAK,WAAW,YAAY;AAAA,QAC1B,eAAe,CAAC;AAAA,MAClB;AACA,WAAK,UAAU,SAAS,SAAS,KAAK,WAAW,CAAC,GAAG,YAAY,CAAC;AAClE,UAAI,OAAO,KAAK,QAAQ,4BAA4B,YAAY,KAAK,QAAQ,wBAAwB,QAAQ,OAAO,IAAI,IAAI;AAC1H,aAAK,QAAQ,0BAA0B,SAAU,GAAG;AAClD,iBAAO,EAAE,QAAQ,KAAK,GAAG;AAAA,QAC3B;AAAA,MACF;AAGA,UAAI,KAAK,QAAQ;AAAoB,aAAK,QAAQ,sBAAsB,KAAK,QAAQ;AACrF,WAAK,cAAc;AACnB,WAAK,YAAY,QAAQ;AACzB,WAAK,YAAY,WAAW;AAC5B,WAAK,YAAY,YAAY;AAC7B,WAAK,YAAY,cAAc;AAC/B,WAAK,YAAY,WAAW;AAC5B,WAAK,YAAY,OAAO;AACxB,WAAK,YAAY,IAAI;AACrB,WAAK,YAAY,SAAS;AAAA,IAC5B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,UAAU;AACpC,WAAK,UAAU,SAAS,IAAI,IAAI;AAChC,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,gBAAgB;AACrC,UAAI,QAAQ;AACZ,UAAI,CAAC;AAAgB,yBAAiB,KAAK,QAAQ;AACnD,UAAI,WAAW,CAAC;AAChB,qBAAe,QAAQ,SAAU,cAAc;AAC7C,YAAI,MAAM,UAAU,YAAY,GAAG;AACjC,cAAIL,UAAS,MAAM,UAAU,YAAY,EAAE,OAAO,MAAM,OAAO;AAC/D,cAAIA,WAAU,OAAOA,YAAW;AAAU,YAAAA,UAAS,CAACA,OAAM;AAC1D,cAAIA;AAAQ,uBAAW,SAAS,OAAOA,OAAM;AAAA,QAC/C;AAAA,MACF,CAAC;AACD,iBAAW,SAAS,IAAI,SAAU,GAAG;AACnC,eAAO,MAAM,QAAQ,wBAAwB,CAAC;AAAA,MAChD,CAAC;AACD,UAAI,KAAK,SAAS,cAAc;AAAuB,eAAO;AAC9D,aAAO,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,IAC7C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAASE,mBAAkB,KAAK,QAAQ;AAC7C,UAAI,SAAS;AACb,UAAI,CAAC;AAAQ,iBAAS,KAAK,QAAQ;AACnC,UAAI,CAAC;AAAQ;AACb,UAAI,KAAK,QAAQ,mBAAmB,KAAK,QAAQ,gBAAgB,QAAQ,GAAG,IAAI;AAAI;AACpF,aAAO,QAAQ,SAAU,WAAW;AAClC,YAAI,OAAO,UAAU,SAAS;AAAG,iBAAO,UAAU,SAAS,EAAE,kBAAkB,KAAK,OAAO,OAAO;AAAA,MACpG,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACJ,EAAE;AACF,QAAQ,OAAO;", "names": ["o", "serializeCookie", "lookup", "localStorageAvailable", "cacheUserLanguage", "sessionStorageAvailable", "htmlTag", "Browser"]}