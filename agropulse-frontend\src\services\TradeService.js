// src/services/TradeService.js
import axios from 'axios'
import MockTradeService from './MockTradeService'

const API_URL = '/api'
// Use environment variable to determine if we should use mock data
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'

class TradeService {
  // Get export seasons data
  async getExportSeasons(country = null) {
    if (USE_MOCK_DATA) {
      return MockTradeService.getExportSeasons(country)
    }

    const params = new URLSearchParams()
    if (country) {
      params.append('country', country)
    }
    const response = await axios.get(`${API_URL}/trade/export-seasons`, { params })
    return response.data
  }

  // Get customs requirements by country
  async getCustomsRequirements(country) {
    if (USE_MOCK_DATA) {
      return MockTradeService.getCustomsRequirements(country)
    }

    const response = await axios.get(`${API_URL}/trade/customs/${country}`)
    return response.data
  }

  // Get global demand data
  async getGlobalDemand(productType = null) {
    if (USE_MOCK_DATA) {
      return MockTradeService.getGlobalDemand(productType)
    }

    const params = new URLSearchParams()
    if (productType) {
      params.append('product_type', productType)
    }
    const response = await axios.get(`${API_URL}/trade/global-demand`, { params })
    return response.data
  }

  // Upload customs document
  async uploadCustomsDocument(documentData) {
    if (USE_MOCK_DATA) {
      return MockTradeService.uploadCustomsDocument(documentData)
    }

    const formData = new FormData()

    Object.keys(documentData).forEach(key => {
      if (key === 'file') {
        formData.append('document', documentData.file)
      } else {
        formData.append(key, documentData[key])
      }
    })

    const response = await axios.post(`${API_URL}/trade/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  // Get user's customs documents
  async getCustomsDocuments() {
    if (USE_MOCK_DATA) {
      return MockTradeService.getCustomsDocuments()
    }

    const response = await axios.get(`${API_URL}/trade/documents`)
    return response.data
  }

  // Get trade statistics
  async getTradeStatistics() {
    if (USE_MOCK_DATA) {
      return MockTradeService.getTradeStatistics()
    }

    const response = await axios.get(`${API_URL}/trade/statistics`)
    return response.data
  }

  // Get public products data
  async getPublicProducts() {
    if (USE_MOCK_DATA) {
      return MockTradeService.getPublicProducts()
    }

    const response = await axios.get(`${API_URL}/market-data/products`)
    return response.data
  }
}

export default new TradeService()