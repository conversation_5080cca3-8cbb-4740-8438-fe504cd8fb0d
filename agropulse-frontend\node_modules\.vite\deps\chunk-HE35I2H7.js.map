{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/extends.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };"], "mappings": ";AAAA,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK;AAAG,SAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;", "names": []}