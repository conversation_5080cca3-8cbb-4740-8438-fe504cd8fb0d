import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import { useTranslation } from 'react-i18next'
import { useAuth } from './contexts/AuthContext'

// Layouts
import MainLayout from './layouts/MainLayout'
import AuthLayout from './layouts/AuthLayout'
import DashboardLayout from './layouts/DashboardLayout'

// Public Pages
import Home from './pages/Home'
import Products from './pages/products/Products'
import ProductDetails from './pages/products/ProductDetails'
import MarketTrends from './pages/market/MarketTrends'
import NotFound from './pages/NotFound'

// Components
import RoleBasedDashboardRedirect from './components/RoleBasedDashboardRedirect'

// Auth Pages
import Login from './pages/auth/Login'
import Register from './pages/auth/Register'

// Dashboard Pages
import Dashboard from './pages/dashboard/Dashboard'
import FarmerDashboard from './pages/dashboard/FarmerDashboard'
import ImporterDashboard from './pages/dashboard/ImporterDashboard'
import ShippingDashboard from './pages/dashboard/ShippingDashboard'
import ModeratorDashboard from './pages/dashboard/ModeratorDashboard'
import AdminDashboard from './pages/dashboard/AdminDashboard'
import AIDashboard from './pages/dashboard/AIDashboard'
import DashboardProducts from './pages/products/Products'
import AddProduct from './pages/products/AddProduct'
import EditProduct from './pages/products/EditProduct'
import DashboardContracts from './pages/contracts/Contracts'
import ContractDetails from './pages/contracts/ContractDetails'
import AddContract from './pages/contracts/AddContract'
import EditContract from './pages/contracts/EditContract'
import DashboardShipments from './pages/shipments/Shipments.jsx'
import IncomingShipments from './pages/shipments/IncomingShipments.jsx'
import ShipmentDetails from './pages/shipments/ShipmentDetails.jsx'
import ShipmentTracking from './pages/shipments/ShipmentTracking.jsx'
import ShipmentAnalytics from './pages/shipments/ShipmentAnalytics.jsx'
import AddShipment from './pages/shipments/AddShipment.jsx'
import EditShipment from './pages/shipments/EditShipment.jsx'
import MarketAnalysis from './pages/market/MarketAnalysis'
import ProductForecast from './pages/market/ProductForecast'
import PriceForecast from './pages/market/PriceForecast'
import Recommendation from './pages/market/Recommendation'
import AdvancedMarketAnalytics from './pages/market/AdvancedMarketAnalytics'
import DeficitAnalysis from './pages/market/DeficitAnalysis'
import Profile from './pages/profile/Profile'
import EditProfile from './pages/profile/EditProfile'

// Advertisements Pages
import Advertisements from './pages/advertisements/Advertisements'
import AdvertisementDetails from './pages/advertisements/AdvertisementDetails'
import CreateAdvertisement from './pages/advertisements/CreateAdvertisement'
import EditAdvertisement from './pages/advertisements/EditAdvertisement'
import AdvertisementAnalytics from './pages/advertisements/AdvertisementAnalytics'
import ModeratorAdvertisements from './pages/moderator/ModeratorAdvertisements'

// Payments Pages
import Payments from './pages/payments/Payments.jsx'
import PaymentDetails from './pages/payments/PaymentDetails.jsx'
import PaymentInvoice from './pages/payments/PaymentInvoice.jsx'
import PaymentMethods from './pages/payments/PaymentMethods.jsx'
import PaymentDashboard from './pages/payments/PaymentDashboard.jsx'
import InvoiceManagement from './pages/payments/InvoiceManagement.jsx'

// Trade & Export/Import Dashboard Pages
import TradeExportImport from './pages/trade/TradeExportImport'
import ExportSeasons from './pages/trade/ExportSeasons'
import CustomsDocuments from './pages/trade/CustomsDocuments'
import GlobalDemand from './pages/trade/GlobalDemand'

// AI Pages
import PricePrediction from './pages/ai/PricePrediction'
import DemandSupplyAnalysis from './pages/ai/DemandSupplyAnalysis'
import ClimateImpactAnalysis from './pages/ai/ClimateImpactAnalysis'
import MissingProducts from './pages/ai/MissingProducts'
import SeasonalAnalysis from './pages/ai/SeasonalAnalysis'
import SmartPricing from './pages/ai/SmartPricing'
import Recommendations from './pages/ai/Recommendations'
import CountryAnalysis from './pages/ai/CountryAnalysis'
import AdvancedAnalytics from './pages/analytics/AdvancedAnalytics.jsx'

// New Features Imports
// Settings Pages
import UserSettings from './pages/settings/UserSettings'

// Admin Pages
import UserManagement from './pages/admin/UserManagement'
import UserPermissions from './pages/admin/UserPermissions'
import AccountActivation from './pages/admin/AccountActivation'
import SystemSettings from './pages/admin/SystemSettings'
import RolesPermissions from './pages/admin/RolesPermissions'
import Reports from './pages/admin/Reports'
import BusinessVerificationReview from './pages/admin/BusinessVerificationReview'

// Verification Pages
import BusinessVerificationPage from './pages/verification/BusinessVerificationPage'

// Reports & Analytics
import SalesReports from './pages/reports/SalesReports'
import SalesAnalytics from './pages/reports/SalesAnalytics'
import PerformanceReports from './pages/reports/PerformanceReports'
import ReportsAdvancedAnalytics from './pages/reports/AdvancedAnalytics'
import IncomeExpenseReports from './pages/reports/IncomeExpenseReports'
import SupplierCustomerReports from './pages/reports/SupplierCustomerReports'

// Inventory Management
import InventoryTracking from './pages/inventory/InventoryTracking'
import WarehouseManagement from './pages/inventory/WarehouseManagement'
import LowStockAlerts from './pages/inventory/LowStockAlerts'

// Messaging & Communications
import InternalMessaging from './pages/communications/InternalMessaging'
import SystemNotifications from './pages/communications/SystemNotifications'
import SupportCenter from './pages/communications/SupportCenter'
import Forums from './pages/communications/Forums'
import ForumDetail from './pages/communications/ForumDetail'

// Ratings & Reviews
import ProductRatings from './pages/ratings/ProductRatings'
import SellerRatings from './pages/ratings/SellerRatings'
import ReviewManagement from './pages/ratings/ReviewManagement'

// Store Pages
import CreateStore from './pages/stores/CreateStore.jsx'
import StoreDetails from './pages/stores/StoreDetails.jsx'
import StoreManagement from './pages/stores/StoreManagement.jsx'
import StoreRatings from './pages/stores/StoreRatings.jsx'

// Auctions
import LiveAuctions from './pages/auctions/LiveAuctions'
import AgriculturalAuctions from './pages/auctions/AgriculturalAuctions'
import BidTracking from './pages/auctions/BidTracking'

// Agricultural Cooperatives
import CooperativeManagement from './pages/cooperatives/CooperativeManagement'
import ResourceSharing from './pages/cooperatives/ResourceSharing'
import ProductionCoordination from './pages/cooperatives/ProductionCoordination'

// Sustainability & Environment
import SustainabilityDashboard from './pages/sustainability/SustainabilityDashboard.jsx'
import AdvancedSustainabilityDashboard from './pages/sustainability/AdvancedSustainabilityDashboard.jsx'
import CarbonFootprint from './pages/sustainability/CarbonFootprint.jsx'
import WaterUsage from './pages/sustainability/WaterUsage.jsx'
import SustainabilityScore from './pages/sustainability/SustainabilityScore.jsx'
import SustainabilityCertificates from './pages/sustainability/SustainabilityCertificates.jsx'
import NewCertificate from './pages/sustainability/NewCertificate.jsx'
import CertificateDetails from './pages/sustainability/CertificateDetails.jsx'
import SustainablePractices from './pages/sustainability/SustainablePractices.jsx'
import NewPractice from './pages/sustainability/NewPractice.jsx'
import PracticeDetails from './pages/sustainability/PracticeDetails.jsx'

// Education & Training
import FarmerTraining from './pages/education/FarmerTraining'
import LearningResources from './pages/education/LearningResources'
import VirtualWorkshops from './pages/education/VirtualWorkshops'
import AgriculturalLibrary from './pages/education/AgriculturalLibrary'

// Smart Devices Integration
import CropMonitoring from './pages/smart-devices/CropMonitoring'
import IrrigationAutomation from './pages/smart-devices/IrrigationAutomation'
import WeatherTracking from './pages/smart-devices/WeatherTracking'

// Marketing
import Campaigns from './pages/marketing/Campaigns.jsx'
import CampaignAnalytics from './pages/marketing/CampaignAnalytics.jsx'
import PPCAdvertisements from './pages/marketing/PPCAdvertisements.jsx'
import CreatePPCAdvertisement from './pages/marketing/CreatePPCAdvertisement.jsx'

// Affiliate Marketing
import AffiliateDashboard from './pages/affiliate/AffiliateDashboard.jsx'
import AffiliateApplication from './pages/affiliate/AffiliateApplication.jsx'
import AffiliateLinks from './pages/affiliate/AffiliateLinks.jsx'
import CreateAffiliateLink from './pages/affiliate/CreateAffiliateLink.jsx'

// CSS
import 'react-toastify/dist/ReactToastify.css'
import './style.css'

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />
  }

  return children
}

const AppContent = () => {
  const { i18n } = useTranslation()

  // Set document direction based on language
  useEffect(() => {
    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = i18n.language
  }, [i18n.language])

  return (
    <>
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<MainLayout />}>
          <Route index element={<Home />} />
          <Route path="products" element={<Products />} />
          <Route path="products/:id" element={<ProductDetails />} />
          <Route path="market-trends" element={<MarketTrends />} />
          <Route path="market/demand" element={<GlobalDemand />} />
          <Route path="market/deficit" element={<DeficitAnalysis />} />
          <Route path="market/product/:productType" element={<ProductForecast />} />
          <Route path="market/country/:countryName" element={<CountryAnalysis />} />
        </Route>

        {/* Auth Routes */}
        <Route path="/" element={<AuthLayout />}>
          <Route path="login" element={<Login />} />
          <Route path="register" element={<Register />} />
        </Route>

        {/* Dashboard Routes */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <DashboardLayout />
          </ProtectedRoute>
        }>
          <Route index element={<Dashboard />} />

          {/* Role-based Dashboards */}
          <Route path="farmer" element={<FarmerDashboard />} />
          <Route path="importer" element={<ImporterDashboard />} />
          <Route path="shipping" element={<ShippingDashboard />} />
          <Route path="moderator" element={<ModeratorDashboard />} />
          <Route path="admin" element={<AdminDashboard />} />
          <Route path="ai-dashboard" element={<AIDashboard />} />

          {/* Products */}
          <Route path="products" element={<DashboardProducts />} />
          <Route path="products/add" element={<AddProduct />} />
          <Route path="products/edit/:id" element={<EditProduct />} />
          <Route path="products/:id" element={<ProductDetails />} />

          {/* Contracts */}
          <Route path="contracts" element={<DashboardContracts />} />
          <Route path="contracts/add" element={<AddContract />} />
          <Route path="contracts/edit/:id" element={<EditContract />} />
          <Route path="contracts/:id" element={<ContractDetails />} />

          {/* Shipments */}
          <Route path="shipments" element={<DashboardShipments />} />
          <Route path="shipments/incoming" element={<IncomingShipments />} />
          <Route path="shipments/add" element={<AddShipment />} />
          <Route path="shipments/edit/:id" element={<EditShipment />} />
          <Route path="shipments/analytics" element={<ShipmentAnalytics />} />
          <Route path="shipments/:id" element={<ShipmentDetails />} />
          <Route path="shipments/:id/tracking" element={<ShipmentTracking />} />

          {/* Market */}
          <Route path="market" element={<MarketAnalysis />} />
          <Route path="market/deficit" element={<DeficitAnalysis />} />
          <Route path="market/product/:productType" element={<ProductForecast />} />
          <Route path="market/country/:countryName" element={<CountryAnalysis />} />
          <Route path="market/forecast/:productType" element={<ProductForecast />} />
          <Route path="market/price-forecast/:productType" element={<PriceForecast />} />
          <Route path="market/recommendation" element={<Recommendation />} />
          <Route path="market/advanced-analytics" element={<AdvancedMarketAnalytics />} />

          {/* Profile */}
          <Route path="profile" element={<Profile />} />
          <Route path="profile/edit" element={<EditProfile />} />

          {/* Settings */}
          <Route path="settings" element={<UserSettings />} />

          {/* Verification */}
          <Route path="verification/business" element={<BusinessVerificationPage />} />

          {/* Trade & Export/Import */}
          <Route path="trade" element={<TradeExportImport />} />
          <Route path="trade/export-seasons" element={<ExportSeasons />} />
          <Route path="trade/customs-documents" element={<CustomsDocuments />} />
          <Route path="trade/global-demand" element={<GlobalDemand />} />
          <Route path="market/demand" element={<GlobalDemand />} />

          {/* AI Routes */}
          <Route path="ai/price-prediction" element={<PricePrediction />} />
          <Route path="ai/demand-supply" element={<DemandSupplyAnalysis />} />
          <Route path="ai/climate-impact" element={<ClimateImpactAnalysis />} />
          <Route path="ai/missing-products" element={<MissingProducts />} />
          <Route path="ai/seasonal-analysis" element={<SeasonalAnalysis />} />
          <Route path="ai/smart-pricing" element={<SmartPricing />} />
          <Route path="ai/recommendations" element={<Recommendations />} />
          <Route path="ai/country-analysis" element={<CountryAnalysis />} />
          <Route path="ai/advanced-analytics" element={<AdvancedAnalytics />} />

          {/* Advertisements */}
          <Route path="advertisements" element={<Advertisements />} />
          <Route path="advertisements/create" element={<CreateAdvertisement />} />
          <Route path="advertisements/:id" element={<AdvertisementDetails />} />
          <Route path="advertisements/:id/edit" element={<EditAdvertisement />} />
          <Route path="advertisements/:id/analytics" element={<AdvertisementAnalytics />} />

          {/* Marketing */}
          <Route path="marketing/campaigns" element={<Campaigns />} />
          <Route path="marketing/campaigns/analytics" element={<CampaignAnalytics />} />
          <Route path="marketing/ppc" element={<PPCAdvertisements />} />
          <Route path="marketing/ppc/create" element={<CreatePPCAdvertisement />} />

          {/* Payments */}
          <Route path="payments" element={<PaymentDashboard />} />
          <Route path="payments/list" element={<Payments />} />
          <Route path="payments/methods" element={<PaymentMethods />} />
          <Route path="payments/invoices" element={<InvoiceManagement />} />
          <Route path="payments/:id" element={<PaymentDetails />} />
          <Route path="payments/:id/invoice" element={<PaymentInvoice />} />

          {/* Moderator Routes */}
          <Route path="moderator/advertisements" element={<ModeratorAdvertisements />} />
          <Route path="moderator/verification" element={<BusinessVerificationReview />} />

          {/* Admin Routes */}
          <Route path="admin/users" element={<UserManagement />} />
          <Route path="admin/roles" element={<RolesPermissions />} />
          <Route path="admin/settings" element={<SystemSettings />} />
          <Route path="admin/reports" element={<Reports />} />
          <Route path="admin/verification" element={<BusinessVerificationReview />} />

          {/* Role-based Dashboard Redirects */}
          <Route path="role/:role" element={
            <RoleBasedDashboardRedirect />
          } />

          {/* User Management Routes */}
          <Route path="users" element={<UserManagement />} />
          <Route path="users/permissions" element={<UserPermissions />} />
          <Route path="users/activation" element={<AccountActivation />} />

          {/* Reports & Analytics Routes */}
          <Route path="reports/sales" element={<SalesAnalytics />} />
          <Route path="reports/sales/details" element={<SalesReports />} />
          <Route path="reports/income-expense" element={<IncomeExpenseReports />} />
          <Route path="reports/supplier-customer" element={<SupplierCustomerReports />} />
          <Route path="reports/performance" element={<PerformanceReports />} />
          <Route path="reports/analytics" element={<ReportsAdvancedAnalytics />} />

          {/* Inventory Management Routes */}
          <Route path="inventory/tracking" element={<InventoryTracking />} />
          <Route path="inventory/warehouses" element={<WarehouseManagement />} />
          <Route path="inventory/alerts" element={<LowStockAlerts />} />

          {/* Messaging & Communications Routes */}
          <Route path="communications/messages" element={<InternalMessaging />} />
          <Route path="communications/notifications" element={<SystemNotifications />} />
          <Route path="communications/support" element={<SupportCenter />} />
          <Route path="communications/forums" element={<Forums />} />
          <Route path="communications/forums/:slug" element={<ForumDetail />} />

          {/* Ratings & Reviews Routes */}
          <Route path="ratings/products" element={<ProductRatings />} />
          <Route path="ratings/sellers" element={<SellerRatings />} />
          <Route path="ratings/management" element={<ReviewManagement />} />

          {/* Store Routes */}
          <Route path="stores" element={<StoreManagement />} />
          <Route path="stores/create" element={<CreateStore />} />
          <Route path="stores/:slug" element={<StoreDetails />} />
          <Route path="stores/:storeId/ratings" element={<StoreRatings />} />

          {/* Auctions Routes */}
          <Route path="auctions/live" element={<LiveAuctions />} />
          <Route path="auctions/agricultural" element={<AgriculturalAuctions />} />
          <Route path="auctions/bids" element={<BidTracking />} />

          {/* Agricultural Cooperatives Routes */}
          <Route path="cooperatives/management" element={<CooperativeManagement />} />
          <Route path="cooperatives/resources" element={<ResourceSharing />} />
          <Route path="cooperatives/production" element={<ProductionCoordination />} />

          {/* Sustainability & Environment Routes */}
          <Route path="sustainability" element={<SustainabilityDashboard />} />
          <Route path="sustainability/advanced" element={<AdvancedSustainabilityDashboard />} />
          <Route path="sustainability/carbon-footprint" element={<CarbonFootprint />} />
          <Route path="sustainability/water-usage" element={<WaterUsage />} />
          <Route path="sustainability/score" element={<SustainabilityScore />} />
          <Route path="sustainability/certificates" element={<SustainabilityCertificates />} />
          <Route path="sustainability/certificates/new" element={<NewCertificate />} />
          <Route path="sustainability/certificates/:id" element={<CertificateDetails />} />
          <Route path="sustainability/practices" element={<SustainablePractices />} />
          <Route path="sustainability/practices/new" element={<NewPractice />} />
          <Route path="sustainability/practices/:id" element={<PracticeDetails />} />

          {/* Education & Training Routes */}
          <Route path="education/training" element={<FarmerTraining />} />
          <Route path="education/resources" element={<LearningResources />} />
          <Route path="education/workshops" element={<VirtualWorkshops />} />
          <Route path="education/library" element={<AgriculturalLibrary />} />

          {/* Smart Devices Integration Routes */}
          <Route path="smart-devices/crop-monitoring" element={<CropMonitoring />} />
          <Route path="smart-devices/irrigation" element={<IrrigationAutomation />} />
          <Route path="smart-devices/weather" element={<WeatherTracking />} />

          {/* Affiliate Marketing Routes */}
          <Route path="affiliate" element={<AffiliateDashboard />} />
          <Route path="affiliate/apply" element={<AffiliateApplication />} />
          <Route path="affiliate/links" element={<AffiliateLinks />} />
          <Route path="affiliate/links/create" element={<CreateAffiliateLink />} />
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={i18n.language === 'ar'}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </>
  )
}

const App = () => {
  return <AppContent />
}

export default App
