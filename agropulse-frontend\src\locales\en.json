{"app": {"name": "AgroPulse", "tagline": "Agricultural Trading Platform"}, "nav": {"home": "Home", "products": "Products", "contracts": "Contracts", "shipments": "Shipments", "market": "Market Analysis", "profile": "Profile", "dashboard": "Dashboard", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Full Name", "role": "Role", "farmer": "<PERSON>", "importer": "Importer", "shipping": "Shipping Company", "moderator": "Moderator", "admin": "Administrator", "companyName": "Company Name", "country": "Country", "address": "Address", "phoneNumber": "Phone Number", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In"}, "home": {"hero": {"title": "Connect, Trade, Grow", "subtitle": "The smart platform for agricultural trading", "cta": "Get Started"}, "features": {"title": "Why Choose AgroPulse?", "directTrade": {"title": "Direct Trading", "description": "Connect directly with farmers and importers without intermediaries"}, "smartContracts": {"title": "Smart Contracts", "description": "Secure and transparent contracts with built-in verification"}, "marketInsights": {"title": "Market Insights", "description": "AI-powered market analysis and price forecasting"}, "integratedShipping": {"title": "Integrated Shipping", "description": "Seamless shipping management and tracking"}}, "howItWorks": {"title": "How It Works", "step1": {"title": "Create an Account", "description": "Sign up as a farmer, importer, or shipping company"}, "step2": {"title": "List or Browse Products", "description": "Add your agricultural products or browse available listings"}, "step3": {"title": "Negotiate and Contract", "description": "Negotiate terms and create secure digital contracts"}, "step4": {"title": "Ship and Track", "description": "Arrange shipping and track your products in real-time"}}, "testimonials": {"title": "What Our Users Say", "farmer": {"quote": "AgroPulse has helped me reach international markets I never had access to before.", "name": "<PERSON>", "role": "Wheat Farmer, Egypt"}, "importer": {"quote": "The market analysis tools have been invaluable for making informed purchasing decisions.", "name": "<PERSON>", "role": "Agricultural Importer, UAE"}, "shipping": {"quote": "The platform streamlines our operations and helps us connect with new clients.", "name": "<PERSON>", "role": "Shipping Manager, Saudi Arabia"}}, "search": {"placeholder": "Search for agricultural products...", "button": "Search"}, "demandedProducts": {"title": "Market Trends"}, "countryDeficit": {"title": "Country Deficit Analysis", "deficit": "deficit", "tons": "tons", "opportunity": "Trade Opportunity"}, "aiRecommendations": {"title": "AI Recommendations", "confidence": "confidence", "product": "Product", "targetMarkets": "Target Markets", "viewDetails": "View Details"}, "ai": {"recommendation1": {"title": "High Demand Opportunity", "description": "Strong export potential identified for wheat in North African markets"}, "recommendation2": {"title": "Price Growth Expected", "description": "Rice prices showing upward trend in Middle Eastern markets"}, "recommendation3": {"title": "Seasonal Advantage", "description": "Optimal timing for fruit exports to Eastern European markets"}}, "upcomingSeasons": {"title": "Upcoming Export Seasons", "product": "Product", "country": "Country", "period": "Period", "status": "Status", "upcoming": "Upcoming", "active": "Active"}, "partnerships": {"title": "Partnership Opportunities", "opportunity1": {"title": "Agricultural Investment Fund", "description": "Join our investment fund focused on sustainable agriculture projects"}, "opportunity2": {"title": "Regional Distribution Network", "description": "Partner with established distributors to expand your market reach"}, "opportunity3": {"title": "Technology Partnership", "description": "Collaborate on innovative agricultural technology solutions"}}, "globalMap": {"title": "Global Market Map", "mapPlaceholder": "Interactive Global Market Map", "clickToInteract": "Click on regions to explore market opportunities", "deficitRegions": "Deficit Regions", "highDeficit": "High Deficit", "mediumDeficit": "Medium Deficit", "lowDeficit": "Low Deficit", "mainProducts": "Main Products"}}, "products": {"title": "Agricultural Products", "myProducts": "My Products", "add": "Add Product", "edit": "Edit Product", "delete": "Delete Product", "name": "Product Name", "description": "Description", "category": "Category", "origin": "Origin", "price": "Price", "unit": "Unit", "quantity": "Available Quantity", "season": "Season", "image": "Product Image", "seller": "<PERSON><PERSON>", "actions": "Actions", "details": "View Details", "contact": "<PERSON>ller", "createContract": "Create Contract", "unknownProduct": "Unknown Product", "filters": {"title": "Filters", "category": "Category", "origin": "Origin", "priceRange": "Price Range", "apply": "Apply Filters", "reset": "Reset"}, "categories": {"grains": "Grains", "fruits": "Fruits", "vegetables": "Vegetables", "nuts": "Nuts", "spices": "Spices", "coffee": "Coffee & Tea", "other": "Other"}}, "contracts": {"title": "Contracts", "allContracts": "All Contracts", "create": "Create Contract", "view": "View Contract", "edit": "Edit Contract", "delete": "Delete Contract", "product": "Product", "quantity": "Quantity", "totalPrice": "Total Price", "seller": "<PERSON><PERSON>", "buyer": "Buyer", "deliveryDate": "Delivery Date", "status": "Status", "terms": "Terms & Conditions", "sign": "Sign Contract", "signed": "Contract Signed", "pending": "Pending", "active": "Active", "completed": "Completed", "cancelled": "Cancelled", "buyerContracts": "Buyer Contracts", "sellerContracts": "Seller Contracts", "noContracts": "No contracts found", "noContractsDescription": "You don't have any contracts yet. Create a new contract to get started.", "contractNumber": "Contract Number", "createdAt": "Created At", "productInformation": "Product Information", "unitPrice": "Unit Price", "partiesInformation": "Parties Information", "noTerms": "No terms specified", "signatures": "Signatures", "sellerSignature": "Seller Signature", "buyerSignature": "Buyer Signature", "notSigned": "Not Signed", "relatedShipments": "Related Shipments", "deleteConfirmTitle": "Delete Contract", "deleteConfirmMessage": "Are you sure you want to delete this contract? This action cannot be undone.", "deleteSuccess": "Contract deleted successfully", "deleteError": "Error deleting contract", "signSuccess": "Contract signed successfully", "signError": "Error signing contract", "fetchDetailError": "Error fetching contract details", "notFound": "Contract not found", "signedAt": "Signed on {{date}}"}, "shipments": {"title": "Shipments", "create": "Create Shipment", "view": "View Shipment", "edit": "Edit Shipment", "delete": "Delete Shipment", "contract": "Contract", "origin": "Origin", "destination": "Destination", "carrier": "Carrier", "departureDate": "Departure Date", "estimatedArrival": "Estimated Arrival", "actualArrival": "Actual Arrival", "status": "Status", "trackingNumber": "Tracking Number", "containerType": "Container Type", "containerSize": "Container <PERSON><PERSON>", "notes": "Notes", "track": "Track Shipment", "updateStatus": "Update Status", "incomingShipments": "Incoming Shipments", "incomingShipmentsDescription": "Track all shipments related to your contracts", "shipmentNumber": "Shipment #{{number}}", "unknownContract": "Unknown Contract", "route": "Route", "viewDetails": "View Details", "trackShipment": "Track Shipment", "noShipmentsFound": "No Shipments Found", "noShipmentsDescription": "There are no shipments matching your criteria", "errorFetchingShipments": "Error fetching shipments. Please try again.", "allStatuses": "All Statuses", "allContracts": "All Contracts", "searchShipments": "Search shipments...", "overdue": "Overdue", "today": "Today", "tomorrow": "Tomorrow", "inXDays": "In {{days}} days", "notAvailable": "Not Available", "processing": "Processing", "inTransit": "In Transit", "delivered": "Delivered", "delayed": "Delayed", "cancelled": "Cancelled", "statuses": {"pending": "Pending", "processing": "Processing", "in_transit": "In Transit", "delivered": "Delivered", "delayed": "Delayed", "cancelled": "Cancelled"}}, "market": {"title": "Market Analysis", "trends": "Market Trends", "forecast": "Price Forecast", "product": "Product", "country": "Country", "currentPrice": "Current Price", "forecastPrice": "Forecast Price", "priceChange": "Price Change", "demandIndex": "Demand Index", "demandChange": "Demand Change", "climateImpact": "Climate Impact", "climateDetails": "Climate Details", "insights": "AI Insights", "recommendation": "Trading Recommendation", "getRecommendation": "Get Recommendation", "trendingProducts": "Trending Products", "highDemand": "High Demand Products", "climateAffected": "Climate Affected Products", "timeframe": {"label": "Timeframe", "short": "Short-term (1-3 months)", "medium": "Medium-term (3-6 months)", "long": "Long-term (6+ months)"}, "actions": {"buy": "Buy", "sell": "<PERSON>ll", "hold": "Hold", "wait": "Wait"}}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "companyInfo": "Company Information", "statistics": "Statistics", "edit": "Edit Profile", "save": "Save Changes", "verified": "Verified", "notVerified": "Not Verified", "rating": "Rating", "completedDeals": "Completed Deals", "pendingBalance": "Pending Balance", "availableBalance": "Available Balance", "products": "Products", "contracts": "Contracts", "shipments": "Shipments"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "summary": "Summary", "recentActivity": "Recent Activity", "marketInsights": "Market Insights", "activeContracts": "Active Contracts", "pendingShipments": "Pending Shipments", "productPerformance": "Product Performance", "revenue": "Revenue", "expenses": "Expenses", "profit": "Profit", "completedDeals": "Completed Deals", "totalRevenue": "Total Revenue", "pendingDeals": "Pending Deals", "rating": "Rating", "salesOverTime": "Sales Over Time", "productDemand": "Product Demand", "aiInsights": "AI Insights", "demandByCountry": "Demand By Country", "upcomingEvents": "Upcoming Events", "event": "Event", "date": "Date", "status": "Status", "contractDue": "Contract Due", "shipmentArrival": "Shipment Arrival", "paymentDue": "Payment Due", "pending": "Pending", "onTrack": "On Track", "upcoming": "Upcoming", "affiliateMarketing": "Affiliate Marketing", "affiliateMarketingDesc": "Earn commissions by promoting agricultural products"}, "ai": {"title": "AI Dashboard", "poweredInsights": "AI-Powered Market Insights", "insightsDescription": "Get real-time market insights and recommendations powered by advanced AI algorithms", "getRecommendation": "Get Recommendation", "priceForecast": "Price Forecast", "historicalPrice": "Historical Price", "forecastPrice": "Forecast Price", "priceInsight": "Price Trend Insight", "priceInsightDescription": "Based on current market conditions and historical data, prices are expected to increase by 15-20% over the next 6 months due to growing global demand and limited supply.", "demandInsight": "Demand Trend Insight", "demandInsightDescription": "Asian markets show the highest growth potential for agricultural exports, with a projected 15% increase in demand over the next year.", "activeExportSeasons": "Active Export Seasons", "recommendations": "AI Recommendations", "buyRecommendation": "Buy: Wheat", "buyRecommendationDesc": "Expected price increase of 9.25% over the next 6 months due to increased global demand and favorable weather conditions.", "sellRecommendation": "Sell: Soybeans", "sellRecommendationDesc": "Limited price growth expected (4.80%) with potential downside risks due to increased production in South America.", "waitRecommendation": "Hold: <PERSON>", "waitRecommendationDesc": "Moderate price increase expected (5.91%), but market volatility suggests waiting for a better entry point.", "getPersonalizedRecommendation": "Get personalized recommendations", "fetchError": "Failed to fetch AI dashboard data"}, "aiDashboard": {"historical": "Historical", "forecast": "Forecast", "currentDemand": "Current Demand", "forecastDemand": "Forecast Demand", "droughtRisk": "Drought Risk", "temperatureChange": "Temperature Change", "rainfallChange": "Rainfall Change", "cropYieldImpact": "Crop Yield Impact", "disasterRisk": "Disaster Risk", "waterScarcity": "Water Scarcity", "fetchError": "Failed to fetch AI analysis data"}, "admin": {"dashboard": "Admin Dashboard", "totalUsers": "Total Users", "totalProducts": "Total Products", "totalContracts": "Total Contracts", "totalShipments": "Total Shipments", "revenue": "Total Revenue", "activeUsers": "Active Users", "userGrowth": "User Growth", "contractsValue": "Contracts Value", "productCategories": "Product Categories", "monthlyRevenue": "Monthly Revenue", "usersByRole": "Users by Role", "quickActions": "Quick Actions", "addUser": "Add User", "addUserDesc": "Create a new user account with specific role and permissions", "manageModeratorAccess": "Manage Moderator Access", "manageModeratorAccessDesc": "Control access levels and permissions for platform moderators", "platformSettings": "Platform Settings", "platformSettingsDesc": "Configure global platform settings and preferences", "generateReports": "Generate Reports", "generateReportsDesc": "Create detailed reports on platform activity and performance"}, "trade": {"title": "Trade & Export/Import", "activeExports": "Active Exports", "pendingDocuments": "Pending Documents", "highDemandCountries": "High Demand Countries", "totalShipments": "Total Shipments", "fetchError": "Failed to fetch trade data", "demandIndex": "Demand Index", "exportSeasons": {"title": "Export Seasons", "calendar": "Export Seasons Calendar", "calendarDescription": "View export seasons for agricultural products by country and month", "allSeasons": "All Export Seasons", "startDate": "Start Date", "endDate": "End Date", "duration": "Duration", "status": "Status", "active": "Active", "inactive": "Inactive", "searchProduct": "Search product...", "noData": "No export seasons found", "noResults": "No results match your filters", "fetchError": "Failed to fetch export seasons"}, "customsDocuments": {"title": "Customs Documents", "description": "Manage your customs documents for international trade", "yourDocuments": "Your Documents", "uploadNew": "Upload New Document", "upload": "Upload Document", "documentType": "Document Type", "file": "Document File", "fileTypes": "Accepted file types: PDF, JPG, PNG (max 10MB)", "formError": "Please fill all required fields", "uploadSuccess": "Document uploaded successfully", "uploadError": "Failed to upload document", "noDocuments": "You haven't uploaded any documents yet", "fetchError": "Failed to fetch customs documents", "requirements": {"origin": "A document that certifies the country of origin of the goods. Required by most importing countries for customs clearance.", "phytosanitary": "A certificate issued by the exporting country's plant protection organization, confirming that plants or plant products meet the importing country's health requirements.", "invoice": "A document that includes details about the sale, including product description, quantity, and price.", "billOfLading": "A transport document issued by the carrier to the shipper, serving as a receipt for the goods and evidence of the contract of carriage."}, "types": {"origin": "Certificate of Origin", "phytosanitary": "Phytosanitary Certificate", "invoice": "Commercial Invoice", "packingList": "Packing List", "billOfLading": "Bill of Lading", "importLicense": "Import License", "exportLicense": "Export License"}}, "globalDemand": {"title": "Global Demand", "description": "Explore global demand for agricultural products by country and region", "overview": "Global Demand Overview", "demandFor": "Global Demand for", "allProducts": "All Products", "topCountry": "Top Demand Country", "averageDemand": "Average Demand", "highDemandCountries": "High Demand Countries", "withDemandOver": "with demand over", "topCountries": "Top Countries by Demand", "demandByRegion": "Demand by Region", "allCountries": "All Countries", "trend": "Trend", "marketSize": "Market Size (USD)", "regionDemand": "Regional Demand", "noData": "No global demand data found", "fetchError": "Failed to fetch global demand data", "aiInsight": "AI Market Insight", "aiInsightGeneral": "Based on current trends, Asian markets show the highest growth potential for agricultural exports, with a projected 15% increase in demand over the next year.", "aiInsightProduct": "Based on current trends, {{product}} is experiencing strong demand growth in Asian and European markets, with prices expected to rise by 8-12% in the next 6 months."}, "documentStatus": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected"}, "tips": {"title": "Trade Tips", "seasonality": "Check Export Seasons", "seasonalityDesc": "Always verify export seasons before planning shipments to ensure compliance with regulations.", "documentation": "Prepare Documents Early", "documentationDesc": "Start preparing customs documents at least 2-3 weeks before planned shipment date.", "marketResearch": "Research Target Markets", "marketResearchDesc": "Use the Global Demand tool to identify high-demand markets for your products."}}, "advertisements": {"title": "Advertisements", "create": "Create Advertisement", "edit": "Edit Advertisement", "delete": "Delete Advertisement", "featured": "Featured Advertisements", "personalized": "Personalized Recommendations", "relatedProducts": "Related Products", "viewProducts": "View Products", "viewMore": "View More", "fetchError": "Error fetching advertisements", "noAdvertisements": "No advertisements found", "noAdvertisementsDesc": "You haven't created any advertisements yet. Create your first advertisement to promote your products.", "createSuccess": "Advertisement created successfully", "updateSuccess": "Advertisement updated successfully", "deleteSuccess": "Advertisement deleted successfully", "createError": "Error creating advertisement", "updateError": "Error updating advertisement", "deleteError": "Error deleting advertisement", "confirmDelete": "Are you sure you want to delete this advertisement?", "targeting": {"title": "Advertisement Targeting", "countries": "Target Countries", "products": "Target Products", "userRoles": "Target User Roles", "demographics": "Target Demographics", "companySize": "Company Size", "businessType": "Business Type", "interests": "Interests", "behavior": "User Behavior"}, "analytics": {"title": "Advertisement Analytics", "impressions": "Impressions", "clicks": "<PERSON>licks", "ctr": "Click-Through Rate", "conversions": "Conversions", "conversionRate": "Conversion Rate", "performance": "Performance", "demographics": "Demographics", "regions": "Regions", "timeframe": "Timeframe", "last7days": "Last 7 Days", "last30days": "Last 30 Days", "last90days": "Last 90 Days", "custom": "Custom Range", "compare": "Compare", "previousPeriod": "Previous Period", "sameLastYear": "Same Period Last Year", "exportData": "Export Data", "recommendations": {"title": "Recommendations", "targeting": "Targeting Recommendation", "targetingDesc": "Consider expanding your target audience to include more countries in North Africa based on recent market trends.", "budget": "Budget Recommendation", "budgetDesc": "Increasing your daily budget by 15-20% could significantly improve your ad visibility during peak hours.", "creative": "Creative Recommendation", "creativeDesc": "Try using more product-focused imagery in your advertisements to improve engagement rates."}}}, "marketing": {"campaigns": {"title": "Marketing Campaigns", "createCampaign": "Create Campaign", "noCampaigns": "No Campaigns Found", "noCampaignsDesc": "You haven't created any marketing campaigns yet. Create your first campaign to start promoting your products.", "analytics": "Campaign Analytics", "performance": "Campaign Performance", "roi": "Return on Investment", "conversionRate": "Conversion Rate", "engagementRate": "Engagement Rate", "audienceReach": "Audience Reach", "campaignType": "Campaign Type", "targetAudience": "Target Audience", "channels": "Marketing Channels", "goals": "Campaign Goals", "budget": "Campaign Budget", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "active": "Active", "completed": "Completed", "scheduled": "Scheduled", "draft": "Draft", "paused": "Paused", "createSuccess": "Campaign created successfully", "createError": "Error creating campaign", "updateSuccess": "Campaign updated successfully", "updateError": "Error updating campaign", "deleteSuccess": "Campaign deleted successfully", "deleteError": "Error deleting campaign", "fetchError": "Error fetching campaigns"}, "ppc": {"title": "PPC Advertisements", "createAd": "Create PPC Advertisement", "noAds": "No PPC Advertisements Found", "noAdsDesc": "You haven't created any PPC advertisements yet. Create your first PPC ad to start promoting your products.", "adContent": "Ad Content", "adTitle": "Ad Title", "adTitlePlaceholder": "Enter a compelling title for your ad", "adDescription": "Ad Description", "adDescriptionPlaceholder": "Describe your product or service in a way that attracts clicks", "adImage": "Ad Image", "uploadImage": "Upload Image", "landingUrl": "Landing URL", "adPreview": "Ad Preview", "targeting": "Targeting", "targetCountries": "Target Countries", "targetCountriesDesc": "Select the countries where you want your ad to be shown", "targetProducts": "Target Products", "targetProductsDesc": "Select the products related to your advertisement", "noProducts": "No products found", "budgetAndSchedule": "Budget & Schedule", "startDate": "Start Date", "endDate": "End Date", "totalBudget": "Total Budget", "dailyBudget": "Daily Budget", "bidAmount": "<PERSON><PERSON>", "bidAmountDesc": "The maximum amount you're willing to pay per click", "keywords": "Keywords", "keywordsPlaceholder": "Enter keywords separated by commas", "keywordsDesc": "Enter relevant keywords that will trigger your ad", "impressions": "Impressions", "clicks": "<PERSON>licks", "ctr": "CTR", "averageCpc": "Avg. CPC", "spent": "Spent", "remaining": "Remaining", "performance": "Performance", "stepAdContent": "Ad Content", "stepTargeting": "Targeting", "stepBudget": "Budget & Schedule", "statusDraft": "Draft", "statusActive": "Active", "statusPaused": "Paused", "statusRejected": "Rejected", "statusExpired": "Expired", "createSuccess": "PPC advertisement created successfully", "createError": "Error creating PPC advertisement", "updateSuccess": "PPC advertisement updated successfully", "updateError": "Error updating PPC advertisement", "deleteSuccess": "PPC advertisement deleted successfully", "deleteError": "Error deleting PPC advertisement", "fetchError": "Error fetching PPC advertisements", "fetchProductsError": "Error fetching products", "titleRequired": "Ad title is required", "descriptionRequired": "Ad description is required", "imageRequired": "Ad image is required", "landingUrlRequired": "Landing URL is required", "invalidUrl": "Please enter a valid URL", "countriesRequired": "Please select at least one country", "productsRequired": "Please select at least one product", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "endDateAfterStart": "End date must be after start date", "budgetRequired": "Total budget is required", "invalidBudget": "Please enter a valid budget amount", "bidAmountRequired": "Bid amount is required", "invalidBidAmount": "Please enter a valid bid amount", "dailyBudgetRequired": "Daily budget is required", "invalidDailyBudget": "Please enter a valid daily budget amount", "dailyBudgetExceedsTotalBudget": "Daily budget cannot exceed total budget", "keywordsRequired": "Please enter at least one keyword"}}, "stores": {"management": {"title": "My Stores", "createStore": "Create Store", "createFirstStore": "Create Your First Store", "noStores": "No Stores Found", "noStoresDesc": "You haven't created any stores yet. Create your first store to start selling your products.", "view": "View Store", "edit": "Edit Store", "delete": "Delete Store", "deleteConfirmTitle": "Delete Store", "deleteConfirmMessage": "Are you sure you want to delete the store '{{storeName}}'?", "deleteWarning": "This action cannot be undone. All products associated with this store will be removed.", "confirmDelete": "Yes, Delete Store", "deleteSuccess": "Store deleted successfully", "deleteError": "Error deleting store", "fetchError": "Error fetching stores", "products": "Products", "rating": "Rating", "sales": "Sales", "revenue": "Revenue", "manageProducts": "Manage Products", "viewAnalytics": "View Analytics", "viewRatings": "View Ratings", "statusActive": "Active", "statusPending": "Pending", "statusSuspended": "Suspended", "verified": "Verified"}, "create": {"title": "Create Store", "success": "Store created successfully", "error": "Error creating store", "storeInfo": "Store Information", "name": "Store Name", "description": "Store Description", "metaTitle": "Meta Title", "metaDescription": "Meta Description", "metaKeywords": "Meta Keywords", "keywordsPlaceholder": "keyword1, keyword2, keyword3", "storeMedia": "Store Media", "logo": "Store Logo", "uploadLogo": "Upload Logo", "banner": "Store Banner", "uploadBanner": "Upload Banner", "contactInfo": "Contact Information", "email": "Email Address", "phone": "Phone Number", "address": "Address", "city": "City", "country": "Country", "postalCode": "Postal Code", "socialMedia": "Social Media", "facebook": "Facebook", "twitter": "Twitter", "instagram": "Instagram", "linkedin": "LinkedIn", "submit": "Create Store"}, "details": {"verified": "Verified", "contactInfo": "Contact Information", "socialMedia": "Social Media", "storeStats": "Store Statistics", "totalProducts": "Total Products", "totalSales": "Total Sales", "memberSince": "Member Since", "rateStore": "Rate Store", "about": "About the Store", "products": "Products", "ratings": "Ratings", "noProducts": "No Products Found", "noProductsDesc": "This store hasn't added any products yet.", "noRatings": "No Ratings Found", "noRatingsDesc": "This store hasn't received any ratings yet.", "yourRating": "Your Rating", "ratingTitle": "Title", "ratingTitlePlaceholder": "Summarize your experience with this store", "ratingComment": "Comment", "ratingCommentPlaceholder": "Share your experience with this store", "submitRating": "Submit Rating", "ratingSuccess": "Rating submitted successfully", "ratingError": "Error submitting rating", "fetchError": "Error fetching store details", "notFound": "Store not found"}, "ratings": {"title": "Ratings for {{storeName}}", "summary": "Ratings Summary", "totalRatings": "ratings", "distribution": "Rating Distribution", "customerReviews": "Customer Reviews", "by": "by", "anonymousUser": "Anonymous User", "helpful": "Helpful", "report": "Report", "reported": "This review has been reported", "noRatings": "No Ratings Found", "noRatingsDesc": "This store hasn't received any ratings yet.", "filterAll": "All Ratings", "filterPositive": "Positive (4-5)", "filterNeutral": "Neutral (2-3)", "filterNegative": "Negative (1)", "sortDate": "Sort by Date", "sortRating": "Sort by Rating", "sortHelpful": "Sort by Helpful", "noTitle": "No Title", "reportRating": "Report Rating", "reportReason": "Reason for Reporting", "reportReasonPlaceholder": "Please explain why you're reporting this rating", "submitReport": "Submit Report", "reportSuccess": "Rating reported successfully", "reportError": "Error reporting rating", "markedHelpful": "Rating marked as helpful", "markHelpfulError": "Error marking rating as helpful", "fetchError": "Error fetching ratings", "storeNotFound": "Store not found"}}, "security": {"twoFactorAuthentication": "Two-Factor Authentication", "twoFactorRecommended": "Two-Factor Authentication Recommended", "twoFactorDescription": "Add an extra layer of security to your account by enabling two-factor authentication. This will require a verification code from your mobile device when signing in.", "enable2FA": "Enable Two-Factor Authentication", "disable2FA": "Disable Two-Factor Authentication", "confirmPassword": "Confirm Password", "enterPassword": "Enter your password", "scanQRCode": "Scan QR Code", "scanQRCodeDescription": "Scan this QR code with your authenticator app (like Google Authenticator, Authy, or Microsoft Authenticator).", "manualEntry": "Manual Entry Code", "copyToClipboard": "Copy to clipboard", "verificationCode": "Verification Code", "verifyAndEnable": "Verify and Enable", "twoFactorEnabled": "Two-Factor Authentication Enabled", "twoFactorEnabledDescription": "Your account is now protected with two-factor authentication. You will need to enter a verification code when signing in.", "saveRecoveryCodes": "Save your recovery codes in a safe place. You can use these codes to access your account if you lose your device.", "recoveryCodes": "Recovery Codes", "recoveryCodesWarning": "Keep these codes in a safe place. Each code can only be used once.", "finishSetup": "Finish Setup", "errorEnabling2FA": "Error enabling two-factor authentication", "errorDisabling2FA": "Error disabling two-factor authentication", "invalidCode": "Invalid verification code", "verificationCodeRequired": "Verification code is required", "enterVerificationCode": "Enter the verification code from your authenticator app", "enterRecoveryCode": "Enter a recovery code from your backup codes", "useVerificationCode": "Use verification code instead", "useRecoveryCode": "Use recovery code instead", "verify": "Verify", "verificationSuccess": "Verification successful", "recoveryCode": "Recovery Code", "securityLogs": "Security Logs", "refresh": "Refresh", "noLogsFound": "No security logs found", "errorFetchingLogs": "Error fetching security logs", "event": "Event", "status": "Status", "ipAddress": "IP Address", "date": "Date", "success": "Success", "failed": "Failed", "showing": "Showing page", "of": "of", "pages": "pages", "previous": "Previous", "next": "Next", "eventLogin": "<PERSON><PERSON>", "eventLogout": "Logout", "eventLoginAttempt": "Login Attempt", "eventPasswordReset": "Password Reset", "event2FAEnabled": "Two-Factor Authentication Enabled", "event2FADisabled": "Two-Factor Authentication Disabled", "event2FAConfirmed": "Two-Factor Authentication Confirmed", "event2FAVerifyAttempt": "Two-Factor Verification Attempt", "event2FARecoveryCodeUsed": "Recovery Code Used"}, "common": {"search": "Search", "filter": "Filter", "sort": "Sort", "create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "back": "Back", "next": "Next", "submit": "Submit", "submitting": "Submitting...", "loading": "Loading...", "noResults": "No results found", "error": "An error occurred", "success": "Success!", "warning": "Warning", "info": "Information", "yes": "Yes", "no": "No", "more": "More", "less": "Less", "all": "All", "none": "None", "date": "Date", "time": "Time", "amount": "Amount", "total": "Total", "status": "Status", "actions": "Actions", "details": "Details", "view": "View", "close": "Close", "viewAll": "View All", "manage": "Manage", "explore": "Explore", "upload": "Upload", "uploading": "Uploading...", "download": "Download", "select": "Select...", "description": "Description", "country": "Country", "allCountries": "All Countries", "region": "Region", "uploadDate": "Upload Date", "days": "days", "welcome": "Welcome", "unknown": "Unknown", "dashboard": "Dashboard", "backToList": "Back to List", "backToStores": "Back to Stores", "viewDetails": "View Details", "about": "About", "contact": "Contact", "terms": "Terms of Service", "allRightsReserved": "All rights reserved"}}