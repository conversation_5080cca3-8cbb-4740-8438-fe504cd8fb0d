{"name": "@emotion/is-prop-valid", "version": "1.3.1", "description": "A function to check whether a prop is valid for HTML and SVG elements", "main": "dist/emotion-is-prop-valid.cjs.js", "module": "dist/emotion-is-prop-valid.esm.js", "types": "dist/emotion-is-prop-valid.cjs.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/is-prop-valid", "scripts": {"test:typescript": "dtslint types"}, "publishConfig": {"access": "public"}, "dependencies": {"@emotion/memoize": "^0.9.0"}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^5.4.5"}, "files": ["src", "dist"], "exports": {".": {"types": {"import": "./dist/emotion-is-prop-valid.cjs.mjs", "default": "./dist/emotion-is-prop-valid.cjs.js"}, "module": "./dist/emotion-is-prop-valid.esm.js", "import": "./dist/emotion-is-prop-valid.cjs.mjs", "default": "./dist/emotion-is-prop-valid.cjs.js"}, "./package.json": "./package.json"}}