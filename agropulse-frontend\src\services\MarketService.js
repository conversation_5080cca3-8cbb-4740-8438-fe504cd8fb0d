import api, { aiApi } from '../utils/api'
import MockMarketService from './MockMarketService'

const API_URL = '/api'
// Use environment variable to determine if we should use mock data
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'

class MarketService {
  // Get all market analyses
  async getMarketAnalyses(filters = {}) {
    const params = new URLSearchParams()

    // Add filters to params
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params.append(key, filters[key])
      }
    })

    try {
      const response = await api.get(`${API_URL}/market-analysis`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching market analyses:', error)
      // Fallback to mock data if API fails
      return MockMarketService.getMarketAnalyses(filters)
    }
  }

  // Get market analysis for a specific product type
  async getProductAnalysis(productType) {
    if (USE_MOCK_DATA) {
      return MockMarketService.getProductAnalysis(productType)
    }

    try {
      const response = await api.get(`${API_URL}/market-analysis/${productType}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching product analysis for ${productType}:`, error)
      // Fallback to mock data if API fails
      return MockMarketService.getProductAnalysis(productType)
    }
  }

  // Get market analysis data for AI components
  async getMarketAnalysis(productType) {
    if (USE_MOCK_DATA) {
      return this.getMockMarketAnalysis(productType)
    }

    try {
      console.log(`Attempting to fetch market analysis for ${productType}...`)

      // Use AI API for market analysis
      const response = await aiApi.get(`/market-analysis/${productType}`)
      console.log(`Successfully fetched market analysis for ${productType}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching market analysis for ${productType}:`, error)
      console.log('Falling back to mock data for market analysis')

      // Fallback to mock data if API fails
      return this.getMockMarketAnalysis(productType)
    }
  }

  // Mock data for market analysis
  getMockMarketAnalysis(productType) {
    // Base data for different products
    const productData = {
      'Wheat': {
        current_price: 450.75,
        previous_price: 420.50,
        price_change_percentage: 7.2,
        demand_index: 78,
        production_regions: ['North America', 'Europe', 'Asia', 'Australia'],
        top_exporters: ['United States', 'Russia', 'Canada', 'France', 'Ukraine'],
        top_importers: ['Egypt', 'Indonesia', 'Algeria', 'Italy', 'Japan'],
        seasonality: [1.05, 1.02, 0.98, 0.95, 0.97, 1.0, 1.03, 1.05, 1.02, 0.99, 0.98, 1.01]
      },
      'Rice': {
        current_price: 380.50,
        previous_price: 362.30,
        price_change_percentage: 5.0,
        demand_index: 85,
        production_regions: ['Asia', 'South America', 'North America'],
        top_exporters: ['India', 'Thailand', 'Vietnam', 'Pakistan', 'United States'],
        top_importers: ['China', 'Saudi Arabia', 'Iran', 'Philippines', 'Nigeria'],
        seasonality: [1.02, 1.01, 0.99, 0.98, 0.97, 0.99, 1.01, 1.03, 1.04, 1.02, 1.0, 0.99]
      },
      'Corn': {
        current_price: 220.25,
        previous_price: 205.80,
        price_change_percentage: 7.0,
        demand_index: 72,
        production_regions: ['North America', 'South America', 'Asia', 'Europe'],
        top_exporters: ['United States', 'Brazil', 'Argentina', 'Ukraine', 'Russia'],
        top_importers: ['Japan', 'Mexico', 'South Korea', 'Egypt', 'Vietnam'],
        seasonality: [1.01, 0.99, 0.97, 0.96, 0.98, 1.0, 1.02, 1.04, 1.03, 1.01, 0.99, 0.98]
      },
      'Soybeans': {
        current_price: 520.75,
        previous_price: 536.50,
        price_change_percentage: -2.9,
        demand_index: 58,
        production_regions: ['North America', 'South America', 'Asia'],
        top_exporters: ['Brazil', 'United States', 'Argentina', 'Paraguay', 'Canada'],
        top_importers: ['China', 'European Union', 'Mexico', 'Japan', 'Thailand'],
        seasonality: [1.03, 1.01, 0.99, 0.97, 0.96, 0.98, 1.0, 1.02, 1.04, 1.03, 1.01, 0.99]
      },
      'Coffee': {
        current_price: 180.30,
        previous_price: 165.40,
        price_change_percentage: 9.0,
        demand_index: 90,
        production_regions: ['South America', 'Central America', 'Africa', 'Asia'],
        top_exporters: ['Brazil', 'Vietnam', 'Colombia', 'Indonesia', 'Ethiopia'],
        top_importers: ['European Union', 'United States', 'Japan', 'Canada', 'Russia'],
        seasonality: [0.98, 0.97, 0.99, 1.01, 1.03, 1.05, 1.04, 1.02, 1.0, 0.99, 0.98, 0.97]
      }
    };

    // Return data for the requested product or default to Wheat
    const data = productData[productType] || productData['Wheat'];

    return {
      data: {
        status: 'success',
        ...data
      }
    };
  }

  // Get market analysis for a specific country
  async getCountryAnalysis(country) {
    if (USE_MOCK_DATA) {
      return MockMarketService.getCountryAnalysis(country)
    }

    try {
      const response = await api.get(`${API_URL}/market-analysis/country/${country}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching country analysis for ${country}:`, error)
      // Fallback to mock data if API fails
      return MockMarketService.getCountryAnalysis(country)
    }
  }

  // Get price forecast for a specific product type
  async getProductForecast(productType) {
    if (USE_MOCK_DATA) {
      return MockMarketService.getProductForecasts(productType)
    }

    try {
      // Check if AI API is available
      console.log(`Attempting to fetch forecast for ${productType} from AI API...`)

      // Use AI API for forecasts
      const response = await aiApi.get(`/forecast/${productType}`)
      console.log(`Successfully fetched forecast for ${productType}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching product forecast for ${productType}:`, error)
      console.log(`Falling back to mock data for ${productType}`)

      // Fallback to mock data if API fails
      const mockResponse = await MockMarketService.getProductForecasts(productType)
      return mockResponse
    }
  }

  // Get detailed price forecast for a specific product type
  async getPriceForecast(productType, timeFrame = '1_year') {
    if (USE_MOCK_DATA) {
      return MockMarketService.getPriceForecast(productType, timeFrame)
    }

    try {
      console.log(`Attempting to fetch price forecast for ${productType} with timeframe ${timeFrame}...`)

      // Use AI API for price forecasts
      const response = await aiApi.get(`/price-forecast/${productType}`, {
        params: { time_frame: timeFrame }
      })
      console.log(`Successfully fetched price forecast for ${productType}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching price forecast for ${productType}:`, error)
      console.log(`Falling back to mock data for price forecast`)

      // Fallback to mock data if API fails
      const mockResponse = await MockMarketService.getPriceForecast(productType, timeFrame)
      return mockResponse
    }
  }

  // Get historical prices for a specific product type
  async getHistoricalPrices(productType) {
    if (USE_MOCK_DATA) {
      return MockMarketService.getHistoricalPrices(productType)
    }

    try {
      console.log(`Attempting to fetch historical prices for ${productType}...`)

      // Use AI API for historical prices
      const response = await aiApi.get(`/historical-prices/${productType}`)
      console.log(`Successfully fetched historical prices for ${productType}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching historical prices for ${productType}:`, error)
      console.log(`Falling back to mock data for historical prices`)

      // Fallback to mock data if API fails
      const mockResponse = await MockMarketService.getHistoricalPrices(productType)
      return mockResponse
    }
  }

  // Get AI recommendation for buying or selling a product
  async getRecommendation(data) {
    if (USE_MOCK_DATA) {
      return MockMarketService.getRecommendations(data?.product_type || null)
    }

    try {
      console.log(`Attempting to fetch AI recommendation for ${data?.product_type || 'all products'}...`)

      // Use AI API for recommendations
      const response = await aiApi.post(`/recommendation`, data)
      console.log(`Successfully fetched AI recommendation`)
      return response.data
    } catch (error) {
      console.error('Error fetching AI recommendation:', error)
      console.log(`Falling back to mock data for recommendations`)

      // Fallback to mock data if API fails
      const mockResponse = await MockMarketService.getRecommendations(data?.product_type || null)
      return mockResponse
    }
  }

  // Get public product categories data
  async getPublicProducts() {
    if (USE_MOCK_DATA) {
      return MockMarketService.getPublicProducts ?
        MockMarketService.getPublicProducts() :
        { data: { status: 'success', data: [] } }
    }

    try {
      const response = await api.get(`${API_URL}/market-data/products`)
      return response.data
    } catch (error) {
      console.error('Error fetching public products:', error)
      // Fallback to mock data if API fails
      return MockMarketService.getPublicProducts ?
        MockMarketService.getPublicProducts() :
        { data: { status: 'success', data: [] } }
    }
  }

  // Get public market trends data
  async getPublicTrends() {
    if (USE_MOCK_DATA) {
      return MockMarketService.getPublicTrends()
    }

    try {
      const response = await api.get(`${API_URL}/market-data/trends`)
      return response.data
    } catch (error) {
      console.error('Error fetching public trends:', error)
      // Fallback to mock data if API fails
      return MockMarketService.getPublicTrends()
    }
  }

  // Get seasonal analysis for a specific product type and region
  async getSeasonalAnalysis(productType, region = 'Global') {
    if (USE_MOCK_DATA) {
      return MockMarketService.getSeasonalAnalysis(productType, region)
    }

    try {
      console.log(`Attempting to fetch seasonal analysis for ${productType} in ${region}...`)

      // Use AI API for seasonal analysis
      const response = await aiApi.get(`/seasonal-analysis/${productType}`, {
        params: { region }
      })
      console.log(`Successfully fetched seasonal analysis for ${productType}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching seasonal analysis for ${productType}:`, error)
      console.log(`Falling back to mock data for seasonal analysis`)

      // Fallback to mock data if API fails
      return MockMarketService.getSeasonalAnalysis(productType, region)
    }
  }

  // Get missing products analysis
  async getMissingProducts() {
    if (USE_MOCK_DATA) {
      return this.getMockMissingProducts()
    }

    try {
      console.log('Attempting to fetch missing products analysis...')

      // Use AI API for missing products analysis
      const response = await aiApi.get('/missing-products')
      console.log('Successfully fetched missing products analysis')
      return response.data
    } catch (error) {
      console.error('Error fetching missing products analysis:', error)
      console.log('Falling back to mock data for missing products')

      // Fallback to mock data if API fails
      return this.getMockMissingProducts()
    }
  }

  // Get smart pricing recommendations
  async getSmartPricing(productType, quality, quantity) {
    if (USE_MOCK_DATA) {
      return this.getMockSmartPricing(productType, quality, quantity)
    }

    try {
      console.log(`Attempting to fetch smart pricing for ${productType} (${quality}, ${quantity})...`)

      // Use AI API for smart pricing
      const response = await aiApi.get('/smart-pricing', {
        params: {
          product_type: productType,
          quality: quality,
          quantity: quantity
        }
      })
      console.log(`Successfully fetched smart pricing for ${productType}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching smart pricing for ${productType}:`, error)
      console.log('Falling back to mock data for smart pricing')

      // Fallback to mock data if API fails
      return this.getMockSmartPricing(productType, quality, quantity)
    }
  }

  // Mock data for missing products
  getMockMissingProducts() {
    return {
      data: {
        status: 'success',
        products: [
          {
            id: 1,
            name: 'Organic Wheat',
            region: 'Middle East',
            demand_level: 'High',
            price_trend: 'Increasing',
            opportunity_score: 92,
            supply_gap: 35,
            top_importers: ['Saudi Arabia', 'UAE', 'Egypt'],
            estimated_market_size: 450000000
          },
          {
            id: 2,
            name: 'Premium Rice',
            region: 'Asia',
            demand_level: 'High',
            price_trend: 'Stable',
            opportunity_score: 88,
            supply_gap: 28,
            top_importers: ['China', 'Indonesia', 'Philippines'],
            estimated_market_size: 380000000
          },
          {
            id: 3,
            name: 'Organic Soybeans',
            region: 'Europe',
            demand_level: 'Medium',
            price_trend: 'Increasing',
            opportunity_score: 85,
            supply_gap: 22,
            top_importers: ['Germany', 'France', 'UK'],
            estimated_market_size: 290000000
          },
          {
            id: 4,
            name: 'Specialty Coffee',
            region: 'North America',
            demand_level: 'High',
            price_trend: 'Increasing',
            opportunity_score: 90,
            supply_gap: 30,
            top_importers: ['USA', 'Canada', 'Mexico'],
            estimated_market_size: 420000000
          },
          {
            id: 5,
            name: 'Quinoa',
            region: 'Europe',
            demand_level: 'Medium',
            price_trend: 'Increasing',
            opportunity_score: 82,
            supply_gap: 25,
            top_importers: ['Germany', 'UK', 'France'],
            estimated_market_size: 180000000
          },
          {
            id: 6,
            name: 'Organic Corn',
            region: 'North America',
            demand_level: 'Medium',
            price_trend: 'Stable',
            opportunity_score: 78,
            supply_gap: 18,
            top_importers: ['USA', 'Canada', 'Mexico'],
            estimated_market_size: 250000000
          },
          {
            id: 7,
            name: 'Basmati Rice',
            region: 'Middle East',
            demand_level: 'High',
            price_trend: 'Increasing',
            opportunity_score: 87,
            supply_gap: 32,
            top_importers: ['Saudi Arabia', 'UAE', 'Iran'],
            estimated_market_size: 320000000
          },
          {
            id: 8,
            name: 'Durum Wheat',
            region: 'Europe',
            demand_level: 'Medium',
            price_trend: 'Stable',
            opportunity_score: 80,
            supply_gap: 20,
            top_importers: ['Italy', 'France', 'Spain'],
            estimated_market_size: 280000000
          },
          {
            id: 9,
            name: 'Organic Fruits',
            region: 'North America',
            demand_level: 'High',
            price_trend: 'Increasing',
            opportunity_score: 89,
            supply_gap: 27,
            top_importers: ['USA', 'Canada', 'UK'],
            estimated_market_size: 350000000
          },
          {
            id: 10,
            name: 'Cashews',
            region: 'Asia',
            demand_level: 'Medium',
            price_trend: 'Stable',
            opportunity_score: 75,
            supply_gap: 20,
            top_importers: ['India', 'Vietnam', 'China'],
            estimated_market_size: 220000000
          }
        ]
      }
    }
  }

  // Mock data for smart pricing
  getMockSmartPricing(productType, quality, quantity) {
    // Base prices for different products
    const basePrices = {
      'Wheat': 320,
      'Rice': 380,
      'Corn': 220,
      'Soybeans': 520,
      'Coffee': 180
    }

    // Quality multipliers
    const qualityMultipliers = {
      'Standard': 1.0,
      'Premium': 1.3,
      'Organic': 1.6
    }

    // Quantity discounts (percentage)
    const quantityDiscounts = {
      100: 0,
      500: 2,
      1000: 5,
      5000: 8,
      10000: 12
    }

    // Calculate base price for the product
    const basePrice = basePrices[productType] || 300

    // Apply quality multiplier
    const qualityMultiplier = qualityMultipliers[quality] || 1.0
    const qualityAdjustedPrice = basePrice * qualityMultiplier

    // Apply quantity discount
    const discount = quantityDiscounts[quantity] || 0
    const discountMultiplier = (100 - discount) / 100
    const finalPrice = qualityAdjustedPrice * discountMultiplier

    // Calculate min and max prices (±10% from recommended)
    const minPrice = finalPrice * 0.9
    const maxPrice = finalPrice * 1.1

    // Calculate market average (slightly below recommended for premium/organic, slightly above for standard)
    const marketAverage = quality === 'Standard'
      ? finalPrice * 1.05
      : finalPrice * 0.95

    // Generate competitor prices
    const competitorPrices = [
      { name: 'Competitor A', price: finalPrice * (0.9 + Math.random() * 0.1) },
      { name: 'Competitor B', price: finalPrice * (0.95 + Math.random() * 0.1) },
      { name: 'Competitor C', price: finalPrice * (1.0 + Math.random() * 0.1) },
      { name: 'Competitor D', price: finalPrice * (1.05 + Math.random() * 0.1) },
      { name: 'Competitor E', price: finalPrice * (1.1 + Math.random() * 0.1) }
    ]

    return {
      data: {
        status: 'success',
        product_type: productType,
        quality: quality,
        quantity: quantity,
        recommended_price: finalPrice,
        min_price: minPrice,
        max_price: maxPrice,
        market_average: marketAverage,
        price_trend: Math.random() > 0.5 ? 'Increasing' : 'Decreasing',
        profit_margin: Math.floor(15 + Math.random() * 15),
        competitor_prices: competitorPrices.map(c => ({ competitor: c.name, price: c.price })),
        pricing_factors: [
          { factor: 'Market Demand', impact: 'High', direction: 'Positive' },
          { factor: 'Supply Availability', impact: 'Medium', direction: 'Negative' },
          { factor: 'Seasonal Trends', impact: 'Medium', direction: 'Positive' },
          { factor: 'Quality Premium', impact: 'High', direction: 'Positive' },
          { factor: 'Bulk Discount', impact: 'Medium', direction: 'Negative' }
        ],
        pricing_insights: [
          'Premium quality commands a 15-20% price premium in current market',
          'Supply is slightly constrained, supporting higher prices',
          'Current season is favorable for higher pricing',
          'Large quantity offers opportunity for volume-based pricing strategy',
          'Market trend is upward, suggesting potential for price increases in coming weeks'
        ]
      }
    }
  }
}

export default new MarketService()
