import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import {
  GlobeAmericasIcon,
  MapIcon,
  BuildingLibraryIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  ShoppingCartIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ScaleIcon
} from '@heroicons/react/24/outline';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, RadarController, RadialLinearScale, Filler } from 'chart.js';
import { Bar, Radar } from 'react-chartjs-2';
import CountryAnalysisMap from '../../components/ai/CountryAnalysisMap';
import MarketService from '../../services/MarketService';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadarController,
  RadialLinearScale,
  Filler
);

const CountryAnalysis = () => {
  const { t } = useTranslation();
  const { countryName } = useParams();
  const [selectedCountry, setSelectedCountry] = useState(countryName || 'Egypt');
  const [selectedProduct, setSelectedProduct] = useState('Wheat');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [countryData, setCountryData] = useState(null);
  const [radarData, setRadarData] = useState({
    labels: [],
    datasets: []
  });
  const [importExportData, setImportExportData] = useState({
    labels: [],
    datasets: []
  });

  const countries = ['Egypt', 'Saudi Arabia', 'UAE', 'Kuwait', 'Jordan', 'Lebanon', 'Morocco', 'Tunisia'];

  // Update selected country when URL parameter changes
  useEffect(() => {
    if (countryName) {
      setSelectedCountry(countryName);
    }
  }, [countryName]);
  const products = ['Wheat', 'Rice', 'Corn', 'Soybeans', 'Coffee'];

  // Fetch country data when country or product changes
  useEffect(() => {
    const fetchCountryData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch country data from AI service
        // In a real implementation, this would be an actual API call
        // const response = await MarketService.getCountryAnalysis(selectedCountry, selectedProduct);

        // For now, we'll simulate a response with mock data
        setTimeout(() => {
          // Mock data for demonstration
          const mockCountryData = {
            name: selectedCountry,
            region: getRegionForCountry(selectedCountry),
            market_size: getMarketSizeForCountry(selectedCountry),
            demand_index: getDemandIndexForCountry(selectedCountry),
            political_risk: getPoliticalRiskForCountry(selectedCountry),
            economic_stability: getEconomicStabilityForCountry(selectedCountry),
            trade_barriers: getTradeBarriersForCountry(selectedCountry),
            top_products: getTopProductsForCountry(selectedCountry),
            import_data: getImportDataForCountry(selectedCountry),
            export_data: getExportDataForCountry(selectedCountry),
            risk_factors: {
              political_stability: getRandomValue(50, 90),
              economic_growth: getRandomValue(40, 85),
              infrastructure: getRandomValue(30, 80),
              climate_vulnerability: getRandomValue(40, 90),
              trade_relations: getRandomValue(50, 95),
              regulatory_environment: getRandomValue(45, 85)
            },
            market_trends: [
              `${selectedProduct} demand in ${selectedCountry} is ${getDemandTrend()} due to ${getDemandReason(selectedCountry)}.`,
              `Import regulations for agricultural products in ${selectedCountry} have ${getRegulationTrend()} in the past year.`,
              `Local production of ${selectedProduct} in ${selectedCountry} is ${getProductionTrend()}.`,
              `Consumer preferences in ${selectedCountry} are shifting towards ${getConsumerPreference()}.`
            ],
            trade_recommendations: [
              `Consider ${getTradeRecommendation(selectedCountry, selectedProduct)}`,
              `${getTimingRecommendation(selectedCountry)}`,
              `${getPartnershipRecommendation(selectedCountry)}`,
              `${getLogisticsRecommendation(selectedCountry)}`
            ]
          };

          setCountryData(mockCountryData);

          // Set radar chart data
          setRadarData({
            labels: [
              t('ai.politicalStability'),
              t('ai.economicGrowth'),
              t('ai.infrastructure'),
              t('ai.climateVulnerability'),
              t('ai.tradeRelations'),
              t('ai.regulatoryEnvironment')
            ],
            datasets: [
              {
                label: selectedCountry,
                data: [
                  mockCountryData.risk_factors.political_stability,
                  mockCountryData.risk_factors.economic_growth,
                  mockCountryData.risk_factors.infrastructure,
                  mockCountryData.risk_factors.climate_vulnerability,
                  mockCountryData.risk_factors.trade_relations,
                  mockCountryData.risk_factors.regulatory_environment
                ],
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgb(75, 192, 192)',
                pointBackgroundColor: 'rgb(75, 192, 192)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgb(75, 192, 192)'
              }
            ]
          });

          // Set import/export data
          setImportExportData({
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [
              {
                label: t('ai.imports'),
                data: mockCountryData.import_data,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgb(54, 162, 235)',
                borderWidth: 1
              },
              {
                label: t('ai.exports'),
                data: mockCountryData.export_data,
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                borderColor: 'rgb(255, 99, 132)',
                borderWidth: 1
              }
            ]
          });

          setIsLoading(false);
        }, 1000);

      } catch (error) {
        console.error('Error fetching country data:', error);
        setError(t('ai.fetchError'));
        setIsLoading(false);
      }
    };

    fetchCountryData();
  }, [selectedCountry, selectedProduct, t]);

  // Helper functions for generating mock data
  function getRegionForCountry(country) {
    const regions = {
      'Egypt': 'North Africa',
      'Morocco': 'North Africa',
      'Tunisia': 'North Africa',
      'Saudi Arabia': 'Middle East',
      'UAE': 'Middle East',
      'Kuwait': 'Middle East',
      'Jordan': 'Middle East',
      'Lebanon': 'Middle East'
    };
    return regions[country] || 'Unknown';
  }

  function getMarketSizeForCountry(country) {
    const marketSizes = {
      'Egypt': '$4.2B',
      'Saudi Arabia': '$3.8B',
      'UAE': '$2.9B',
      'Kuwait': '$1.5B',
      'Jordan': '$1.2B',
      'Lebanon': '$0.9B',
      'Morocco': '$1.8B',
      'Tunisia': '$1.1B'
    };
    return marketSizes[country] || '$1.0B';
  }

  function getDemandIndexForCountry(country) {
    const demandIndices = {
      'Egypt': 85,
      'Saudi Arabia': 78,
      'UAE': 92,
      'Kuwait': 65,
      'Jordan': 72,
      'Lebanon': 68,
      'Morocco': 75,
      'Tunisia': 70
    };
    return demandIndices[country] || 70;
  }

  function getPoliticalRiskForCountry(country) {
    const politicalRisks = {
      'Egypt': 'medium',
      'Saudi Arabia': 'low',
      'UAE': 'low',
      'Kuwait': 'low',
      'Jordan': 'medium',
      'Lebanon': 'high',
      'Morocco': 'medium',
      'Tunisia': 'medium'
    };
    return politicalRisks[country] || 'medium';
  }

  function getEconomicStabilityForCountry(country) {
    const economicStabilities = {
      'Egypt': 'moderately stable',
      'Saudi Arabia': 'very stable',
      'UAE': 'very stable',
      'Kuwait': 'stable',
      'Jordan': 'moderately stable',
      'Lebanon': 'unstable',
      'Morocco': 'stable',
      'Tunisia': 'moderately stable'
    };
    return economicStabilities[country] || 'moderately stable';
  }

  function getTradeBarriersForCountry(country) {
    const tradeBarriers = {
      'Egypt': 'medium',
      'Saudi Arabia': 'low',
      'UAE': 'very low',
      'Kuwait': 'medium',
      'Jordan': 'low',
      'Lebanon': 'medium',
      'Morocco': 'low',
      'Tunisia': 'medium'
    };
    return tradeBarriers[country] || 'medium';
  }

  function getTopProductsForCountry(country) {
    const topProducts = {
      'Egypt': ['Wheat', 'Rice', 'Cotton', 'Citrus'],
      'Saudi Arabia': ['Dates', 'Wheat', 'Fruits', 'Vegetables'],
      'UAE': ['Fruits', 'Vegetables', 'Dairy', 'Poultry'],
      'Kuwait': ['Rice', 'Wheat', 'Vegetables', 'Meat'],
      'Jordan': ['Wheat', 'Fruits', 'Vegetables', 'Olives'],
      'Lebanon': ['Fruits', 'Vegetables', 'Olives', 'Wheat'],
      'Morocco': ['Wheat', 'Citrus', 'Olives', 'Vegetables'],
      'Tunisia': ['Olives', 'Wheat', 'Dates', 'Citrus']
    };
    return topProducts[country] || ['Wheat', 'Rice', 'Corn'];
  }

  function getImportDataForCountry() {
    // Generate random import data for 12 months
    return Array.from({ length: 12 }, () => getRandomValue(100, 500));
  }

  function getExportDataForCountry() {
    // Generate random export data for 12 months
    return Array.from({ length: 12 }, () => getRandomValue(50, 300));
  }

  function getRandomValue(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  function getDemandTrend() {
    const trends = ['increasing', 'stable', 'slightly increasing', 'rapidly growing'];
    return trends[Math.floor(Math.random() * trends.length)];
  }

  function getDemandReason(country) {
    const reasons = [
      'population growth',
      'changing dietary preferences',
      'economic development',
      'urbanization',
      'government subsidies',
      'reduced local production'
    ];
    return reasons[Math.floor(Math.random() * reasons.length)];
  }

  function getRegulationTrend() {
    const trends = ['become more stringent', 'been simplified', 'remained stable', 'been reformed'];
    return trends[Math.floor(Math.random() * trends.length)];
  }

  function getProductionTrend() {
    const trends = ['increasing', 'decreasing', 'stable', 'facing challenges due to climate conditions'];
    return trends[Math.floor(Math.random() * trends.length)];
  }

  function getConsumerPreference() {
    const preferences = [
      'organic products',
      'locally produced goods',
      'premium quality imports',
      'value-priced staples',
      'convenience foods'
    ];
    return preferences[Math.floor(Math.random() * preferences.length)];
  }

  function getTradeRecommendation(country, product) {
    const recommendations = [
      `establishing direct trade relationships with ${country} for ${product}`,
      `focusing on premium quality ${product} for the ${country} market`,
      `partnering with local distributors in ${country} for better market access`,
      `exploring organic ${product} opportunities in ${country}`,
      `targeting urban markets in ${country} for higher-value ${product}`
    ];
    return recommendations[Math.floor(Math.random() * recommendations.length)];
  }

  function getTimingRecommendation(country) {
    const recommendations = [
      `Best import timing for ${country} is during Q2 and Q3 when local supplies are lower`,
      `Consider seasonal demand patterns in ${country}, with peaks during holiday seasons`,
      `Plan shipments to arrive before Ramadan when demand increases significantly`,
      `Winter months see higher demand for imported produce in ${country}`
    ];
    return recommendations[Math.floor(Math.random() * recommendations.length)];
  }

  function getPartnershipRecommendation(country) {
    const recommendations = [
      `Seek partnerships with established importers in ${country} with strong distribution networks`,
      `Consider joint ventures with local agricultural companies in ${country}`,
      `Work with trade promotion agencies specializing in ${country} market entry`,
      `Engage with ${country}'s chamber of commerce for business matchmaking services`
    ];
    return recommendations[Math.floor(Math.random() * recommendations.length)];
  }

  function getLogisticsRecommendation(country) {
    const recommendations = [
      `Utilize sea freight for bulk shipments to ${country} to optimize costs`,
      `Consider air freight for high-value perishables entering ${country}`,
      `Establish warehousing solutions near ${country}'s major ports for just-in-time delivery`,
      `Partner with logistics providers experienced in agricultural imports to ${country}`
    ];
    return recommendations[Math.floor(Math.random() * recommendations.length)];
  }

  // Get risk level class
  const getRiskLevelClass = (risk) => {
    if (!risk) return 'text-gray-500';

    switch (risk.toLowerCase()) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      case 'very low':
        return 'text-green-700';
      default:
        return 'text-gray-500';
    }
  };

  // Get demand index class
  const getDemandIndexClass = (index) => {
    if (index >= 80) return 'bg-green-600';
    if (index >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">
        {t('ai.countryAnalysis')}
      </h1>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('ai.selectCountry')}
            </label>
            <select
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              value={selectedCountry}
              onChange={(e) => setSelectedCountry(e.target.value)}
            >
              {countries.map((country) => (
                <option key={country} value={country}>
                  {country}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('ai.selectProduct')}
            </label>
            <select
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              value={selectedProduct}
              onChange={(e) => setSelectedProduct(e.target.value)}
            >
              {products.map((product) => (
                <option key={product} value={product}>
                  {product}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Loading and Error States */}
      {isLoading && (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {!isLoading && !error && countryData && (
        <>
          {/* Country Overview */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div className="mb-4 md:mb-0">
                <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                  <GlobeAmericasIcon className="h-6 w-6 text-green-600 mr-2" />
                  {countryData.name}
                </h2>
                <p className="text-gray-500">{countryData.region}</p>
              </div>

              <div className="flex flex-col md:items-end">
                <div className="text-sm text-gray-500 mb-1">{t('ai.marketSize')}</div>
                <div className="text-lg font-semibold text-gray-800">{countryData.market_size}</div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">{t('ai.demandIndex')}</h3>
                <div className="flex items-center">
                  <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                    <div
                      className={`h-2 rounded-full ${getDemandIndexClass(countryData.demand_index)}`}
                      style={{ width: `${countryData.demand_index}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{countryData.demand_index}/100</span>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">{t('ai.politicalRisk')}</h3>
                <p className={`text-sm font-medium ${getRiskLevelClass(countryData.political_risk)}`}>
                  {countryData.political_risk.toUpperCase()}
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">{t('ai.tradeBarriers')}</h3>
                <p className={`text-sm font-medium ${getRiskLevelClass(countryData.trade_barriers)}`}>
                  {countryData.trade_barriers.toUpperCase()}
                </p>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Risk Assessment Radar Chart */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">{t('ai.countryRiskAssessment')}</h2>
              <div className="h-80">
                <Radar
                  data={radarData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                          stepSize: 20
                        }
                      }
                    },
                    plugins: {
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            return `${context.dataset.label}: ${context.raw}/100`;
                          }
                        }
                      }
                    }
                  }}
                />
              </div>
            </div>

            {/* Import/Export Chart */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">{t('ai.importExportTrends')}</h2>
              <div className="h-80">
                <Bar
                  data={importExportData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            return `${context.dataset.label}: $${context.raw}K`;
                          }
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        title: {
                          display: true,
                          text: t('ai.valueInThousandUSD')
                        }
                      }
                    }
                  }}
                />
              </div>
            </div>
          </div>

          {/* Market Trends and Recommendations */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Market Trends */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center">
                <ChartBarIcon className="h-5 w-5 text-blue-600 mr-2" />
                {t('ai.marketTrends')}
              </h2>
              <div className="space-y-4">
                {countryData.market_trends.map((trend, index) => (
                  <div key={index} className="p-3 bg-blue-50 border-l-4 border-blue-500 rounded-r-md">
                    <p className="text-gray-700">{trend}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Trade Recommendations */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center">
                <LightBulbIcon className="h-5 w-5 text-yellow-600 mr-2" />
                {t('ai.tradeRecommendations')}
              </h2>
              <div className="space-y-4">
                {countryData.trade_recommendations.map((recommendation, index) => (
                  <div key={index} className="p-3 bg-yellow-50 border-l-4 border-yellow-500 rounded-r-md">
                    <p className="text-gray-700">{recommendation}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Top Products and Economic Stability */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Top Products */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center">
                <ShoppingCartIcon className="h-5 w-5 text-green-600 mr-2" />
                {t('ai.topProducts')}
              </h2>
              <div className="grid grid-cols-2 gap-4">
                {countryData.top_products.map((product, index) => (
                  <div key={index} className="bg-green-50 rounded-lg p-4 flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-3"></div>
                    <span className="text-gray-800 font-medium">{product}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Economic Stability */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center">
                <BuildingLibraryIcon className="h-5 w-5 text-purple-600 mr-2" />
                {t('ai.economicOverview')}
              </h2>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-700">{t('ai.economicStability')}:</span>
                  <span className="font-medium">{countryData.economic_stability}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-700">{t('ai.currencyStability')}:</span>
                  <span className="font-medium">{countryData.economic_stability === 'very stable' ? t('ai.high') :
                    countryData.economic_stability === 'stable' ? t('ai.moderate') :
                    countryData.economic_stability === 'moderately stable' ? t('ai.moderate') : t('ai.low')}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-700">{t('ai.paymentRisk')}:</span>
                  <span className="font-medium">{countryData.economic_stability === 'very stable' ? t('ai.low') :
                    countryData.economic_stability === 'stable' ? t('ai.moderate') :
                    countryData.economic_stability === 'moderately stable' ? t('ai.moderate') : t('ai.high')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Interactive Map Component */}
          <CountryAnalysisMap />
        </>
      )}
    </div>
  );
};

// Helper component for the LightBulbIcon
const LightBulbIcon = ({ className }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
    </svg>
  );
};

export default CountryAnalysis;
