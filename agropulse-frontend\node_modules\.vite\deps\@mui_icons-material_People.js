"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-YKCWYOVU.js";
import "./chunk-HE35I2H7.js";
import "./chunk-6EVIHVLY.js";
import {
  require_jsx_runtime
} from "./chunk-TOXUORXJ.js";
import "./chunk-K5YNGTCN.js";
import {
  __toESM
} from "./chunk-ZC22LKFR.js";

// node_modules/@mui/icons-material/esm/People.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var People_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3m-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3m0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5m8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5"
}), "People");
export {
  People_default as default
};
//# sourceMappingURL=@mui_icons-material_People.js.map
